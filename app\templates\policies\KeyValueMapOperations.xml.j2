<KeyValueMapOperations name="{{ name }}">
  {% if params.map_name %}<MapName>{{ params.map_name }}</MapName>{% endif %}
  {% if params.scope %}<Scope>{{ params.scope }}</Scope>{% endif %} <!-- organization | environment | apiproxy | proxyendpoint -->
  {% if params.get %}
  <Get assignTo="{{ params.get.assign_to }}">
    {% if params.get.key %}<Key>{{ params.get.key }}</Key>{% endif %}
    {% if params.get.value_prefix %}<ValuePrefix>{{ params.get.value_prefix }}</ValuePrefix>{% endif %}
  </Get>
  {% endif %}
  {% if params.put %}
  <Put>
    <Entries>
      {% for e in params.put.entries %}
      <Entry>
        <Key>{{ e.key }}</Key>
        <Value>{{ e.value }}</Value>
      </Entry>
      {% endfor %}
    </Entries>
  </Put>
  {% endif %}
  {% if params.delete %}
  <Delete>
    {% for k in params.delete.keys %}
    <Key>{{ k }}</Key>
    {% endfor %}
  </Delete>
  {% endif %}
</KeyValueMapOperations>