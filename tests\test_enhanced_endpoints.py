#!/usr/bin/env python3
"""
Test enhanced endpoint configurations
"""

import sys
import os
import yaml
from pathlib import Path

# Add the app directory to the Python path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from app.services.policy_builder import Policy<PERSON><PERSON>er


def test_enhanced_target_endpoints():
    """Test that multiple target endpoints with PreFlow and PostFlow work correctly"""
    print("Testing Enhanced Target Endpoints...")
    
    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"
    
    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)
    
    # Check targets configuration
    targets = system_cfg.get("targets", {})
    
    print(f"✅ Found {len(targets)} target endpoints:")
    for target_name, target_config in targets.items():
        print(f"   - {target_name}: {target_config.get('baseurl')}")
        
        # Check flows
        flows = target_config.get("flows", {})
        preflow = flows.get("preflow", {})
        postflow = flows.get("postflow", {})
        
        preflow_req = preflow.get("request", [])
        preflow_resp = preflow.get("response", [])
        postflow_req = postflow.get("request", [])
        postflow_resp = postflow.get("response", [])
        
        print(f"     PreFlow Request: {preflow_req}")
        print(f"     PreFlow Response: {preflow_resp}")
        print(f"     PostFlow Request: {postflow_req}")
        print(f"     PostFlow Response: {postflow_resp}")
    
    # Verify specific targets exist
    expected_targets = ["default", "default-jo"]
    for expected in expected_targets:
        if expected in targets:
            print(f"✅ {expected} target endpoint is properly configured")
        else:
            print(f"❌ {expected} target endpoint is missing")
    
    return len(targets) >= 2


def test_enhanced_proxy_flows():
    """Test that proxy endpoint flows include PostClientFlow and noMatchFound"""
    print("\nTesting Enhanced Proxy Flows...")
    
    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"
    
    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)
    
    # Check proxy flows configuration
    flows = system_cfg.get("flows", {})
    proxy_flows = flows.get("proxy", {})
    
    print("✅ Proxy Flow Configuration:")
    
    # Check all flow types
    flow_types = ["preflow", "per_operation", "postflow", "postclientflow", "nomatchfound"]
    for flow_type in flow_types:
        flow_config = proxy_flows.get(flow_type, {})
        if isinstance(flow_config, dict):
            request_policies = flow_config.get("request", [])
            response_policies = flow_config.get("response", [])
            print(f"   {flow_type.upper()}:")
            print(f"     Request: {request_policies}")
            print(f"     Response: {response_policies}")
        else:
            print(f"   {flow_type.upper()}: {flow_config}")
    
    # Verify specific flows exist
    expected_flows = ["preflow", "per_operation", "postflow", "postclientflow", "nomatchfound"]
    missing_flows = []
    for expected in expected_flows:
        if expected not in proxy_flows:
            missing_flows.append(expected)
    
    if not missing_flows:
        print("✅ All expected proxy flows are configured")
        return True
    else:
        print(f"❌ Missing proxy flows: {missing_flows}")
        return False


def test_policy_rendering_with_new_structure():
    """Test that policy rendering works with the new flow structure"""
    print("\nTesting Policy Rendering with New Structure...")

    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"

    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)

    # Initialize PolicyBuilder
    builder = PolicyBuilder()

    # Test input
    input_vars = {
        "policy_params": {}
    }

    try:
        # Render policies
        policies, attach_plan = builder.render_policies(
            system_cfg=system_cfg,
            input_vars=input_vars,
            system_name="REFLECT_FDID"
        )

        print(f"✅ Successfully rendered {len(policies)} policies")

        # Check that target-specific policies are included
        target_policies = [p for p in policies if "Target" in p.name]
        print(f"✅ Found {len(target_policies)} target-related policies:")
        for policy in target_policies:
            print(f"   - {policy.name}")

        # Check that PostClientFlow policies are included
        postclient_policies = [p for p in policies if p.name == "FC-PushLogsToPubSub"]
        if postclient_policies:
            print("✅ PostClientFlow policies are properly configured")
        else:
            print("❌ PostClientFlow policies are missing")

        # Check that NoMatchFound policies are included
        nomatch_policies = [p for p in policies if p.name == "RF-ResourceNotFound"]
        if nomatch_policies:
            print("✅ NoMatchFound policies are properly configured")
        else:
            print("❌ NoMatchFound policies are missing")

        return True

    except Exception as e:
        print(f"❌ Error rendering policies: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_target_builder_with_new_structure():
    """Test that target builder works with the new targets structure"""
    print("\nTesting Target Builder with New Structure...")

    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"

    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)

    try:
        from app.services.target_builder import TargetBuilder

        # Initialize TargetBuilder
        builder = TargetBuilder()

        # Test building default target
        default_xml = builder.build_target_endpoint_xml(
            system_cfg=system_cfg,
            endpoint_name="default"
        )

        print("✅ Successfully built default target endpoint XML")
        print(f"   XML length: {len(default_xml)} characters")

        # Check that XML contains expected elements
        if "<TargetEndpoint" in default_xml and "https://payments.internal.example.com" in default_xml:
            print("✅ Default target XML contains expected URL")
        else:
            print("❌ Default target XML missing expected content")

        # Test building default-jo target
        jo_xml = builder.build_target_endpoint_xml(
            system_cfg=system_cfg,
            endpoint_name="default-jo"
        )

        print("✅ Successfully built default-jo target endpoint XML")
        print(f"   XML length: {len(jo_xml)} characters")

        # Check that XML contains expected elements
        if "<TargetEndpoint" in jo_xml and "https://payments-jo.internal.example.com" in jo_xml:
            print("✅ Default-jo target XML contains expected URL")
        else:
            print("❌ Default-jo target XML missing expected content")

        # Check for PreFlow and PostFlow elements
        if "<PreFlow" in default_xml:
            print("✅ Default target XML includes PreFlow")
        else:
            print("❌ Default target XML missing PreFlow")

        if "<PostFlow" in default_xml:
            print("✅ Default target XML includes PostFlow")
        else:
            print("❌ Default target XML missing PostFlow")

        return True

    except Exception as e:
        print(f"❌ Error building target endpoints: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("TESTING ENHANCED ENDPOINT CONFIGURATIONS")
    print("=" * 60)
    
    success1 = test_enhanced_target_endpoints()
    success2 = test_enhanced_proxy_flows()
    success3 = test_policy_rendering_with_new_structure()
    success4 = test_target_builder_with_new_structure()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3 and success4:
        print("✅ ALL TESTS PASSED!")
        print("Enhanced endpoint configurations are working correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the implementation.")
    print("=" * 60)
