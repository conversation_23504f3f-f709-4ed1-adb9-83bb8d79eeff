<JSONThreatProtection name="{{ name }}">
  {% if params.get('display_name') %}<DisplayName>{{ params.display_name }}</DisplayName>{% endif %}
  {% if params.get('source') %}<Source>{{ params.source }}</Source>{% endif %}

  {# Map old parameter names to new ones for backward compatibility #}
  {% set array_count = params.get('array_element_count') or params.get('max_array_elements') %}
  {% set depth = params.get('container_depth') or params.get('max_container_depth') %}
  {% set object_count = params.get('object_entry_count') or params.get('max_object_members') %}
  {% set object_name_length = params.get('object_entry_name_length') %}
  {% set string_length = params.get('string_value_length') or params.get('max_string_length') %}

  {% if array_count is not none %}<ArrayElementCount>{{ array_count }}</ArrayElementCount>{% endif %}
  {% if depth is not none %}<ContainerDepth>{{ depth }}</ContainerDepth>{% endif %}
  {% if object_count is not none %}<ObjectEntryCount>{{ object_count }}</ObjectEntryCount>{% endif %}
  {% if object_name_length is not none %}<ObjectEntryNameLength>{{ object_name_length }}</ObjectEntryNameLength>{% endif %}
  {% if string_length is not none %}<StringValueLength>{{ string_length }}</StringValueLength>{% endif %}
</JSONThreatProtection>