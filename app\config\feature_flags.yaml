# Global Feature Flags Configuration
# These flags apply across all environments and systems unless overridden

# Basic feature flags (simple boolean)
advanced_security: false
debug_logging: false
experimental_features: false

# Complex feature flags with conditions and rollout control
smart_caching:
  enabled: true
  environments:
    dev: true
    test: true
    staging: true
    prod: false  # Not ready for production yet
  percentage: 50  # 50% rollout
  conditions:
    - type: api_characteristic
      characteristic: method_count
      operator: greater_than
      value: 3

# Environment-aware features
cors_credentials:
  enabled: true
  environments:
    dev: true
    test: true
    prod: false  # Disabled in production for security
  default: false

# Security features
security_headers:
  enabled: true
  environments:
    prod: true
    staging: true
    dev: false
    test: false
  conditions:
    - type: environment
      values: ["prod", "staging"]

# Traffic management features
advanced_traffic_management:
  enabled: true
  percentage: 75
  conditions:
    - type: api_characteristic
      characteristic: has_security
      value: true

# Monitoring and observability
enhanced_monitoring:
  enabled: true
  environments:
    prod: true
    staging: true
    dev: false
    test: false

# Performance optimizations
response_compression:
  enabled: true
  percentage: 100
  conditions:
    - type: context
      key: response_size_estimate
      operator: greater_than
      value: 1024

# Developer experience features
template_validation:
  enabled: true
  environments:
    dev: true
    test: true
    staging: false
    prod: false

# API-specific features
json_threat_protection:
  enabled: true
  conditions:
    - type: context
      key: content_type
      operator: in
      value: ["application/json", "application/vnd.api+json"]

xml_threat_protection:
  enabled: true
  conditions:
    - type: context
      key: content_type
      operator: in
      value: ["application/xml", "text/xml"]

# Conditional policy attachment
conditional_policies:
  enabled: true
  percentage: 100

# Configuration validation
config_validation:
  enabled: true
  environments:
    dev: true
    test: true
    staging: true
    prod: true

# Custom quota variables
custom_quota_variables:
  enabled: false
  percentage: 25
  conditions:
    - type: system
      values: ["payments", "orders"]

# Expose custom headers in CORS
expose_custom_headers:
  enabled: true
  environments:
    dev: true
    test: true
    prod: false

# Allow DELETE method in production
allow_delete_in_prod:
  enabled: false
  environments:
    prod: false
  default: false

# High quota limits for specific systems
high_quota_limits:
  enabled: true
  conditions:
    - type: system
      values: ["internal-apis", "admin-apis"]

# Client ID identification
client_id_identification:
  enabled: true
  percentage: 100

# Async quota processing
async_quota:
  enabled: false
  environments:
    dev: true
    test: true
    prod: false

# Feature flag for regex-based flow conditions
regex_flow_conditions:
  enabled: true
  percentage: 80
  conditions:
    - type: context
      key: complex_routing
      value: true

# Dynamic flow generation
dynamic_flows:
  enabled: true
  percentage: 60
  environments:
    dev: true
    test: true
    staging: false
    prod: false
