"""
Error taxonomy + FastAPI exception handlers.

- Domain-first exceptions to keep controllers thin
- Problem-shaped JSON response: { "error": { "code", "message", "details" } }
- Mapped status codes per exception type
"""

from __future__ import annotations

import logging
from typing import Any, Dict, Optional, <PERSON><PERSON>, Type

import structlog
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from starlette.responses import JSONResponse
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_415_UNSUPPORTED_MEDIA_TYPE,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_429_TOO_MANY_REQUESTS,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

log = structlog.get_logger(__name__)


# ---------- Domain Exceptions ----------

class DomainError(Exception):
    """Base domain error with optional details payload."""

    def __init__(self, message: str, *, details: Optional[Dict[str, Any]] = None) -> None:
        super().__init__(message)
        self.message = message
        self.details = details or {}


class ValidationError(DomainError):
    """Payload, config, or OAS validation failed."""


class ConflictError(DomainError):
    """Resource already exists or cannot be created in the requested state."""


class NotFoundError(DomainError):
    """Referenced resource not found."""


class AuthError(DomainError):
    """Authentication / token exchange / permission issue."""


class UpstreamError(DomainError):
    """Apigee/Git/HTTP upstream error."""


# Map exception → (code, http_status)
EXC_MAP: Dict[Type[Exception], Tuple[str, int]] = {
    ValidationError: ("VALIDATION_ERROR", HTTP_422_UNPROCESSABLE_ENTITY),
    ConflictError: ("CONFLICT", HTTP_409_CONFLICT),
    NotFoundError: ("NOT_FOUND", HTTP_404_NOT_FOUND),
    AuthError: ("AUTH_ERROR", HTTP_401_UNAUTHORIZED),
    UpstreamError: ("UPSTREAM_ERROR", HTTP_500_INTERNAL_SERVER_ERROR),
    DomainError: ("DOMAIN_ERROR", HTTP_400_BAD_REQUEST),
}


# ---------- Response helpers ----------

def _problem(code: str, message: str, status: int, *, details: Optional[Dict[str, Any]] = None):
    return JSONResponse(
        status_code=status,
        content={
            "error": {
                "code": code,
                "message": message,
                "details": details or {},
            }
        },
    )


# ---------- Exception Handlers ----------

async def _handle_domain_error(request: Request, exc: DomainError):
    code, status = EXC_MAP.get(type(exc), EXC_MAP[DomainError])
    log.warning("domain_error", code=code, status=status, details=exc.details, msg=str(exc))
    return _problem(code, exc.message, status, details=exc.details)


async def _handle_http_exception(request: Request, exc: HTTPException):
    # Preserve FastAPI/Starlette HTTPException but return problem-shaped JSON
    code = "HTTP_ERROR"
    status = exc.status_code
    log.warning("http_exception", code=code, status=status, detail=exc.detail)
    details = exc.detail if isinstance(exc.detail, dict) else {"detail": exc.detail}
    return _problem(code, details.get("detail", "HTTP error occurred"), status, details=details)


async def _handle_validation_error(request: Request, exc: RequestValidationError):
    code = "REQUEST_VALIDATION_ERROR"
    status = HTTP_422_UNPROCESSABLE_ENTITY
    log.warning("request_validation_error", code=code, errors=exc.errors())
    return _problem(code, "Request validation failed", status, details={"errors": exc.errors()})


async def _handle_unexpected_error(request: Request, exc: Exception):
    code = "INTERNAL_SERVER_ERROR"
    status = HTTP_500_INTERNAL_SERVER_ERROR
    # Use logging module to ensure stack traces hit stderr too
    logging.getLogger(__name__).exception("Unhandled exception")
    log.error("unhandled_exception", code=code, status=status)
    return _problem(code, "Unexpected server error", status)


# ---------- Installer ----------

def install_exception_handlers(app: FastAPI) -> None:
    """
    Register global exception handlers on the FastAPI app.
    Call this once at startup.
    """
    app.add_exception_handler(DomainError, _handle_domain_error)
    app.add_exception_handler(ValidationError, _handle_domain_error)
    app.add_exception_handler(ConflictError, _handle_domain_error)
    app.add_exception_handler(NotFoundError, _handle_domain_error)
    app.add_exception_handler(AuthError, _handle_domain_error)
    app.add_exception_handler(UpstreamError, _handle_domain_error)

    app.add_exception_handler(HTTPException, _handle_http_exception)
    app.add_exception_handler(RequestValidationError, _handle_validation_error)
    app.add_exception_handler(Exception, _handle_unexpected_error)
