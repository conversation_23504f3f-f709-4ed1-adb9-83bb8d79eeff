<PopulateCache name="{{ name }}">
  {% if params.cache_resource %}<CacheResource>{{ params.cache_resource }}</CacheResource>{% endif %}
  {% if params.scope %}<Scope>{{ params.scope }}</Scope>{% endif %}
  <CacheKey>
    {% for frag in params.key_fragments %}
    <KeyFragment>{{ frag }}</KeyFragment>
    {% endfor %}
  </CacheKey>
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %} <!-- variable to cache -->
  {% if params.expiry_seconds is defined %}
  <ExpirySettings>
    <TimeoutInSeconds>{{ params.expiry_seconds }}</TimeoutInSeconds>
  </ExpirySettings>
  {% endif %}
</PopulateCache>