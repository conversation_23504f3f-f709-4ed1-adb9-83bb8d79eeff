"""
Unit tests for ResourceUploader service
"""

import pytest
from unittest.mock import Mock, patch
from app.services.resource_uploader import ResourceUploader
from app.services.apigee_client import ApigeeClient


class TestResourceUploader:
    """Test ResourceUploader functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.mock_apigee = Mock(spec=ApigeeClient)
        self.uploader = ResourceUploader(self.mock_apigee)
    
    def test_extract_js_resources_dictionary_format(self):
        """Test JS resource extraction with dictionary format"""
        system_cfg = {
            "resources": {
                "js": ["file1.js", "file2.js", "utils.js"]
            }
        }
        
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == ["file1.js", "file2.js", "utils.js"]
    
    def test_extract_js_resources_list_format(self):
        """Test JS resource extraction with list format (actual YAML structure)"""
        system_cfg = {
            "resources": [
                {"path": "xsl/transform/normalize.xsl", "type": "resource"},
                {"path": "helper.js", "type": "jsc"},
                {"name": "utils.js", "type": "jsc"},
                {"path": "schema.xsd", "type": "xsd"}
            ]
        }
        
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == ["helper.js", "utils.js"]
    
    def test_extract_js_resources_list_format_no_js(self):
        """Test JS resource extraction with list format but no JS resources"""
        system_cfg = {
            "resources": [
                {"path": "xsl/transform/normalize.xsl", "type": "resource"},
                {"path": "schema.xsd", "type": "xsd"}
            ]
        }
        
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == []
    
    def test_extract_js_resources_empty_resources(self):
        """Test JS resource extraction with empty resources"""
        system_cfg = {"resources": []}
        
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == []
    
    def test_extract_js_resources_no_resources_key(self):
        """Test JS resource extraction when resources key is missing"""
        system_cfg = {"metadata": {"system": "test"}}
        
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == []
    
    def test_extract_js_resources_invalid_input_types(self):
        """Test JS resource extraction with invalid input types"""
        invalid_configs = [
            None,
            [],
            "string",
            {"resources": "invalid"}
        ]
        
        for config in invalid_configs:
            result = self.uploader._extract_js_resources(config)
            assert result == [], f"Invalid config {config} should return empty list"
    
    def test_extract_js_resources_original_error_scenario(self):
        """Test the specific scenario that was causing the original 'list object has no attribute get' error"""
        # This is the structure from reflect_v2.yaml that was causing the error
        system_cfg = {
            "metadata": {"system": "payments"},
            "resources": [  # This is a LIST, not a dict!
                {"path": "xsl/transform/normalize.xsl", "type": "resource"}
            ]
        }
        
        # This should NOT raise "list object has no attribute get" error
        result = self.uploader._extract_js_resources(system_cfg)
        assert result == []  # No JS resources in this config
    
    def test_upload_js_from_system_no_js_resources(self):
        """Test upload_js_from_system when no JS resources are configured"""
        system_cfg = {
            "resources": [
                {"path": "xsl/transform/normalize.xsl", "type": "resource"}
            ]
        }
        
        # Should not raise any errors and should not call apigee client
        self.uploader.upload_js_from_system("test-api", 1, system_cfg, "Bearer token")
        
        # Verify no upload calls were made
        self.mock_apigee.upload_resource_file.assert_not_called()
    
    def test_upload_js_from_system_dictionary_format(self):
        """Test upload_js_from_system with dictionary format (backward compatibility)"""
        system_cfg = {
            "resources": {
                "js": ["helper.js", "utils.js"]
            }
        }

        # Should extract JS files correctly without errors
        js_list = self.uploader._extract_js_resources(system_cfg)
        assert js_list == ["helper.js", "utils.js"]
