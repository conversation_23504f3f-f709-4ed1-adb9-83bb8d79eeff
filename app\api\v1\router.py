from fastapi import APIRouter
from .endpoints import health, ready, bootstrap, policies

router = APIRouter()
router.include_router(health.router, prefix="/health", tags=["Health"])
router.include_router(ready.router, prefix="/ready", tags=["Readiness"])
router.include_router(bootstrap.router, prefix="/development", tags=["Development"])
router.include_router(policies.router, prefix="/policy-catalog", tags=["Policy Catalog"])
