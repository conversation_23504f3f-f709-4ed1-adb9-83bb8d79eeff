#!/usr/bin/env python3
"""
Test multiple policy instances functionality
"""

import sys
import os
import yaml
from pathlib import Path

# Add the project root to the Python path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from app.services.policy_builder import PolicyBuilder


def test_multiple_policy_instances():
    """Test that multiple instances of the same policy type can be created with different display names"""
    print("Testing Optimized Multiple Policy Instances...")

    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"

    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)

    # Initialize PolicyBuilder
    builder = PolicyBuilder()

    # Test input with multiple AssignMessage policies
    input_vars = {
        "policy_params": {}
    }

    try:
        # Render policies
        policies, attach_plan = builder.render_policies(
            system_cfg=system_cfg,
            input_vars=input_vars,
            system_name="REFLECT_FDID"
        )

        print(f"✅ Successfully rendered {len(policies)} policies")

        # Check for multiple AssignMessage instances
        assign_message_policies = [p for p in policies if p.name.startswith("AM-")]
        print(f"✅ Found {len(assign_message_policies)} AssignMessage policy instances:")
        for policy in assign_message_policies:
            print(f"   - {policy.name}")

        # Check for multiple RaiseFault instances
        raise_fault_policies = [p for p in policies if p.name.startswith("RF-")]
        print(f"✅ Found {len(raise_fault_policies)} RaiseFault policy instances:")
        for policy in raise_fault_policies:
            print(f"   - {policy.name}")

        # Check for multiple FlowCallout instances
        flow_callout_policies = [p for p in policies if p.name.startswith("FC-")]
        print(f"✅ Found {len(flow_callout_policies)} FlowCallout policy instances:")
        for policy in flow_callout_policies:
            print(f"   - {policy.name}")

        # Verify that all policy names are unique
        policy_names = [p.name for p in policies]
        unique_names = set(policy_names)

        if len(policy_names) == len(unique_names):
            print("✅ All policy names are unique")
        else:
            print("❌ Found duplicate policy names")
            duplicates = [name for name in policy_names if policy_names.count(name) > 1]
            print(f"   Duplicates: {set(duplicates)}")

        # Check attach plan
        print(f"\nAttach Plan:")
        print(f"  Preflow Request: {attach_plan.preflow_request}")
        print(f"  Preflow Response: {attach_plan.preflow_response}")
        print(f"  Per-operation Request: {attach_plan.perflow_request}")
        print(f"  Per-operation Response: {attach_plan.perflow_response}")

        # Verify specific policies are in the attach plan (updated for new structure)
        expected_policies = [
            "VK-VerifyApiKey", "FC-CommonSecurityPreFlow", "SA-SpikeArrest", "AM-SetRequestData",
            "AM-AddRespHeaders", "Q-ImposeQuota", "EV-ExtractRequestPayload",
            "AM-SetTargetStartTime", "AM-SetTargetEndTime", "FC-ExtractCustomLogs"
        ]

        all_attached_policies = (
            attach_plan.preflow_request + attach_plan.preflow_response +
            attach_plan.perflow_request + attach_plan.perflow_response
        )

        for expected in expected_policies:
            if expected in all_attached_policies:
                print(f"✅ {expected} is properly attached to flows")
            else:
                print(f"❌ {expected} is missing from flow attachments")

        # Test template-based correlation
        print(f"\nTemplate Correlation Test:")
        for policy in policies:
            if policy.name.startswith("AM-"):
                print(f"✅ {policy.name} uses AssignMessage template")
            elif policy.name.startswith("RF-"):
                print(f"✅ {policy.name} uses RaiseFault template")
            elif policy.name.startswith("FC-"):
                print(f"✅ {policy.name} uses FlowCallout template")

        return True

    except Exception as e:
        print(f"❌ Error rendering policies: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_policy_xml_generation():
    """Test that the generated XML is valid for multiple policy instances"""
    print("\nTesting Policy XML Generation...")
    
    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"
    
    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)
    
    # Initialize PolicyBuilder
    builder = PolicyBuilder()
    
    input_vars = {"policy_params": {}}
    
    try:
        policies, _ = builder.render_policies(
            system_cfg=system_cfg,
            input_vars=input_vars,
            system_name="REFLECT_FDID"
        )
        
        # Test specific policy instances
        test_policies = ["AM-AddRespHeaders", "RF-MethodNotAllowed", "RF-ResourceNotFound", "FC-CommonErrorHandler"]
        
        for test_policy in test_policies:
            policy = next((p for p in policies if p.name == test_policy), None)
            if policy:
                print(f"✅ {test_policy} XML generated successfully")
                # Basic XML validation - check if it starts with expected tag
                if test_policy.startswith("AM-"):
                    assert policy.xml.strip().startswith("<AssignMessage"), f"Invalid XML for {test_policy}"
                elif test_policy.startswith("RF-"):
                    assert policy.xml.strip().startswith("<RaiseFault"), f"Invalid XML for {test_policy}"
                elif test_policy.startswith("FC-"):
                    assert policy.xml.strip().startswith("<FlowCallout"), f"Invalid XML for {test_policy}"
                print(f"   XML validation passed for {test_policy}")
            else:
                print(f"❌ {test_policy} not found in generated policies")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating XML: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("TESTING MULTIPLE POLICY INSTANCES")
    print("=" * 60)
    
    success1 = test_multiple_policy_instances()
    success2 = test_policy_xml_generation()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ ALL TESTS PASSED!")
        print("Multiple policy instances are working correctly.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the implementation.")
    print("=" * 60)
