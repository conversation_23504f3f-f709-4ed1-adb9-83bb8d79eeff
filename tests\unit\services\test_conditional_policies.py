"""
Unit tests for ConditionalPolicyAttacher service
===============================================
Comprehensive test coverage for conditional policy attachment,
policy conditions, and dynamic policy selection.
"""

import pytest
from unittest.mock import Mock, patch

from app.services.conditional_policies import (
    PolicyCondition,
    ConditionalPolicy,
    ConditionalPolicyAttacher,
    get_conditional_attacher,
    create_policy_condition,
    create_conditional_policy_from_config
)


class TestPolicyCondition:
    """Test suite for PolicyCondition class"""
    
    def test_init(self):
        """Test PolicyCondition initialization"""
        condition = PolicyCondition("environment", environments=["prod", "staging"])
        
        assert condition.type == "environment"
        assert condition.params["environments"] == ["prod", "staging"]
    
    def test_evaluate_environment_condition(self):
        """Test environment condition evaluation"""
        condition = PolicyCondition("environment", environments=["prod", "staging"])
        
        context = {"env_name": "prod"}
        assert condition.evaluate(context) is True
        
        context = {"env_name": "dev"}
        assert condition.evaluate(context) is False
    
    def test_evaluate_feature_flag_condition(self):
        """Test feature flag condition evaluation"""
        condition = PolicyCondition("feature_flag", flag="advanced_security")
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.return_value = True
            mock_get.return_value = mock_evaluator
            
            context = {"env_name": "prod", "system_name": "payments"}
            assert condition.evaluate(context) is True
            
            mock_evaluator.is_enabled.assert_called_once()
    
    def test_evaluate_api_method_count_condition(self):
        """Test API method count condition evaluation"""
        condition = PolicyCondition("api_method_count", operator="greater_than", value=3)
        
        context = {"methods": ["GET", "POST", "PUT", "DELETE", "PATCH"]}
        assert condition.evaluate(context) is True
        
        context = {"methods": ["GET", "POST"]}
        assert condition.evaluate(context) is False
    
    def test_evaluate_api_method_count_operators(self):
        """Test different operators for API method count"""
        # Test less_than
        condition = PolicyCondition("api_method_count", operator="less_than", value=3)
        context = {"methods": ["GET", "POST"]}
        assert condition.evaluate(context) is True
        
        # Test equals
        condition = PolicyCondition("api_method_count", operator="equals", value=2)
        context = {"methods": ["GET", "POST"]}
        assert condition.evaluate(context) is True
    
    def test_evaluate_has_security_condition(self):
        """Test has_security condition evaluation"""
        condition = PolicyCondition("has_security")
        
        context = {"security_policies": ["VerifyApiKey"]}
        assert condition.evaluate(context) is True
        
        context = {"security_policies": []}
        assert condition.evaluate(context) is False
        
        context = {}
        assert condition.evaluate(context) is False
    
    def test_evaluate_content_type_condition(self):
        """Test content type condition evaluation"""
        condition = PolicyCondition("content_type", types=["application/json", "application/xml"])
        
        context = {"content_types": ["application/json", "text/plain"]}
        assert condition.evaluate(context) is True
        
        context = {"content_types": ["text/plain", "text/html"]}
        assert condition.evaluate(context) is False
        
        context = {"content_types": []}
        assert condition.evaluate(context) is False
    
    def test_evaluate_system_condition(self):
        """Test system condition evaluation"""
        condition = PolicyCondition("system", systems=["payments", "orders"])
        
        context = {"system_name": "payments"}
        assert condition.evaluate(context) is True
        
        context = {"system_name": "users"}
        assert condition.evaluate(context) is False
        
        context = {}
        assert condition.evaluate(context) is False
    
    def test_evaluate_custom_condition(self):
        """Test custom condition evaluation"""
        def custom_evaluator(context):
            return context.get("custom_flag", False)
        
        condition = PolicyCondition("custom", evaluator=custom_evaluator)
        
        context = {"custom_flag": True}
        assert condition.evaluate(context) is True
        
        context = {"custom_flag": False}
        assert condition.evaluate(context) is False
    
    def test_evaluate_unknown_condition_type(self):
        """Test evaluation of unknown condition type"""
        condition = PolicyCondition("unknown_type", param="value")
        
        context = {"env_name": "test"}
        assert condition.evaluate(context) is False


class TestConditionalPolicy:
    """Test suite for ConditionalPolicy class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.conditions = [
            PolicyCondition("environment", environments=["prod"]),
            PolicyCondition("feature_flag", flag="advanced_security")
        ]
        
        self.policy = ConditionalPolicy(
            policy_name="JSONThreatProtection",
            display_name="JTP-JSONThreatProtection",
            conditions=self.conditions,
            params={"source": "request"},
            priority=10
        )
    
    def test_init(self):
        """Test ConditionalPolicy initialization"""
        assert self.policy.policy_name == "JSONThreatProtection"
        assert self.policy.display_name == "JTP-JSONThreatProtection"
        assert len(self.policy.conditions) == 2
        assert self.policy.params == {"source": "request"}
        assert self.policy.priority == 10
    
    def test_init_defaults(self):
        """Test ConditionalPolicy initialization with defaults"""
        policy = ConditionalPolicy(policy_name="TestPolicy")
        
        assert policy.policy_name == "TestPolicy"
        assert policy.display_name is None
        assert policy.conditions == []
        assert policy.params == {}
        assert policy.priority == 100
    
    def test_should_attach_all_conditions_true(self):
        """Test should_attach when all conditions are true"""
        context = {"env_name": "prod", "system_name": "payments"}
        
        with patch.object(self.conditions[0], 'evaluate', return_value=True):
            with patch.object(self.conditions[1], 'evaluate', return_value=True):
                assert self.policy.should_attach(context) is True
    
    def test_should_attach_one_condition_false(self):
        """Test should_attach when one condition is false"""
        context = {"env_name": "prod", "system_name": "payments"}
        
        with patch.object(self.conditions[0], 'evaluate', return_value=True):
            with patch.object(self.conditions[1], 'evaluate', return_value=False):
                assert self.policy.should_attach(context) is False
    
    def test_should_attach_no_conditions(self):
        """Test should_attach with no conditions (always attach)"""
        policy = ConditionalPolicy(policy_name="AlwaysAttach")
        
        context = {"env_name": "test"}
        assert policy.should_attach(context) is True
    
    def test_to_policy_entry(self):
        """Test conversion to policy entry format"""
        entry = self.policy.to_policy_entry()
        
        assert entry["policy"] == "JSONThreatProtection"
        assert entry["display_name"] == "JTP-JSONThreatProtection"
        assert entry["params"] == {"source": "request"}
    
    def test_to_policy_entry_minimal(self):
        """Test conversion to policy entry with minimal data"""
        policy = ConditionalPolicy(policy_name="MinimalPolicy")
        entry = policy.to_policy_entry()
        
        assert entry["policy"] == "MinimalPolicy"
        assert "display_name" not in entry
        assert "params" not in entry


class TestConditionalPolicyAttacher:
    """Test suite for ConditionalPolicyAttacher class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.attacher = ConditionalPolicyAttacher()
        
        # Mock the conditional policies to avoid loading real ones
        self.mock_policies = [
            ConditionalPolicy(
                policy_name="JSONThreatProtection",
                display_name="JTP-JSONThreatProtection",
                conditions=[
                    PolicyCondition("feature_flag", flag="json_threat_protection"),
                    PolicyCondition("content_type", types=["application/json"])
                ],
                priority=10
            ),
            ConditionalPolicy(
                policy_name="XMLThreatProtection",
                display_name="XTP-XMLThreatProtection",
                conditions=[
                    PolicyCondition("feature_flag", flag="xml_threat_protection"),
                    PolicyCondition("content_type", types=["application/xml"])
                ],
                priority=10
            ),
            ConditionalPolicy(
                policy_name="ResponseCache",
                display_name="RC-ResponseCache",
                conditions=[
                    PolicyCondition("feature_flag", flag="smart_caching"),
                    PolicyCondition("api_method_count", operator="greater_than", value=5)
                ],
                priority=50
            )
        ]
        
        self.attacher.conditional_policies = self.mock_policies
    
    def test_init(self):
        """Test ConditionalPolicyAttacher initialization"""
        attacher = ConditionalPolicyAttacher()
        
        assert hasattr(attacher, 'conditional_policies')
        assert isinstance(attacher.conditional_policies, list)
    
    def test_get_applicable_policies_none_applicable(self):
        """Test getting applicable policies when none match"""
        context = {
            "env_name": "test",
            "system_name": "test-system",
            "content_types": ["text/plain"],
            "methods": ["GET"]
        }
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.return_value = False
            mock_get.return_value = mock_evaluator
            
            policies = self.attacher.get_applicable_policies(context)
            
            assert len(policies) == 0
    
    def test_get_applicable_policies_json_threat_protection(self):
        """Test getting applicable policies for JSON content"""
        context = {
            "env_name": "prod",
            "system_name": "payments",
            "content_types": ["application/json"],
            "methods": ["POST"]
        }
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.side_effect = lambda flag, ctx: flag == "json_threat_protection"
            mock_get.return_value = mock_evaluator
            
            policies = self.attacher.get_applicable_policies(context)
            
            assert len(policies) == 1
            assert policies[0]["policy"] == "JSONThreatProtection"
            assert policies[0]["display_name"] == "JTP-JSONThreatProtection"
    
    def test_get_applicable_policies_multiple_matches(self):
        """Test getting multiple applicable policies"""
        context = {
            "env_name": "prod",
            "system_name": "payments",
            "content_types": ["application/json"],
            "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]
        }
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.side_effect = lambda flag, ctx: flag in ["json_threat_protection", "smart_caching"]
            mock_get.return_value = mock_evaluator
            
            policies = self.attacher.get_applicable_policies(context)
            
            assert len(policies) == 2
            
            # Should be sorted by priority (lower number = higher priority)
            assert policies[0]["policy"] == "JSONThreatProtection"  # priority 10
            assert policies[1]["policy"] == "ResponseCache"  # priority 50
    
    def test_get_applicable_policies_priority_sorting(self):
        """Test that policies are sorted by priority"""
        # Add a high-priority policy
        high_priority_policy = ConditionalPolicy(
            policy_name="HighPriorityPolicy",
            conditions=[PolicyCondition("environment", environments=["test"])],
            priority=1
        )
        
        self.attacher.conditional_policies.append(high_priority_policy)
        
        context = {"env_name": "test", "content_types": ["application/json"]}
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.return_value = True
            mock_get.return_value = mock_evaluator
            
            policies = self.attacher.get_applicable_policies(context)
            
            # High priority policy should be first
            assert policies[0]["policy"] == "HighPriorityPolicy"
    
    def test_add_conditional_policy(self):
        """Test adding a new conditional policy"""
        new_policy = ConditionalPolicy(
            policy_name="NewPolicy",
            conditions=[PolicyCondition("environment", environments=["test"])]
        )
        
        initial_count = len(self.attacher.conditional_policies)
        self.attacher.add_conditional_policy(new_policy)
        
        assert len(self.attacher.conditional_policies) == initial_count + 1
        assert self.attacher.conditional_policies[-1] == new_policy
    
    def test_evaluate_policy_conditions(self):
        """Test evaluating all policy conditions"""
        context = {
            "env_name": "prod",
            "content_types": ["application/json"],
            "methods": ["GET", "POST"]
        }
        
        with patch('app.services.conditional_policies.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.side_effect = lambda flag, ctx: flag == "json_threat_protection"
            mock_get.return_value = mock_evaluator
            
            results = self.attacher.evaluate_policy_conditions(context)
            
            assert isinstance(results, dict)
            assert "JSONThreatProtection" in results
            assert "XMLThreatProtection" in results
            assert "ResponseCache" in results
            
            assert results["JSONThreatProtection"] is True
            assert results["XMLThreatProtection"] is False
            assert results["ResponseCache"] is False


class TestConditionalPolicyHelperFunctions:
    """Test helper functions for conditional policies"""
    
    def test_create_policy_condition(self):
        """Test creating PolicyCondition from configuration"""
        config = {
            "type": "environment",
            "environments": ["prod", "staging"]
        }
        
        condition = create_policy_condition(config)
        
        assert isinstance(condition, PolicyCondition)
        assert condition.type == "environment"
        assert condition.params["environments"] == ["prod", "staging"]
    
    def test_create_conditional_policy_from_config(self):
        """Test creating ConditionalPolicy from configuration"""
        config = {
            "policy": "TestPolicy",
            "display_name": "TP-TestPolicy",
            "params": {"param1": "value1"},
            "priority": 25,
            "conditions": [
                {
                    "type": "environment",
                    "environments": ["prod"]
                },
                {
                    "type": "feature_flag",
                    "flag": "test_feature"
                }
            ]
        }
        
        policy = create_conditional_policy_from_config(config)
        
        assert isinstance(policy, ConditionalPolicy)
        assert policy.policy_name == "TestPolicy"
        assert policy.display_name == "TP-TestPolicy"
        assert policy.params == {"param1": "value1"}
        assert policy.priority == 25
        assert len(policy.conditions) == 2
    
    def test_get_conditional_attacher(self):
        """Test get_conditional_attacher global function"""
        attacher1 = get_conditional_attacher()
        attacher2 = get_conditional_attacher()
        
        assert isinstance(attacher1, ConditionalPolicyAttacher)
        # Should return the same instance (singleton pattern)
        assert attacher1 is attacher2


class TestConditionalPolicyErrorHandling:
    """Test error handling scenarios"""
    
    def test_policy_condition_evaluate_exception(self):
        """Test PolicyCondition evaluation with exceptions"""
        condition = PolicyCondition("custom", evaluator=lambda ctx: ctx["missing_key"])
        
        context = {}  # Missing key will cause KeyError
        
        # Should handle exceptions gracefully and return False
        assert condition.evaluate(context) is False
    
    def test_conditional_policy_should_attach_exception(self):
        """Test ConditionalPolicy should_attach with condition exceptions"""
        def failing_condition(context):
            raise Exception("Test exception")
        
        condition = PolicyCondition("custom", evaluator=failing_condition)
        policy = ConditionalPolicy(
            policy_name="TestPolicy",
            conditions=[condition]
        )
        
        context = {"env_name": "test"}
        
        # Should handle condition exceptions gracefully
        assert policy.should_attach(context) is False
    
    def test_get_applicable_policies_with_malformed_context(self):
        """Test getting applicable policies with malformed context"""
        attacher = ConditionalPolicyAttacher()
        
        # Test with None context
        policies = attacher.get_applicable_policies(None)
        assert isinstance(policies, list)
        
        # Test with empty context
        policies = attacher.get_applicable_policies({})
        assert isinstance(policies, list)
    
    def test_feature_flag_condition_with_missing_evaluator(self):
        """Test feature flag condition when evaluator is not available"""
        condition = PolicyCondition("feature_flag", flag="test_flag")
        
        with patch('app.services.conditional_policies.get_feature_evaluator', side_effect=Exception("Evaluator error")):
            context = {"env_name": "test"}
            
            # Should handle evaluator errors gracefully
            assert condition.evaluate(context) is False


class TestConditionalPolicyIntegration:
    """Integration tests for conditional policies"""
    
    @pytest.mark.integration
    def test_real_conditional_policy_attachment(self):
        """Test conditional policy attachment with real configuration"""
        try:
            attacher = ConditionalPolicyAttacher()
            
            context = {
                "env_name": "prod",
                "system_name": "payments",
                "content_types": ["application/json"],
                "methods": ["GET", "POST", "PUT"],
                "security_policies": ["VerifyApiKey"],
                "features": {
                    "json_threat_protection": True,
                    "advanced_security": True
                }
            }
            
            policies = attacher.get_applicable_policies(context)
            
            assert isinstance(policies, list)
            # Should have some policies attached for this context
            if len(policies) > 0:
                for policy in policies:
                    assert "policy" in policy
                    assert isinstance(policy["policy"], str)
                    
        except Exception as e:
            pytest.skip(f"Integration test environment not available: {e}")
    
    @pytest.mark.integration
    def test_conditional_policy_evaluation_performance(self):
        """Test performance of conditional policy evaluation"""
        import time
        
        attacher = ConditionalPolicyAttacher()
        
        context = {
            "env_name": "prod",
            "system_name": "payments",
            "content_types": ["application/json"],
            "methods": ["GET", "POST", "PUT", "DELETE"],
            "security_policies": ["VerifyApiKey"]
        }
        
        start_time = time.time()
        
        # Run evaluation multiple times
        for _ in range(100):
            policies = attacher.get_applicable_policies(context)
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete 100 evaluations in reasonable time (< 1 second)
        assert elapsed < 1.0, f"Policy evaluation too slow: {elapsed:.3f}s for 100 evaluations"
