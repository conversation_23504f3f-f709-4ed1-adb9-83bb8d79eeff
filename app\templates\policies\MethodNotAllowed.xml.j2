<!-- RaiseFault policy -->
<RaiseFault async="false" continueOnError="false" enabled="true" name="RF-MethodNotAllowed">
    <DisplayName>RF-MethodNotAllowed</DisplayName>
    <Properties/>
    <FaultResponse>
        <Set>
            <StatusCode>405</StatusCode>
            <ReasonPhrase>Method Not Allowed</ReasonPhrase>
        </Set>
    </FaultResponse>
    <IgnoreUnresolvedVariables>true</IgnoreUnresolvedVariables>
</RaiseFault>