# Installation Guide

Complete installation guide for the Apigee Proxy Factory with all Phase 1-4 enhancements and comprehensive testing capabilities.

## 📋 Prerequisites

- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Disk Space**: 500MB for installation, 1GB for development

## 🚀 Quick Installation

### Option 1: Production Installation
```bash
# Clone the repository
git clone <repository-url>
cd apigee-proxy-factory

# Install core dependencies
pip install -r requirements.txt

# Verify installation
python -c "from app.main import create_app; print('✅ Installation successful!')"
```

### Option 2: Development Installation
```bash
# Clone the repository
git clone <repository-url>
cd apigee-proxy-factory

# Install development dependencies
pip install -r requirements-dev.txt

# Install in editable mode
pip install -e .

# Set up pre-commit hooks
pre-commit install

# Verify installation
make test-unit
```

### Option 3: Using Make (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd apigee-proxy-factory

# Set up development environment
make setup-dev

# Run tests to verify
make test
```

## 📦 Installation Options

### Core Installation (Production)
```bash
pip install -r requirements.txt
```
**Includes**: FastAPI, Jinja2, YAML processing, basic testing, core Phase 1-4 features

### Development Installation
```bash
pip install -r requirements-dev.txt
```
**Includes**: All core features + testing framework + code quality tools + documentation tools

### Testing Only
```bash
pip install -r requirements-test.txt
```
**Includes**: Minimal testing dependencies for CI/CD environments

### Optional Features
```bash
pip install -r requirements-optional.txt
```
**Includes**: Database support, caching, monitoring, advanced security features

### Complete Installation
```bash
pip install -e .[all]
```
**Includes**: Everything - all features, testing, development tools

## 🔧 Dependency Categories

### Core Dependencies (Always Required)
| Package | Version | Purpose |
|---------|---------|---------|
| fastapi | >=0.111.0 | Web framework |
| uvicorn | >=0.30.0 | ASGI server |
| pydantic | >=2.8.0 | Data validation |
| jinja2 | >=3.1.0 | Template engine |
| PyYAML | >=6.0.0 | YAML processing |
| structlog | >=24.1.0 | Structured logging |
| httpx | >=0.27.0 | HTTP client |

### Phase Enhancement Dependencies
| Package | Version | Phase | Purpose |
|---------|---------|-------|---------|
| jsonschema | >=4.20.0 | 2 | Configuration validation |
| cerberus | >=1.3.5 | 2 | Schema validation |
| markupsafe | >=2.1.0 | 3 | Template security |
| cachetools | >=5.3.0 | 4 | Feature flag caching |
| cryptography | >=41.0.0 | 4 | Security features |

### Testing Dependencies
| Package | Version | Purpose |
|---------|---------|---------|
| pytest | >=7.4.0 | Test framework |
| pytest-cov | >=4.1.0 | Coverage reporting |
| pytest-mock | >=3.12.0 | Mocking utilities |
| responses | >=0.24.0 | HTTP mocking |
| faker | >=20.0.0 | Test data generation |

### Development Dependencies
| Package | Version | Purpose |
|---------|---------|---------|
| black | >=23.9.0 | Code formatting |
| flake8 | >=6.1.0 | Linting |
| mypy | >=1.6.0 | Type checking |
| bandit | >=1.7.5 | Security scanning |
| pre-commit | >=3.5.0 | Git hooks |

## 🐍 Python Version Support

| Python Version | Status | Notes |
|----------------|--------|-------|
| 3.8 | ✅ Supported | Minimum version |
| 3.9 | ✅ Supported | Recommended |
| 3.10 | ✅ Supported | Recommended |
| 3.11 | ✅ Supported | Latest features |
| 3.12 | ✅ Supported | Latest features |

## 🌍 Environment Setup

### Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv venv

# Activate (Linux/macOS)
source venv/bin/activate

# Activate (Windows)
venv\Scripts\activate

# Install dependencies
pip install -r requirements-dev.txt
```

### Conda Environment
```bash
# Create conda environment
conda create -n apigee-proxy-factory python=3.9

# Activate environment
conda activate apigee-proxy-factory

# Install dependencies
pip install -r requirements-dev.txt
```

### Docker Environment
```bash
# Build Docker image
docker build -t apigee-proxy-factory .

# Run container
docker run -p 8000:8000 apigee-proxy-factory
```

## ⚙️ Configuration

### Environment Variables
```bash
# Core configuration
export APIGEE_ENV=test
export LOG_LEVEL=INFO

# Optional features
export REDIS_URL=redis://localhost:6379
export DATABASE_URL=postgresql://user:pass@localhost/db
```

### Configuration Files
```bash
# Copy example configurations
cp app/config/systems/reflect.yaml app/config/systems/my-system.yaml
cp app/config/feature_flags.yaml app/config/my-feature-flags.yaml
```

## 🧪 Verification

### Basic Verification
```bash
# Test core functionality
python -c "
from app.services.policy_builder import PolicyBuilder
from app.config.loader import ConfigLoader
print('✅ Core services working')
"

# Test API
python -c "
from app.main import create_app
app = create_app()
print('✅ API application created')
"
```

### Comprehensive Testing
```bash
# Run all tests
python run_tests.py --all

# Run specific test categories
python run_tests.py --unit
python run_tests.py --integration
python run_tests.py --coverage
```

### Development Server
```bash
# Start development server
uvicorn app.main:app --reload

# Or using make
make run

# Verify at http://localhost:8000/docs
```

## 🚨 Troubleshooting

### Common Issues

#### Import Errors
```bash
# Issue: ModuleNotFoundError
# Solution: Install in editable mode
pip install -e .
```

#### Template Errors
```bash
# Issue: Template not found
# Solution: Verify template directory structure
ls app/templates/policies/
```

#### Configuration Errors
```bash
# Issue: YAML parsing errors
# Solution: Validate YAML syntax
python -c "import yaml; yaml.safe_load(open('app/config/systems/reflect.yaml'))"
```

#### Test Failures
```bash
# Issue: Tests failing
# Solution: Install test dependencies
pip install -r requirements-test.txt

# Run specific failing test
pytest tests/unit/services/test_policy_builder.py::TestPolicyBuilder::test_render_policies_basic -v
```

### Platform-Specific Issues

#### Windows
```bash
# Long path issues
git config --system core.longpaths true

# PowerShell execution policy
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### macOS
```bash
# Xcode command line tools
xcode-select --install

# Homebrew dependencies
brew install python@3.9
```

#### Linux
```bash
# Ubuntu/Debian dependencies
sudo apt-get update
sudo apt-get install python3-dev python3-pip python3-venv

# CentOS/RHEL dependencies
sudo yum install python3-devel python3-pip
```

## 📈 Performance Optimization

### Production Optimizations
```bash
# Install with production optimizations
pip install -r requirements.txt --no-cache-dir

# Use faster JSON library
pip install orjson

# Enable uvloop (Linux/macOS)
pip install uvloop
```

### Development Optimizations
```bash
# Parallel testing
pip install pytest-xdist
pytest tests/ -n auto

# Faster linting
pip install flake8-cached
```

## 🔄 Updates and Maintenance

### Updating Dependencies
```bash
# Update all dependencies
pip install --upgrade -r requirements.txt

# Update specific package
pip install --upgrade fastapi

# Check for security updates
safety check
```

### Version Management
```bash
# Check current version
python -c "from app import __version__; print(__version__)"

# Upgrade to latest version
git pull origin main
pip install --upgrade -r requirements.txt
```

## 📞 Support

If you encounter issues during installation:

1. **Check Prerequisites**: Ensure Python 3.8+ is installed
2. **Review Logs**: Check error messages for specific issues
3. **Verify Environment**: Ensure virtual environment is activated
4. **Test Installation**: Run `make test-unit` to verify core functionality
5. **Check Documentation**: Review README.md for additional guidance

For additional support, please refer to the project documentation or create an issue in the repository.
