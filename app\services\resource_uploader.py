"""
ResourceUploader
----------------
Uploads proxy resources to Apigee:
- swagger.json (stored under resource type "resource" at path oas/swagger.json)
- JS files listed in system YAML (type "jsc")

Usage:
    ru = ResourceUploader(apigee_client)
    ru.upload_swagger(api, rev, content_bytes, bearer)
    ru.upload_js_from_system(api, rev, system_cfg, bearer)
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, Iterable, Optional

import json
import yaml
import structlog

from ..core.errors import ValidationError
from ..core.settings import settings
from .apigee_client import ApigeeClient

log = structlog.get_logger(__name__)


class ResourceUploader:
    def __init__(self, apigee: ApigeeClient) -> None:
        self._apigee = apigee

    # ---------------- Swagger / OAS ----------------

    def upload_swagger(self, api: str, rev: int, content: bytes, bearer: str) -> None:
        """
        Accepts JSON or YAML bytes; normalizes to JSON and uploads to oas/swagger.json.
        This uploads the proxy swagger (transformed for Apigee proxy endpoints).
        """
        data = _parse_oas_to_json(content)
        payload = json.dumps(data, separators=(",", ":"), ensure_ascii=False).encode("utf-8")
        try:
            self._apigee.upload_resource_file(
                api,
                rev,
                res_type="resource",
                name="oas/swagger.json",
                content_bytes=payload,
                bearer=bearer,
                content_type="application/octet-stream",
            )
        except Exception as e:
            log.error("Failed to upload resource file", error=str(e), payload=payload)
        log.info("swagger_uploaded", api=api, rev=rev, path="oas/swagger.json")

    def upload_target_swagger(self, api: str, rev: int, content: bytes, bearer: str) -> None:
        """
        Upload original target service swagger for reference.
        Stored separately from the proxy swagger.
        """
        data = _parse_oas_to_json(content)
        payload = json.dumps(data, separators=(",", ":"), ensure_ascii=False).encode("utf-8")
        try:
            self._apigee.upload_resource_file(
                api,
                rev,
                res_type="resource",
                name="oas/target-swagger.json",
                content_bytes=payload,
                bearer=bearer,
                content_type="application/octet-stream",
            )
        except Exception as e:
            log.error("Failed to upload target swagger file", error=str(e))
        log.info("target_swagger_uploaded", api=api, rev=rev, path="oas/target-swagger.json")

    # ---------------- JS list from system YAML ----------------

    def upload_js_from_system(self, api: str, rev: int, system_cfg: Dict[str, Any], bearer: str) -> None:
        """
        Looks for resources.js list in system YAML and uploads each one from resources/js/.
        Supports both dictionary format (resources.js: [...]) and list format (resources: [{type: "jsc", ...}]).
        """
        js_list = self._extract_js_resources(system_cfg)
        if not js_list:
            log.info("no_js_resources_configured")
            return

        base = Path(settings.resources_dir).joinpath("js").resolve()
        for entry in js_list:
            fname = str(entry).strip()
            p = base.joinpath(fname).resolve()
            if not p.exists():
                raise ValidationError("JS resource not found", details={"file": str(p)})

            content = p.read_bytes()

            # Uploading resource file
            try:
                self._apigee.upload_resource_file(
                    api,
                    rev,
                    res_type="jsc",  # Res type for JS
                    name=Path(fname).name,  # Store by filename
                    content_bytes=content,
                    bearer=bearer,
                    content_type="application/octet-stream",  # Correcting content-type
                )
                log.info("js_uploaded", api=api, rev=rev, file=fname)
            except Exception as e:
                log.error("js_upload_failed", api=api, rev=rev, file=fname, error=str(e))
                raise e

    def _extract_js_resources(self, system_cfg: Dict[str, Any]) -> list:
        """
        Extract JS resource files from system configuration.
        Supports multiple formats:
        1. Dictionary format: resources.js: ["file1.js", "file2.js"]
        2. List format: resources: [{type: "jsc", path: "file1.js"}, ...]
        3. Mixed list format: resources: [{type: "jsc", name: "file1.js"}, ...]
        """
        if not isinstance(system_cfg, dict):
            return []

        resources = system_cfg.get("resources")
        if not resources:
            return []

        # Case 1: Dictionary format with 'js' key
        if isinstance(resources, dict):
            js_list = resources.get("js", [])
            return js_list if isinstance(js_list, list) else []

        # Case 2: List format - extract JS resources from list
        if isinstance(resources, list):
            js_files = []
            for resource in resources:
                if isinstance(resource, dict):
                    # Check if this is a JS resource (type: "jsc")
                    if resource.get("type") == "jsc":
                        # Try to get filename from 'path' or 'name' field
                        filename = resource.get("path") or resource.get("name")
                        if filename:
                            js_files.append(filename)
            return js_files

        # Unsupported format
        log.warning("unsupported_resources_format", resources_type=type(resources).__name__)
        return []


# ---------------- helpers ----------------

def _parse_oas_to_json(content: bytes):
    """
    Parses OpenAPI content that may be JSON or YAML and returns a Python object.
    """
    text = content.decode("utf-8", errors="ignore")
    try:
        return json.loads(text)
    except Exception:
        return yaml.safe_load(text)
