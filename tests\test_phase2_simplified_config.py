#!/usr/bin/env python3
"""
Test script for Phase 2: Simplified Configuration
- New simplified system YAML structure
- Updated policy builder for new structure
- Policy catalog system
- Automatic display name generation
"""

import json
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.policy_builder import PolicyBuilder, _generate_display_name
from app.config.loader import Confi<PERSON><PERSON><PERSON><PERSON>


def test_simplified_yaml_structure():
    """Test that the new simplified YAML structure loads correctly"""
    print("Testing Simplified YAML Structure...")
    
    try:
        loader = ConfigLoader()
        cfgs = loader.load(system_name="reflect")
        system_cfg = cfgs.system_cfg
        
        # Check new structure elements
        if "policy_defaults" in system_cfg:
            print("✓ Found policy_defaults section")
            policy_defaults = system_cfg["policy_defaults"]
            
            # Check that policy defaults are simplified (no nested params)
            if "VerifyApiKey" in policy_defaults:
                verify_api_key = policy_defaults["VerifyApiKey"]
                if isinstance(verify_api_key, dict) and "keyref" in verify_api_key:
                    print("  ✓ VerifyApiKey has simplified structure (no nested params)")
                else:
                    print("  ❌ VerifyApiKey structure incorrect")
                    return False
        else:
            print("❌ policy_defaults section not found")
            return False
        
        # Check flows structure
        if "flows" in system_cfg:
            print("✓ Found flows section")
            flows = system_cfg["flows"]
            
            if "preflow" in flows and "per_operation" in flows:
                print("  ✓ Found preflow and per_operation sections")
                
                # Check that policies are listed as simple strings
                preflow_req = flows.get("preflow", {}).get("request", [])
                if isinstance(preflow_req, list) and len(preflow_req) > 0:
                    if isinstance(preflow_req[0], str):
                        print("  ✓ Preflow policies are simple strings")
                    else:
                        print("  ❌ Preflow policies should be simple strings")
                        return False
            else:
                print("  ❌ Missing preflow or per_operation sections")
                return False
        else:
            print("❌ flows section not found")
            return False
        
        # Check other simplified sections
        if "flow_settings" in system_cfg:
            print("✓ Found flow_settings section")
        
        if "features" in system_cfg:
            print("✓ Found features section")
        
        print("✅ Simplified YAML Structure test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Simplified YAML Structure test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_display_name_generation():
    """Test automatic display name generation"""
    print("\nTesting Display Name Generation...")
    
    try:
        test_cases = [
            ("VerifyApiKey", "VK-VerifyApiKey"),
            ("SpikeArrest", "SA-SpikeArrest"),
            ("Quota", "Q-ImposeQuota"),
            ("CORS", "AM-AddCors"),
            ("ExtractVariables", "EV-ExtractVariables"),
            ("JSONThreatProtection", "JTP-JSONThreatProtection"),
            ("CustomPolicy", "CU-CustomPolicy"),  # Default case
        ]
        
        for policy_name, expected_display_name in test_cases:
            actual = _generate_display_name(policy_name)
            if actual == expected_display_name:
                print(f"  ✓ {policy_name} -> {actual}")
            else:
                print(f"  ❌ {policy_name} -> {actual} (expected {expected_display_name})")
                return False
        
        print("✅ Display Name Generation test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Display Name Generation test FAILED: {str(e)}")
        return False


def test_policy_builder_with_new_structure():
    """Test that PolicyBuilder works with the new simplified structure"""
    print("\nTesting Policy Builder with New Structure...")
    
    try:
        builder = PolicyBuilder()
        loader = ConfigLoader()
        cfgs = loader.load(system_name="reflect")
        
        # Test input with new structure
        input_vars = {
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],
            "policy_params": {
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "200pm"},
                "Quota": {"allow": "1000pm"},
                "CORS": {"allow_origins": ["https://example.com"]}
            },
            "features": {"advanced_security": True}
        }
        
        policies, attach_plan = builder.render_policies(
            cfgs.system_cfg,
            input_vars,
            system_name="reflect"
        )
        
        print(f"✓ Policies rendered: {len(policies)} policies")
        
        # Check that display names were generated correctly
        policy_names = [p.name for p in policies]
        expected_patterns = ["VK-", "SA-", "Q-", "AM-"]
        
        for pattern in expected_patterns:
            if any(name.startswith(pattern) for name in policy_names):
                print(f"  ✓ Found policy with {pattern} prefix")
            else:
                print(f"  ❌ No policy found with {pattern} prefix")
                return False
        
        # Check that parameters were applied correctly
        for policy in policies:
            if "x-api-key" in policy.xml:
                print("  ✓ VerifyApiKey parameter applied")
            if "200pm" in policy.xml:
                print("  ✓ SpikeArrest parameter applied")
            if "1000pm" in policy.xml:
                print("  ✓ Quota parameter applied")
            if "https://example.com" in policy.xml:
                print("  ✓ CORS parameter applied")
        
        print("✅ Policy Builder with New Structure test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Policy Builder with New Structure test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_policy_catalog_loading():
    """Test that the policy catalog loads correctly"""
    print("\nTesting Policy Catalog Loading...")
    
    try:
        catalog_path = Path("app/config/policy_catalog.yaml")
        if not catalog_path.exists():
            print("❌ Policy catalog file not found")
            return False
        
        import yaml
        with open(catalog_path, 'r') as f:
            catalog = yaml.safe_load(f)
        
        # Check structure
        if "categories" in catalog:
            print("✓ Found categories section")
            categories = catalog["categories"]
            
            expected_categories = ["security", "traffic_management", "data_transformation", "threat_protection"]
            for cat in expected_categories:
                if cat in categories:
                    print(f"  ✓ Found {cat} category")
                else:
                    print(f"  ❌ Missing {cat} category")
                    return False
        
        if "policies" in catalog:
            print("✓ Found policies metadata section")
            policies = catalog["policies"]
            
            # Check some key policies
            key_policies = ["VerifyApiKey", "SpikeArrest", "Quota", "CORS"]
            for policy in key_policies:
                if policy in policies:
                    policy_meta = policies[policy]
                    if "category" in policy_meta and "description" in policy_meta:
                        print(f"  ✓ {policy} has complete metadata")
                    else:
                        print(f"  ❌ {policy} missing metadata")
                        return False
        
        if "templates" in catalog:
            print("✓ Found policy templates section")
        
        print("✅ Policy Catalog Loading test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Policy Catalog Loading test FAILED: {str(e)}")
        return False


def test_backward_compatibility():
    """Test that the system still works with legacy configurations"""
    print("\nTesting Backward Compatibility...")
    
    try:
        # This test ensures that if someone has old-style config, it still works
        builder = PolicyBuilder()
        
        # Simulate old-style system config
        old_style_config = {
            "policies": {
                "defaults": {
                    "VerifyApiKey": {
                        "params": {"keyref": "request.header.client_id"}
                    }
                },
                "preflow": {
                    "request": [
                        {"policy": "VerifyApiKey", "display_name": "VK-VerifyAPIKey"}
                    ]
                }
            }
        }
        
        input_vars = {
            "methods": ["GET"],
            "headers": ["Authorization"],
            "policy_params": {}
        }
        
        policies, attach_plan = builder.render_policies(
            old_style_config,
            input_vars
        )
        
        if len(policies) > 0:
            print("✓ Legacy configuration still works")
            print(f"  - Rendered {len(policies)} policies")
        else:
            print("❌ Legacy configuration failed")
            return False
        
        print("✅ Backward Compatibility test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Backward Compatibility test FAILED: {str(e)}")
        return False


def main():
    """Run all Phase 2 tests"""
    print("=" * 60)
    print("PHASE 2: SIMPLIFIED CONFIGURATION TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_simplified_yaml_structure,
        test_display_name_generation,
        test_policy_catalog_loading,
        test_backward_compatibility,
        test_policy_builder_with_new_structure,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PHASE 2 TESTS PASSED!")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
