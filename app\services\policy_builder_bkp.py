"""
PolicyBuilder (env-aware, catalog-driven)
----------------------------------------
Renders Apigee policy XMLs from Jinja2 templates using 3 sources of param values:

  DEFAULT < ENV < INPUT

- DEFAULT: system YAML (systems/<system>.yaml)
- ENV:     per-env YAML (env/<env>/<system>.yaml), kept outside the system file
- INPUT:   API request payload + OAS-derived values (supports list ops)

Per-param merge semantics are defined by a small "param catalog" YAML, so lists
(e.g., CORS headers) can default to append_unique, while origins may replace, etc.

Templates should only reference {{ params.* }}; no default values in templates.
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import jinja2
import structlog
import yaml

from ..core.errors import ValidationError
from ..core.settings import settings

log = structlog.get_logger(__name__)


# ---------------------- Data models ----------------------

@dataclass(frozen=True)
class PolicyRender:
    name: str
    xml: str


@dataclass(frozen=True)
class AttachPlan:
    preflow_request: List[str]
    preflow_response: List[str]
    perflow_request: List[str]
    perflow_response: List[str]
    cors_policy: Optional[str] = None  # if configured/enabled


# ---------------------- Param catalog ----------------------

# Built-in param catalog defaults; can be overridden by YAML (see _load_param_catalog)
# type: string|int|bool|list ; merge for lists: replace|append_unique|prepend_unique|remove_only
# normalize: lower|exact (applies to list items)
_BUILTIN_PARAM_CATALOG: Dict[str, Dict[str, Dict[str, Any]]] = {
    "CORS": {
        "allow_origins":     {"type": "list", "merge": "replace",       "normalize": "exact"},
        "allow_methods":     {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "allow_headers":     {"type": "list", "merge": "append_unique", "normalize": "lower"},
        "expose_headers":    {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "max_age":           {"type": "int",  "merge": "replace"},
        "allow_credentials": {"type": "bool", "merge": "replace"},
    },
    "Quota": {
        "allow":          {"type": "string", "merge": "replace"},
        "interval":       {"type": "string", "merge": "replace"},
        "timeunit":       {"type": "string", "merge": "replace"},
        "identifier_ref": {"type": "string", "merge": "replace"},
    },
    "Spike-Arrest": {
        "rate": {"type": "string", "merge": "replace"},
    },
    "Verify-API-Key": {
        "keyref": {"type": "string", "merge": "replace"},
    },
    # Add others as needed (OAuthV2, ThreatProtection...)
}


def _load_param_catalog() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Load param catalog from YAML if present; fall back to built-in defaults.
    Default path: <config_root>/policy_param_catalog.yaml (sibling to systems_dir)
    """
    sys_dir = Path(settings.systems_dir).resolve()
    candidate = getattr(settings, "param_catalog_file", None)
    if candidate:
        path = Path(candidate)
    else:
        path = sys_dir.parent.joinpath("policy_param_catalog.yaml")

    if path.exists():
        try:
            with open(path, "r", encoding="utf-8") as f:
                y = yaml.safe_load(f) or {}
            # Deep merge: file overrides built-ins by (policy, key)
            merged = {k: dict(v) for k, v in _BUILTIN_PARAM_CATALOG.items()}
            for pol, spec in (y or {}).items():
                merged.setdefault(pol, {})
                for k, cfg in (spec or {}).items():
                    merged[pol][k] = cfg or {}
            log.info("param_catalog_loaded", source=str(path))
            return merged
        except Exception as e:
            log.warning("param_catalog_failed", path=str(path), error=str(e))
    return _BUILTIN_PARAM_CATALOG


# ---------------------- Policy builder ----------------------

class PolicyBuilder:
    def __init__(self, templates_dir: Optional[str] = None) -> None:
        tdir = templates_dir or settings.templates_dir
        base = Path(tdir).joinpath("policies").resolve()
        if not base.exists():
            raise ValidationError(
                f"Policies templates directory not found: {base}",
                details={"templates_dir": str(base)},
            )
        self._env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(base)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._env.undefined = jinja2.StrictUndefined
        self._templates_root = base

        self._param_catalog = _load_param_catalog()

    def render_policies(
        self,
        system_cfg: Dict[str, Any],
        input_vars: Dict[str, Any],
        *,
        system_name: Optional[str] = None,   # used to load env overrides if provided
    ) -> Tuple[List[PolicyRender], AttachPlan]:
        """
        Render all configured policies with DEFAULT < ENV < INPUT param resolution.
        - system_cfg: parsed systems/<system>.yaml
        - input_vars: UI + OAS dynamic context (may contain list ops)
        - system_name: optional; if given, try loading env overrides
        """
        pol_cfg = (system_cfg or {}).get("policies", {})
        preflow_req = _normalize_entries(pol_cfg.get("preflow", {}).get("request", []))
        preflow_resp = _normalize_entries(pol_cfg.get("preflow", {}).get("response", []))
        perflow_req = _normalize_entries(pol_cfg.get("perflow", {}).get("request", []))
        perflow_resp = _normalize_entries(pol_cfg.get("perflow", {}).get("response", []))

        # Optional env overrides (kept outside systems)
        env_overrides = _load_env_overrides(system_name) if system_name else {}

        # Optional "defaults" node per policy (inside system YAML)
        sys_defaults = (pol_cfg.get("defaults") or {})

        # Optional CORS config (policy-like, but often single AssignMessage)
        cors_cfg = (pol_cfg.get("cors") or {})
        cors_enabled = bool(cors_cfg.get("enabled"))
        cors_name = None

        outputs: List[PolicyRender] = []
        seen_names: set[str] = set()

        def _render_entries(entries: List[Dict[str, Any]]) -> List[str]:
            names: List[str] = []
            for e in entries:
                pol_name = str(e.get("name", "")).strip()           # policy file/policy Name
                template = str(e.get("template", "")).strip()
                attach_params = dict(e.get("params") or {})

                if not pol_name:
                    raise ValidationError("Policy entry missing 'name'", details={"entry": e})
                if not template:
                    raise ValidationError("Policy entry missing 'template'", details={"name": pol_name})

                # Resolve params with DEFAULT < ENV < INPUT
                final_params = self._resolve_params(
                    policy_label=pol_name,   # use actual policy name as label
                    sys_defaults=sys_defaults,
                    env_overrides=env_overrides,
                    attach_params=attach_params,
                    input_vars=input_vars,
                )

                xml = self._render_template(template, name=pol_name, params=final_params, input=input_vars)

                if pol_name not in seen_names:
                    outputs.append(PolicyRender(name=pol_name, xml=xml))
                    seen_names.add(pol_name)
                else:
                    log.warning("duplicate_policy_name", name=pol_name)

                names.append(pol_name)
            return names

        pre_req_names = _render_entries(preflow_req)
        pre_resp_names = _render_entries(preflow_resp)
        per_req_names = _render_entries(perflow_req)
        per_resp_names = _render_entries(perflow_resp)

        # Optional: CORS as a dedicated policy entry with same merging
        if cors_enabled:
            cors_template = cors_cfg.get("template", "AssignMessage.CORS.xml.j2")
            cors_name = str(cors_cfg.get("name") or "CORS").strip()
            cors_params_attach = dict(cors_cfg.get("params") or {})

            final_params = self._resolve_params(
                policy_label="CORS",    # catalog key
                sys_defaults=sys_defaults,
                env_overrides=env_overrides,
                attach_params=cors_params_attach,
                input_vars=input_vars,
            )
            xml = self._render_template(cors_template, name=cors_name, params=final_params, input=input_vars)
            if cors_name not in seen_names:
                outputs.append(PolicyRender(name=cors_name, xml=xml))
                seen_names.add(cors_name)

        plan = AttachPlan(
            preflow_request=pre_req_names,
            preflow_response=pre_resp_names,
            perflow_request=per_req_names,
            perflow_response=per_resp_names,
            cors_policy=cors_name,
        )

        log.info(
            "policies_rendered",
            pre_req=len(pre_req_names),
            pre_resp=len(pre_resp_names),
            per_req=len(per_req_names),
            per_resp=len(per_resp_names),
            cors=bool(cors_name),
        )
        return outputs, plan

    # -------------------- internals --------------------

    def _resolve_params(
        self,
        *,
        policy_label: str,
        sys_defaults: Dict[str, Any],
        env_overrides: Dict[str, Any],
        attach_params: Dict[str, Any],
        input_vars: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Compute final params for a given policy using:
            DEFAULT < ENV < INPUT
        with per-param semantics from the param catalog + list ops.
        """
        # 0) Choose catalog key: use policy label if present, otherwise a generic mapping
        catalog = self._param_catalog.get(policy_label) or self._param_catalog.get(_canonical(policy_label)) or {}

        # 1) Collect layers (raw dicts) for the given policy
        def _get(d: Dict[str, Any], key: str) -> Dict[str, Any]:
            return ((d.get(key) or {}).get("params")) if isinstance(d.get(key), dict) else {}

        layer_default = _get(sys_defaults, policy_label) or _get(sys_defaults, _canonical(policy_label)) or {}
        layer_env     = _get(env_overrides.get("policies", {}).get("overrides", {}), policy_label) \
                        or _get(env_overrides.get("policies", {}).get("overrides", {}), _canonical(policy_label)) \
                        or {}
        layer_attach  = attach_params or {}

        # 2) Merge DEFAULT < ENV < ATTACH for non-list scalars & dicts;
        #    For lists, use param catalog semantics. Also collect YAML directives (__append etc.).
        merged = _merge_three_layers(catalog, layer_default, layer_env, layer_attach)

        # 3) Apply INPUT overlay (UI + OAS). INPUT can provide list ops or simple scalars.
        policy_input = _extract_policy_input(policy_label, input_vars)
        merged = _apply_input_overlay(catalog, merged, policy_input)

        # 4) Render any strings (e.g., "User sent {{ input.quota }}") using Jinja
        rendered = _render_params_dict(self._env, merged, {"input": input_vars, "env": settings.apigee_env})
        return rendered

    def _render_template(self, template_name: str, **context: Any) -> str:
        try:
            tmpl = self._env.get_template(template_name)
        except jinja2.TemplateNotFound as e:
            raise ValidationError(
                "Policy template not found",
                details={"template": template_name, "templates_root": str(self._templates_root)},
            ) from e

        try:
            xml: str = tmpl.render(**context)
        except jinja2.UndefinedError as e:
            log.warning(
                "template_missing_variable",
                template=template_name,
                context=context,
                error=str(e),
            )
            raise ValidationError(
                "Missing variable while rendering policy template",
                details={"template": template_name, "error": str(e)},
            ) from e

        return xml.strip()


# ---------------- helpers: loading & normalizing ----------------

def _canonical(s: str) -> str:
    return (s or "").strip()

def _normalize_entries(value: Any) -> List[Dict[str, Any]]:
    if not value:
        return []
    if not isinstance(value, list):
        raise ValidationError("Policy list must be a list", details={"value": value})
    for e in value:
        if not isinstance(e, dict):
            raise ValidationError("Policy entry must be an object", details={"entry": e})
    return value

def _load_env_overrides(system_name: Optional[str]) -> Dict[str, Any]:
    """
    Load env overrides YAML from <envs_dir>/<env>/<system>.yaml if present.
    envs_dir defaults to sibling of systems_dir: config/env
    """
    if not system_name:
        return {}
    sys_dir = Path(settings.systems_dir).resolve()
    envs_dir = getattr(settings, "envs_dir", None)
    env_root = Path(envs_dir) if envs_dir else sys_dir.parent.joinpath("env")
    path = env_root.joinpath(settings.apigee_env, f"{system_name}.yaml")
    if not path.exists():
        log.debug("env_overrides_not_found", path=str(path))
        return {}
    try:
        with open(path, "r", encoding="utf-8") as f:
            y = yaml.safe_load(f) or {}
        log.info("env_overrides_loaded", system=system_name, env=settings.apigee_env, path=str(path))
        return y
    except Exception as e:
        log.warning("env_overrides_failed", path=str(path), error=str(e))
        return {}

# ---------------- helpers: merging ----------------

def _merge_three_layers(
    catalog: Dict[str, Dict[str, Any]],
    default_layer: Dict[str, Any],
    env_layer: Dict[str, Any],
    attach_layer: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Merge DEFAULT < ENV < ATTACH for params dicts using per-key semantics from catalog.
    - Scalars: replace
    - Dicts: shallow merge (later overrides earlier)
    - Lists: follow catalog.merge (replace|append_unique|prepend_unique). Also resolve YAML directives.
    """
    # Start with empty; fold in three layers
    result: Dict[str, Any] = {}

    # We also collect directives like key__append from all layers and apply at the end.
    directives: Dict[str, Dict[str, List[Any]]] = {}

    def fold(src: Dict[str, Any]):
        for key, val in (src or {}).items():
            if "__" in key and key.split("__", 1)[1] in ("append", "prepend", "replace", "remove"):
                k, op = key.split("__", 1)
                directives.setdefault(k, {}).setdefault(op, [])
                directives[k][op].extend(_as_list(val))
                continue

            spec = catalog.get(key, {})
            typ = (spec.get("type") or _infer_type(val))
            merge_mode = spec.get("merge", "replace")

            if typ == "list":
                base = _as_list(result.get(key))
                incoming = _as_list(val)
                if base is None:
                    base = []
                if incoming is None:
                    incoming = []

                if merge_mode == "replace":
                    result[key] = list(incoming)
                elif merge_mode == "append_unique":
                    result[key] = _append_unique(base, incoming)
                elif merge_mode == "prepend_unique":
                    result[key] = _prepend_unique(base, incoming)
                else:  # fallback to replace
                    result[key] = list(incoming)
            elif typ == "dict" and isinstance(val, dict):
                dest = result.get(key, {})
                if not isinstance(dest, dict):
                    dest = {}
                tmp = dict(dest)
                tmp.update(val)
                result[key] = tmp
            else:
                result[key] = val

    # Apply layers
    for layer in (default_layer, env_layer, attach_layer):
        fold(layer)

    # Apply directives after base merges
    for k, ops in directives.items():
        spec = catalog.get(k, {})
        typ = spec.get("type", "list")
        norm = spec.get("normalize")
        current = _as_list(result.get(k)) or []
        if "replace" in ops:
            current = _as_list(ops["replace"])
        if "prepend" in ops:
            current = _prepend_unique(current, _as_list(ops["prepend"]))
        if "append" in ops:
            current = _append_unique(current, _as_list(ops["append"]))
        if "remove" in ops:
            current = [x for x in current if x not in set(_as_list(ops["remove"]))]
        # normalize & dedupe
        if typ == "list":
            current = _normalize_list(current, norm)
        result[k] = current

    # Normalize lists from non-directive merges too
    for k, v in list(result.items()):
        spec = catalog.get(k, {})
        if spec.get("type") == "list":
            result[k] = _normalize_list(_as_list(v) or [], spec.get("normalize"))

    return result


def _apply_input_overlay(
    catalog: Dict[str, Dict[str, Any]],
    merged: Dict[str, Any],
    policy_input: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Apply INPUT layer to an already-merged dict, respecting catalog semantics and list ops.
    INPUT can provide:
      - Scalars (replace)
      - Lists (treated as 'replace' unless catalog says append_unique)
      - Dict list-ops: {append:[], prepend:[], replace:[], remove:[]}
    """
    out = dict(merged)

    for key, val in (policy_input or {}).items():
        spec = catalog.get(key, {})
        typ = spec.get("type", _infer_type(val))
        norm = spec.get("normalize")
        default_list_mode = spec.get("merge", "replace")

        if typ == "list":
            base = _as_list(out.get(key)) or []
            # Dict with ops?
            if isinstance(val, dict):
                current = list(base)
                if "replace" in val:
                    current = _as_list(val.get("replace"))
                if "prepend" in val:
                    current = _prepend_unique(current, _as_list(val.get("prepend")))
                if "append" in val:
                    current = _append_unique(current, _as_list(val.get("append")))
                if "remove" in val:
                    current = [x for x in current if x not in set(_as_list(val.get("remove")))]
                out[key] = _normalize_list(current, norm)
            else:
                # plain list input: obey default strategy
                incoming = _as_list(val) or []
                if default_list_mode == "append_unique":
                    out[key] = _normalize_list(_append_unique(base, incoming), norm)
                elif default_list_mode == "prepend_unique":
                    out[key] = _normalize_list(_prepend_unique(base, incoming), norm)
                else:  # replace
                    out[key] = _normalize_list(incoming, norm)
        elif typ == "dict" and isinstance(val, dict):
            tmp = dict(out.get(key) or {})
            tmp.update(val)
            out[key] = tmp
        else:
            out[key] = val

    return out


# ---------------- helpers: rendering params ----------------

def _render_params_dict(env: jinja2.Environment, params: Dict[str, Any], ctx: Dict[str, Any]) -> Dict[str, Any]:
    def _render(val: Any) -> Any:
        if isinstance(val, str):
            t = env.from_string(val)
            return t.render(**ctx)
        elif isinstance(val, dict):
            return {k: _render(v) for k, v in val.items()}
        elif isinstance(val, list):
            return [_render(v) for v in val]
        return val
    return _render(params)


# ---------------- helpers: types & lists ----------------

def _infer_type(val: Any) -> str:
    if isinstance(val, list):
        return "list"
    if isinstance(val, bool):
        return "bool"
    if isinstance(val, int):
        return "int"
    if isinstance(val, dict):
        return "dict"
    return "string"

def _as_list(val: Any) -> Optional[List[Any]]:
    if val is None:
        return None
    if isinstance(val, list):
        return list(val)
    return [val]

def _append_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out = list(base)
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    return out

def _prepend_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out: List[Any] = []
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    out.extend(base)
    return out

def _normalize_list(items: List[Any], mode: Optional[str]) -> List[Any]:
    if not mode:
        # ensure uniqueness while preserving order
        return _append_unique([], items)
    if mode == "lower":
        normalized = [str(x).lower() for x in items]
        return _append_unique([], normalized)
    # 'exact' or unknown → unique preserve order
    return _append_unique([], items)


# ---------------- helpers: input extraction ----------------

def _extract_policy_input(policy_label: str, input_vars: Dict[str, Any]) -> Dict[str, Any]:
    """
    Combine general inputs into per-policy inputs:
      - Generic overrides: input.policy.<PolicyName> (structured list ops)
      - Convenience mappings:
          Quota.allow          <- input.quota
          Spike-Arrest.rate    <- input.spikeArrest
          CORS.allow_methods   <- input.methods (+ maybe oas.methods if present)
          CORS.allow_headers   <- input.headers (+ oas.request_headers)
          CORS.expose_headers  <- oas.response_headers
    """
    out: Dict[str, Any] = {}

    # Structured per-policy map from input: { policy: { key: {append/prepend/replace/remove} } }
    policy_map = (input_vars.get("policy") or {}) if isinstance(input_vars, dict) else {}
    if isinstance(policy_map.get(policy_label), dict):
        out.update(policy_map[policy_label])

    # Convenience overlays
    if policy_label == "Quota":
        if "quota" in input_vars:
            out["allow"] = input_vars["quota"]

    if policy_label == "Spike-Arrest":
        if "spikeArrest" in input_vars:
            out["rate"] = input_vars["spikeArrest"]

    if policy_label == "CORS":
        # caller may set methods/headers; treat as append_unique by default
        if "methods" in input_vars:
            out.setdefault("allow_methods", {})  # dict ops
            out["allow_methods"].setdefault("append", [])
            out["allow_methods"]["append"] += _as_list(input_vars["methods"]) or []

        if "headers" in input_vars:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(input_vars["headers"]) or []

        # OAS-derived headers
        oas = input_vars.get("oas") or {}
        if "request_headers" in oas:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(oas["request_headers"]) or []
        if "response_headers" in oas:
            out.setdefault("expose_headers", {})
            out["expose_headers"].setdefault("append", [])
            out["expose_headers"]["append"] += _as_list(oas["response_headers"]) or []

    return out