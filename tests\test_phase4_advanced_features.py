#!/usr/bin/env python3
"""
Test script for Phase 4: Advanced Features
- Enhanced feature flag system
- Conditional policy attachment
- Configuration validation tools
- Advanced flow generation
- Runtime policy evaluation
"""

import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.feature_flags import FeatureFlagEvaluator, is_feature_enabled
from app.services.conditional_policies import ConditionalPolicyAttacher, PolicyCondition, ConditionalPolicy
from app.services.config_validator import ConfigurationValidator, SystemConfigValidator
from app.services.policy_builder import PolicyBuilder
from app.config.loader import ConfigLoader


def test_feature_flag_system():
    """Test the enhanced feature flag system"""
    print("Testing Enhanced Feature Flag System...")
    
    try:
        # Test basic feature flag evaluation
        evaluator = FeatureFlagEvaluator(env_name="test", system_name="payments")
        
        # Test simple boolean flags
        if evaluator.is_enabled("advanced_security") is False:
            print("  ✓ Simple boolean flag evaluation works")
        else:
            print("  ❌ Simple boolean flag evaluation failed")
            return False
        
        # Test environment-specific flags
        prod_evaluator = FeatureFlagEvaluator(env_name="prod", system_name="payments")
        if prod_evaluator.is_enabled("security_headers"):
            print("  ✓ Environment-specific flag evaluation works")
        else:
            print("  ❌ Environment-specific flag evaluation failed")
            return False
        
        # Test conditional flags
        context = {
            "methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
            "security_policies": ["VerifyApiKey"],
            "method_count": 5  # This should trigger the smart_caching flag
        }

        if evaluator.is_enabled("smart_caching", context):
            print("  ✓ Conditional flag evaluation works")
        else:
            print("  ❌ Conditional flag evaluation failed")
            return False
        
        # Test convenience function
        if is_feature_enabled("config_validation", "test", "payments"):
            print("  ✓ Convenience function works")
        else:
            print("  ❌ Convenience function failed")
            return False
        
        # Test flag value retrieval
        value = evaluator.get_flag_value("advanced_security", "default_value")
        if value == False:  # The flag is set to false in config
            print("  ✓ Flag value retrieval works")
        else:
            print("  ❌ Flag value retrieval failed")
            return False
        
        print("✅ Enhanced Feature Flag System test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Feature Flag System test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_conditional_policy_attachment():
    """Test conditional policy attachment system"""
    print("\nTesting Conditional Policy Attachment...")
    
    try:
        attacher = ConditionalPolicyAttacher()
        
        # Test context for evaluation
        context = {
            "env_name": "prod",
            "system_name": "payments",
            "methods": ["GET", "POST", "PUT"],
            "content_types": ["application/json"],
            "security_policies": ["VerifyApiKey"],
            "features": {"advanced_security": True, "json_threat_protection": True}
        }
        
        # Get applicable policies
        applicable_policies = attacher.get_applicable_policies(context, "preflow")
        
        if len(applicable_policies) > 0:
            print(f"  ✓ Found {len(applicable_policies)} applicable conditional policies")
            
            # Check if JSON threat protection is included
            json_threat_found = any(p.get("policy") == "JSONThreatProtection" for p in applicable_policies)
            if json_threat_found:
                print("  ✓ JSONThreatProtection correctly attached for JSON content")
            else:
                print("  ❌ JSONThreatProtection should be attached for JSON content")
                return False
        else:
            print("  ❌ No conditional policies found")
            return False
        
        # Test policy condition evaluation
        condition = PolicyCondition("environment", environments=["prod", "staging"])
        if condition.evaluate(context):
            print("  ✓ Environment condition evaluation works")
        else:
            print("  ❌ Environment condition evaluation failed")
            return False
        
        # Test feature flag condition (use a flag that's enabled)
        feature_condition = PolicyCondition("feature_flag", flag="json_threat_protection")
        if feature_condition.evaluate(context):
            print("  ✓ Feature flag condition evaluation works")
        else:
            print("  ❌ Feature flag condition evaluation failed")
            return False
        
        print("✅ Conditional Policy Attachment test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Conditional Policy Attachment test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_configuration_validation():
    """Test configuration validation tools"""
    print("\nTesting Configuration Validation Tools...")
    
    try:
        validator = SystemConfigValidator()
        
        # Test valid configuration
        valid_config = {
            "metadata": {
                "system": "test-system",
                "owner_team": "test-team",
                "description": "Test system"
            },
            "policy_defaults": {
                "SpikeArrest": {"rate": "100pm"},
                "Quota": {"allow": "1000pm", "timeunit": "minute"},
                "VerifyApiKey": {"keyref": "request.header.x-api-key"}
            },
            "flows": {
                "preflow": {
                    "request": ["VerifyApiKey", "SpikeArrest"],
                    "response": []
                },
                "per_operation": {
                    "request": ["Quota"],
                    "response": []
                }
            },
            "target": {
                "baseurl": "https://api.example.com",
                "connect_timeout_ms": 5000,
                "read_timeout_ms": 30000
            },
            "features": {
                "advanced_security": True,
                "caching_enabled": False
            }
        }
        
        result = validator.validate_system_config(valid_config, "test-system")
        
        if result.is_valid:
            print("  ✓ Valid configuration passes validation")
        else:
            print(f"  ❌ Valid configuration failed validation: {result.errors}")
            return False
        
        # Test invalid configuration
        invalid_config = {
            "metadata": {
                "system": "Invalid System Name!",  # Invalid characters
                # Missing owner_team
            },
            "policy_defaults": {
                "SpikeArrest": {"rate": "invalid-rate"},  # Invalid rate format
                "Quota": {"timeunit": "invalid-unit"}  # Invalid time unit
            },
            "target": {
                "baseurl": "not-a-valid-url",  # Invalid URL
                "connect_timeout_ms": -1000  # Invalid timeout
            }
        }
        
        invalid_result = validator.validate_system_config(invalid_config, "invalid-system")
        
        if not invalid_result.is_valid and len(invalid_result.errors) > 0:
            print(f"  ✓ Invalid configuration correctly rejected with {len(invalid_result.errors)} errors")
        else:
            print("  ❌ Invalid configuration should be rejected")
            return False
        
        # Test configuration validator
        config_validator = ConfigurationValidator()
        
        # This would normally validate all configs in a directory
        # For testing, we'll just verify the validator was created
        if config_validator.system_validator and config_validator.template_validator:
            print("  ✓ Configuration validator initialized correctly")
        else:
            print("  ❌ Configuration validator initialization failed")
            return False
        
        print("✅ Configuration Validation Tools test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration Validation Tools test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_advanced_policy_builder_integration():
    """Test integration of advanced features with policy builder"""
    print("\nTesting Advanced Policy Builder Integration...")
    
    try:
        builder = PolicyBuilder()
        loader = ConfigLoader()
        cfgs = loader.load(system_name="reflect")
        
        # Test with advanced features enabled
        input_vars = {
            "methods": ["GET", "POST", "PUT"],
            "headers": ["Authorization", "Content-Type"],
            "policy_params": {
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "100pm"},
                "Quota": {"allow": "1000pm"}
            },
            "features": {
                "conditional_policies": True,
                "advanced_security": True,
                "json_threat_protection": True
            }
        }
        
        # Set environment to trigger conditional policies
        import os
        original_env = os.environ.get('APIGEE_ENV', 'test')
        os.environ['APIGEE_ENV'] = 'prod'
        
        try:
            policies, attach_plan = builder.render_policies(
                cfgs.system_cfg,
                input_vars,
                system_name="reflect"
            )
            
            print(f"  ✓ Rendered {len(policies)} policies with advanced features")
            
            # Check if conditional policies were added
            policy_names = [p.name for p in policies]
            
            # Look for threat protection policies that should be conditionally attached
            threat_protection_found = any("ThreatProtection" in name for name in policy_names)
            if threat_protection_found:
                print("  ✓ Conditional threat protection policies attached")
            else:
                print("  ⚠️ No conditional threat protection policies found (may be expected)")
            
            # Check attachment plan
            total_attached = (len(attach_plan.preflow_request) + 
                            len(attach_plan.preflow_response) +
                            len(attach_plan.perflow_request) + 
                            len(attach_plan.perflow_response))
            
            if total_attached > 0:
                print(f"  ✓ Attachment plan created with {total_attached} policy attachments")
            else:
                print("  ❌ No policies in attachment plan")
                return False
            
        finally:
            os.environ['APIGEE_ENV'] = original_env
        
        print("✅ Advanced Policy Builder Integration test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Advanced Policy Builder Integration test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_runtime_policy_evaluation():
    """Test runtime policy evaluation capabilities"""
    print("\nTesting Runtime Policy Evaluation...")
    
    try:
        # Test feature flag evaluation with different contexts
        evaluator = FeatureFlagEvaluator(env_name="prod", system_name="payments")
        
        # Context for a high-traffic API
        high_traffic_context = {
            "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
            "security_policies": ["VerifyApiKey", "Quota"],
            "api_characteristics": {"method_count": 6, "has_security": True}
        }
        
        # Context for a simple API
        simple_context = {
            "methods": ["GET"],
            "security_policies": ["VerifyApiKey"],
            "api_characteristics": {"method_count": 1, "has_security": True}
        }
        
        # Test smart caching flag (should be enabled for high-traffic APIs)
        high_traffic_caching = evaluator.is_enabled("smart_caching", high_traffic_context)
        simple_caching = evaluator.is_enabled("smart_caching", simple_context)
        
        if high_traffic_caching and not simple_caching:
            print("  ✓ Smart caching correctly enabled only for high-traffic APIs")
        else:
            print(f"  ⚠️ Smart caching evaluation: high_traffic={high_traffic_caching}, simple={simple_caching}")
        
        # Test conditional policy attachment with different contexts
        attacher = ConditionalPolicyAttacher()
        
        high_traffic_policies = attacher.get_applicable_policies({
            **high_traffic_context,
            "env_name": "prod",
            "system_name": "payments",
            "content_types": ["application/json"],
            "features": {"json_threat_protection": True}
        })
        
        simple_policies = attacher.get_applicable_policies({
            **simple_context,
            "env_name": "prod", 
            "system_name": "payments",
            "content_types": ["application/json"],
            "features": {"json_threat_protection": True}
        })
        
        if len(high_traffic_policies) >= len(simple_policies):
            print(f"  ✓ More policies attached for complex APIs ({len(high_traffic_policies)} vs {len(simple_policies)})")
        else:
            print(f"  ⚠️ Policy attachment: high_traffic={len(high_traffic_policies)}, simple={len(simple_policies)}")
        
        print("✅ Runtime Policy Evaluation test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Runtime Policy Evaluation test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all Phase 4 tests"""
    print("=" * 60)
    print("PHASE 4: ADVANCED FEATURES TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_feature_flag_system,
        test_conditional_policy_attachment,
        test_configuration_validation,
        test_advanced_policy_builder_integration,
        test_runtime_policy_evaluation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PHASE 4 TESTS PASSED!")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
