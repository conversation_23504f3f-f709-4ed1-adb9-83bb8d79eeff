<Javascript name="{{ name }}"
{% if params.enabled is defined %} enabled="{{ 'true' if params.enabled else 'false' }}"{% endif %}
{% if params.continueOnError is defined %} continueOnError="{{ 'true' if params.continueOnError else 'false' }}"{% endif %}
{% if params.async is defined %} async="{{ 'true' if params.async else 'false' }}"{% endif %}
{% if params.timeLimit is defined %} timeLimit="{{ params.timeLimit }}"{% endif %}>
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  {% if params.properties %}
  <Properties>
    {% for prop in params.properties %}
    <Property name="{{ prop.name }}">{{ prop.value }}</Property>
    {% endfor %}
  </Properties>
  {% endif %}
  {% if params.resource_url %}
  <ResourceURL>{{ params.resource_url }}</ResourceURL>
  {% elif params.source_code %}
  <SourceCode>{{ params.source_code }}</SourceCode>
  {% endif %}
  {% if params.include_url %}
  <IncludeURL>{{ params.include_url }}</IncludeURL>
  {% endif %}
</Javascript>
