{"swagger": "2.0", "info": {"description": "Neo Banking - Digital Mobile Banking", "version": "1.0.0", "title": "Neobanking Card Experience API Documentation", "termsOfService": "https://developer.arabbank.com/api-catalogue", "contact": {"name": "Arab-Bank", "url": "http://www.arabbank.jo", "email": "<EMAIL>"}, "license": {"name": "Arab-Bank API license 1.0", "url": "https://developer.arabbank.com/api-catalogue"}}, "host": "*************", "tags": [{"name": "Credit Card Transactions Controller", "description": "Credit Card Spend Analysis Controller"}, {"name": "card-dashboard", "description": "Dashboard Controller"}, {"name": "card-tpp", "description": "Card TPP Controller"}, {"name": "cc-issuance", "description": "Credit Card Issuance Controller"}, {"name": "cc-management", "description": "Credit Card Management Controller"}, {"name": "cc-payment", "description": "Credit Card Payment Controller"}, {"name": "tap-to-pay", "description": "Tap To Pay Controller"}], "paths": {"/neobanking/card-experience/v1/cards/dashboard": {"get": {"tags": ["card-dashboard"], "summary": "Cards dashboard", "description": "Cards dashboard", "operationId": "getAllCardDetailsUsingGET", "produces": ["*/*"], "parameters": [{"name": "card-type", "in": "query", "description": "card-type", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "b97e077e-e598-45f5-b7d2-818f0f044d7e"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "22200ae5-8668-4e05-8910-f39e86c19550"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CardDetailsResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/cards/dashboard/v2": {"get": {"tags": ["card-dashboard"], "summary": "Cards dashboard V2", "description": "Cards dashboard V2", "operationId": "getAllCardDetailsV2UsingGET", "produces": ["*/*"], "parameters": [{"name": "card-type", "in": "query", "description": "card-type", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "388074fe-2cb1-472a-bef8-97e45519f7f0"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "ced5e792-2358-40a3-9126-c4eec4157f18"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CardDetailsResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/activate": {"post": {"tags": ["cc-management"], "summary": "Activate Credit Card", "description": "Activate Credit Card", "operationId": "activateCreditCardUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ActivateCardRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "a42c3abe-1f55-4b48-9847-e3a62b341d20"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9a8e17e2-f8a6-45a7-97d5-a25e36e634ac"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/basic-details": {"get": {"tags": ["cc-management"], "summary": "Credit card details", "description": "Credit card details", "operationId": "getCreditCardDetailsUsingGET", "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "ed544d0b-743f-4188-8e93-ed13fe5dd708"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "e8212500-dd9d-4ce7-909f-20783c580063"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BasicCreditCardDetailsWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/control": {"post": {"tags": ["cc-management"], "summary": "Maintain Online Payments Credit Card", "description": "Maintain Online Payments Credit Card", "operationId": "maintainCreditCardUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "command", "description": "command", "required": true, "schema": {"$ref": "#/definitions/MaintainCardCommand"}}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "71d58325-d932-4e44-8b63-9148e50af28d"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "d8bb98df-cd82-45ec-98fd-4cb560a03c01"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/defrost": {"post": {"tags": ["cc-management"], "summary": "Defrost Credit Card", "description": "Defrost Credit Card", "operationId": "defrostCreditCardUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "command", "description": "command", "required": true, "schema": {"$ref": "#/definitions/CardStatusChangeCommand"}}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "42c0d8c1-0c2e-43cf-a8ae-a7592aac7eee"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9e399d3e-e060-4ec7-a180-75a869cae250"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/freeze": {"post": {"tags": ["cc-management"], "summary": "Freeze Credit Card", "description": "Freeze Credit Card", "operationId": "freezeCreditCardUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "command", "description": "command", "required": true, "schema": {"$ref": "#/definitions/CardStatusChangeCommand"}}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "5f88bed5-8adc-43be-8298-6f5362bf90f7"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9036b10c-ee95-4570-8326-ce7e3d1e285a"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/full-details": {"get": {"tags": ["cc-management"], "summary": "Credit card full details", "description": "Credit card full details", "operationId": "getCreditCardFullDetailsUsingGET", "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "c34147ac-ee80-4860-ad47-f63cb0a70d72"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "0af9e2d3-30c3-4c96-87ce-dc017e490d5b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/FullCreditCardDetailsWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/declaration/income-expense": {"post": {"tags": ["cc-issuance"], "summary": "Update income Details", "description": "Update income Details", "operationId": "updateIncomeDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/IncomeAndExpenseRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "bb6c4a14-9ce9-4e23-8252-edbb1fd1b25f"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "ffcf9bd0-6ec5-4ced-8e52-be53ff368606"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/IncomeAndExpenseResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/initiation": {"post": {"tags": ["cc-issuance"], "summary": "CC issuance initiation", "description": "CC issuance initiation", "operationId": "issuanceInitiationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/InitiationRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "570d2c20-3146-4482-ae74-68b774ab20b4"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "6c28980e-a60f-41f2-af09-8e3aa234b99e"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/limit": {"get": {"tags": ["cc-issuance"], "summary": "CC View Limit", "description": "CC View Limit", "operationId": "viewLimitUsingGET", "produces": ["application/json"], "parameters": [{"name": "ccReference", "in": "header", "description": "ccReference", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "89e442e9-370a-4f34-8638-87e30232a9e3"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "01b8046d-a315-43a8-a4b1-c945723f30a0"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ViewLimitResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}, "put": {"tags": ["cc-issuance"], "summary": "Update Card limit", "description": "Update Card Limit", "operationId": "limitUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "ccReference", "in": "header", "description": "ccReference", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "status", "in": "header", "description": "status", "required": true, "type": "string", "enum": ["ACCEPT", "REJECT"]}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "773118d1-1006-44e3-89df-c800e75164a9"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "6b9903d1-7094-424b-8f4a-d71b5b3b77bf"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/profession": {"post": {"tags": ["cc-issuance"], "summary": "CC issuance profession", "description": "CC issuance profession", "operationId": "customerProfessionDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ProfessionDetailsRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "21d4337b-fe55-42ef-b6c9-27470bd26901"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "19f096dc-f0b2-4a81-85bd-a416168f4884"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CustomerProfessionDetailsResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/replace": {"post": {"tags": ["cc-issuance"], "summary": "CC re-issue initiation", "description": "CC re-issue initiation", "operationId": "replaceCardUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ReplaceRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "be345fe0-6568-43ad-90f9-3df0e7bf3eda"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "be9a22e2-1268-449f-90d6-6543bf6cd86f"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/social-security/send-otp": {"post": {"tags": ["cc-issuance"], "summary": "SSC send otp code", "description": "SSC send otp code", "operationId": "sscSendOtpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/CcSscSendOtpRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "71e3acd3-6950-407c-bdd4-b5585cb0abd4"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "f14d88cc-0888-4111-bcee-0368d7e65204"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CcSscSendOtpResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/social-security/validate-otp": {"post": {"tags": ["cc-issuance"], "summary": "SSC validate otp code", "description": "SSC validate otp code", "operationId": "sscValidateOtpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/CcSscValidateOtpRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "ebafad81-fb14-49fb-9f37-358449e443f1"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "45a52b2e-2351-448d-b77c-d8316907c4fe"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CcSscValidateOtpResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/validation": {"post": {"tags": ["cc-issuance"], "summary": "CC issuance validation", "description": "CC issuance validation", "operationId": "issuanceValidationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "5318ea5a-0f78-408d-a182-98978b69d685"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "39ba5d32-e893-47d5-89f2-b8f48b15ff6b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/IssuanceValidationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/issuance/validation/v2": {"post": {"tags": ["cc-issuance"], "summary": "CC issuance validation v2", "description": "CC issuance validation v2", "operationId": "issuanceValidationV2UsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "ccReference", "in": "header", "description": "ccReference", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "1acef9bb-c372-4393-9644-131c9689cb87"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "e784ecb3-c8c0-4d9f-9cae-f3b2c44d783b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/IssuanceValidationResponseV3Wrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/payment-option": {"put": {"tags": ["cc-management"], "summary": "Update Payment Option", "description": "Update Payment Options", "operationId": "paymentOptionUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "command", "description": "command", "required": true, "schema": {"$ref": "#/definitions/CardStatusChangeCommand"}}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "paymentOptionId", "in": "header", "description": "paymentOptionId", "required": true, "type": "integer", "format": "int32"}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "9e2afae5-6b20-4efd-a33f-2583661e2304"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "5550b90d-e8cd-411d-a259-1fe055a27a12"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/StatusResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/payment/analysis": {"post": {"tags": ["cc-payment"], "summary": "Card Bill Payment Analysis", "description": "Card Bill Payment Analysis", "operationId": "paymentAnalysisUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/CardPaymentRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "5b72c12e-76db-4855-a413-8e2fd749b4cb"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "a8c6e330-8e51-4c93-bfe3-09eadde94d34"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PaymentAnalysisResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/payment/enriched-transactions": {"get": {"tags": ["cc-payment"], "summary": "Credit Card Enrich Transactions", "description": "Credit Card Enrich Transactions", "operationId": "ccPaymentTransactionsUsingGET", "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": false, "type": "string"}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "page", "in": "query", "description": "page", "required": false, "type": "integer", "default": 0, "format": "int32"}, {"name": "size", "in": "query", "description": "size", "required": false, "type": "integer", "default": 15, "format": "int32"}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "toDate", "in": "query", "description": "toDate", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "a6178eea-c872-4f91-8d10-53e079ac378a"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "a79cdcf7-6bdc-40c3-a7f6-a1da6d08f983"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/EnrichedTransactionResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/payment/initiation": {"post": {"tags": ["cc-payment"], "summary": "Card Bill Payment Initiation", "description": "Card Bill Payment Initiation", "operationId": "paymentInitiationUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/CardPaymentRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "a94cc1da-828b-415f-a4f1-a0070c2330a5"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "cb2392cf-7bfe-4491-8771-73b30599545b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/PaymentInitiationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/payment/transactions": {"get": {"tags": ["cc-payment"], "summary": "Card Bill Payment Initiation", "description": "Card Bill Payment Initiation", "operationId": "paymentTransactionsUsingGET", "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": false, "type": "string"}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "page", "in": "query", "description": "page", "required": false, "type": "integer", "default": 1, "format": "int32"}, {"name": "size", "in": "query", "description": "size", "required": false, "type": "integer", "default": 10, "format": "int32"}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "startSequenceNumber", "in": "query", "description": "startSequenceNumber", "required": false, "type": "string"}, {"name": "toDate", "in": "query", "description": "toDate", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "6878fda8-904f-4aaf-b9ec-b7b498937865"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "6c81b52c-3644-4012-82ed-cfe843106291"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/CreditCardPaymentResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/pin": {"post": {"tags": ["cc-management"], "summary": "View Pin", "description": "View Pin", "operationId": "viewPinUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ViewPinRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "85676b48-1256-40e8-adf4-b0862b1fcb52"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "5b70ec68-d2a0-4911-983b-fc092c0ac6d4"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ViewPinResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/spend-analysis/categories": {"get": {"tags": ["Credit Card Transactions Controller"], "summary": "Spend Categories", "description": "spendCategories", "operationId": "spendCategoriesUsingGET", "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": false, "type": "string", "format": "date"}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "toDate", "in": "query", "description": "toDate", "required": false, "type": "string", "format": "date"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "973b95cb-a4e0-450e-82c8-e006238da0a6"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9e29e39f-4ae1-4c2e-ba25-166efb45e098"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/CategoryResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/spend-analysis/categories/{category-id}/merchants": {"get": {"tags": ["Credit Card Transactions Controller"], "summary": "Category Merchants", "description": "categoryMerchants", "operationId": "categoryMerchantsUsingGET", "produces": ["application/json"], "parameters": [{"name": "category-id", "in": "path", "description": "category-id", "required": true, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": false, "type": "string", "format": "date"}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "toDate", "in": "query", "description": "toDate", "required": false, "type": "string", "format": "date"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "6d5ab704-23d3-484d-8dfe-c094b5f6f718"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "426b9888-6864-41dc-bd02-eeffbc989efe"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/CategoryIdMerchantResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/credit-card/spend-analysis/categories/{category-id}/merchants/{merchant-id}/transactions": {"get": {"tags": ["Credit Card Transactions Controller"], "summary": "Merchants Transactions", "description": "merchantTransactions", "operationId": "merchantTransactionsUsingGET", "produces": ["application/json"], "parameters": [{"name": "category-id", "in": "path", "description": "category-id", "required": true, "type": "string"}, {"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "fromDate", "in": "query", "description": "fromDate", "required": false, "type": "string", "format": "date"}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "merchant-id", "in": "path", "description": "merchant-id", "required": true, "type": "string"}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "toDate", "in": "query", "description": "toDate", "required": false, "type": "string", "format": "date"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "bb50a6eb-20b1-475a-bb9d-0f9e63bd30d6"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "5de1677a-d907-4069-8682-9b1bd89c309b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/MerchantIdTransactionResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tap2pay/registration/analysis": {"post": {"tags": ["tap-to-pay"], "summary": "TapToPay", "description": "TapToPay", "operationId": "tapToPayResponseUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/TapToPayAnalysisRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "50629590-21a7-4dbc-a974-808ad99da6b8"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "5298d8fb-371a-4406-a073-dd2b4fc69098"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/TapToPayAnalysisResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tap2pay/registration/initiation": {"post": {"tags": ["tap-to-pay"], "summary": "TapToPay", "description": "TapToPay", "operationId": "TapToPayInitiationResponseUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/TapToPayInitiationRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "506a8836-2e2c-4ce1-bd24-ae6caa26bbfb"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "70f8e4aa-4911-49c0-a3fc-52eabb7704a0"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/TapToPayInitiationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tap2pay/token": {"get": {"tags": ["tap-to-pay"], "summary": "TapToPay", "description": "TapToPay", "operationId": "TokenGetResponseUsingGET", "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "a6c422c8-a227-4da0-bb76-08f8fb6c5b23"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "59b19e6e-33f4-4414-ba86-2a83adf7686e"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/TokenGetResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tpp/mobile/events": {"post": {"tags": ["card-tpp"], "summary": "mobile events", "description": "mobile events", "operationId": "mobileEventUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/MobileEventRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "96332dd9-d3f3-4890-9c7c-ebda36b5067d"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "acd5adce-225b-4d0f-9ff1-fa2a297fa21f"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/StringWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tpp/tap-to-pay/default": {"put": {"tags": ["card-tpp"], "summary": "Tap to pay default card set", "description": "Tap to pay default card set", "operationId": "tapToPayDefaultCardUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "query", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/TapToPayDefaultCardRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "c90e32da-cfff-4734-aaa9-e0c09b23ef04"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "aa4a6d63-3f4d-4bea-9a5b-90962c485923"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/TapToPayDefaultCardResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/card-experience/v1/tpp/thales/card-registration": {"post": {"tags": ["card-tpp"], "summary": "Thales force registration", "description": "Thales force registration", "operationId": "thalesCardRegistrationUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ThalesCardRegistrationRequest"}}, {"name": "source-id", "in": "header", "description": "Caller Source", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "a418b42b-964a-4169-8e63-e77ad0b4e98d"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "8a90316a-a314-49b5-a158-239cac5bd81d"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/ThalesCardRegistrationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}}, "definitions": {"ActivateCardRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "cvv": {"type": "string"}, "expiry": {"type": "string"}, "keyId": {"type": "string"}, "maskedCardNumber": {"type": "string"}}, "title": "ActivateCardRequest"}, "Amount": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}, "title": "Amount"}, "AmountDetails": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}, "title": "AmountDetails"}, "BasicCreditCardDetails": {"type": "object", "properties": {"cardNumber": {"type": "string"}, "cardVerificationValue": {"type": "string"}, "documentInfo": {"$ref": "#/definitions/DocumentResponse"}, "expiry": {"type": "string"}, "maskedCardNumber": {"type": "string"}, "nameOnCard": {"type": "string"}, "paymentOptionId": {"type": "number"}, "status": {"type": "string"}}, "title": "BasicCreditCardDetails"}, "BasicCreditCardDetailsDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/BasicCreditCardDetails"}}}, "title": "BasicCreditCardDetailsDataHolder"}, "BasicCreditCardDetailsWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BasicCreditCardDetailsDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BasicCreditCardDetailsWrapper"}, "CardBasicDetails": {"type": "object", "properties": {"cardUId": {"type": "string"}, "maskedCardNumber": {"type": "string"}, "nameOnCard": {"type": "string"}, "reIssueFlag": {"type": "boolean"}, "status": {"type": "string"}}, "title": "CardBasicDetails"}, "CardDetailsResponse": {"type": "object", "properties": {"consumerId": {"type": "string"}, "creditCard": {"$ref": "#/definitions/CreditCardDetails"}, "debitCard": {"$ref": "#/definitions/PhysicalCardDetails"}, "virtualCard": {"$ref": "#/definitions/CardBasicDetails"}}, "title": "CardDetailsResponse"}, "CardDetailsResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/CardDetailsResponse"}}}, "title": "CardDetailsResponseDataHolder"}, "CardDetailsResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CardDetailsResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CardDetailsResponseWrapper"}, "CardPaymentRequest": {"type": "object", "properties": {"maskedCardNumber": {"type": "string"}, "paymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentType": {"type": "string", "enum": ["DUE", "OTHER"]}}, "title": "CardPaymentRequest"}, "CardStatusChangeCommand": {"type": "object", "properties": {"ccReference": {"type": "string"}, "maskedCardNumber": {"type": "string"}, "reason": {"type": "string"}}, "title": "CardStatusChangeCommand"}, "CategoryDetails": {"type": "object", "properties": {"categoryColour": {"type": "string"}, "categoryIcon": {"type": "string"}, "categoryId": {"type": "string"}, "categoryName": {"type": "string"}, "overallSpendingPercent": {"type": "number"}, "totalSpendInCategory": {"$ref": "#/definitions/AmountDetails"}, "totalSpendInCategoryAmount": {"type": "string"}, "transactionCount": {"type": "integer", "format": "int32"}}, "title": "CategoryDetails"}, "CategoryIdMerchantResponse": {"type": "object", "properties": {"categoryColour": {"type": "string"}, "categoryIcon": {"type": "string"}, "categoryId": {"type": "string"}, "categoryName": {"type": "string"}, "merchantTransactionDetails": {"type": "array", "items": {"$ref": "#/definitions/MerchantIdDetails"}}, "overallSpendingPercent": {"type": "number"}, "totalSpendInCategory": {"$ref": "#/definitions/AmountDetails"}, "transactionCount": {"type": "integer", "format": "int32"}, "transactionEndDate": {"type": "string"}, "transactionStartDate": {"type": "string"}}, "title": "CategoryIdMerchantResponse"}, "CategoryIdMerchantResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CategoryIdMerchantResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CategoryIdMerchantResponseWrapper"}, "CategoryResponse": {"type": "object", "properties": {"categoriesDetails": {"type": "array", "items": {"$ref": "#/definitions/CategoryDetails"}}, "totalSpend": {"$ref": "#/definitions/AmountDetails"}, "transactionEndDate": {"type": "string"}, "transactionStartDate": {"type": "string"}}, "title": "CategoryResponse"}, "CategoryResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CategoryResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CategoryResponseWrapper"}, "CcSscSendOtpRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "status": {"type": "string"}}, "title": "CcSscSendOtpRequest"}, "CcSscSendOtpResponse": {"type": "object", "properties": {"results": {"$ref": "#/definitions/IssuanceValidationResponseV3"}}, "title": "CcSscSendOtpResponse"}, "CcSscSendOtpResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CcSscSendOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CcSscSendOtpResponseWrapper"}, "CcSscValidateOtpRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "otpCode": {"type": "string"}}, "title": "CcSscValidateOtpRequest"}, "CcSscValidateOtpResponse": {"type": "object", "properties": {"results": {"$ref": "#/definitions/IssuanceValidationResponseV3"}}, "title": "CcSscValidateOtpResponse"}, "CcSscValidateOtpResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CcSscValidateOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CcSscValidateOtpResponseWrapper"}, "CodeAndValue": {"type": "object", "properties": {"code": {"type": "string"}, "value": {"type": "string"}}, "title": "CodeAndValue"}, "CreditCardDetails": {"type": "object", "properties": {"businessErrorCode": {"type": "string"}, "cardUId": {"type": "string"}, "days": {"type": "integer", "format": "int32"}, "deliveryFee": {"type": "number"}, "lastApplicationDate": {"type": "string", "format": "date"}, "limit": {"$ref": "#/definitions/InstructedAmount"}, "maskedCardNumber": {"type": "string"}, "nameOnCard": {"type": "string"}, "onlinePayments": {"type": "string", "enum": ["DISABLE", "ENABLE"]}, "reApplicationDate": {"type": "string"}, "reApplicationDaysLeft": {"type": "integer", "format": "int32"}, "reIssueFlag": {"type": "boolean"}, "remainingBalance": {"$ref": "#/definitions/InstructedAmount"}, "status": {"type": "string"}}, "title": "CreditCardDetails"}, "CreditCardPaymentResponse": {"type": "object", "properties": {"narratives": {"type": "object", "additionalProperties": {"type": "string"}}, "sequenceNumber": {"type": "string"}, "transactionAmount": {"type": "number"}, "transactionCode": {"type": "string"}, "transactionCurrency": {"type": "string"}, "transactionDate": {"type": "string"}, "transactionIcon": {"type": "string"}, "transactionIndicator": {"type": "string"}, "transactionTitle": {"type": "string"}, "transactionType": {"type": "string"}, "valueDate": {"type": "string"}}, "title": "CreditCardPaymentResponse"}, "CreditCardPaymentResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/CreditCardPaymentResponse"}}}, "title": "CreditCardPaymentResponseDataHolder"}, "CreditCardPaymentResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CreditCardPaymentResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CreditCardPaymentResponseWrapper"}, "CurrentCommitments": {"type": "object", "properties": {"electronicDeviceObligations": {"$ref": "#/definitions/InstructedAmount"}, "furnitureObligations": {"$ref": "#/definitions/InstructedAmount"}, "loanGuarantees": {"$ref": "#/definitions/InstructedAmount"}, "otherCommitments": {"$ref": "#/definitions/InstructedAmount"}}, "title": "CurrentCommitments"}, "CurrentCommitments0": {"type": "object", "properties": {"electronicDeviceObligations": {"$ref": "#/definitions/Amount"}, "furnitureObligations": {"$ref": "#/definitions/Amount"}, "loanGuarantees": {"$ref": "#/definitions/Amount"}, "otherCommitments": {"$ref": "#/definitions/Amount"}}, "title": "CurrentCommitments0"}, "CustomerProfessionDetailsResponse": {"type": "object", "properties": {"ccReference": {"type": "string"}, "incomeExpenseDetails": {"$ref": "#/definitions/IncomeExpenseDetails"}, "status": {"type": "string"}}, "title": "CustomerProfessionDetailsResponse"}, "CustomerProfessionDetailsResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/CustomerProfessionDetailsResponse"}}}, "title": "CustomerProfessionDetailsResponseDataHolder"}, "CustomerProfessionDetailsResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CustomerProfessionDetailsResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "CustomerProfessionDetailsResponseWrapper"}, "DocumentInfo": {"type": "object", "properties": {"documentId": {"type": "string"}, "documentName": {"type": "string"}, "documentVersion": {"type": "string"}}, "title": "DocumentInfo"}, "DocumentResponse": {"type": "object", "properties": {"href": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "version": {"type": "string"}}, "title": "DocumentResponse"}, "EnrichedTransactionResponse": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "accountType": {"type": "string"}, "currentBalance": {"type": "number"}, "externalReference": {"type": "string"}, "googlePlaceId": {"type": "string"}, "narratives": {"type": "object", "additionalProperties": {"type": "string"}}, "transactionAmount": {"type": "number"}, "transactionCode": {"type": "string"}, "transactionCodeDescription": {"type": "string"}, "transactionCurrency": {"type": "string"}, "transactionDate": {"type": "string"}, "transactionDescription": {"type": "string"}, "transactionIcon": {"type": "string"}, "transactionId": {"type": "string"}, "transactionTitle": {"type": "string"}, "transactionType": {"type": "string"}, "valueDate": {"type": "string"}}, "title": "EnrichedTransactionResponse"}, "EnrichedTransactionResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/EnrichedTransactionResponse"}}}, "title": "EnrichedTransactionResponseDataHolder"}, "EnrichedTransactionResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/EnrichedTransactionResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "EnrichedTransactionResponseWrapper"}, "ErrorOfobject": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDetails": {"type": "object"}, "message": {"type": "string"}, "path": {"type": "string"}, "url": {"type": "string"}}, "title": "ErrorOfobject"}, "ErrorResponseDTO": {"type": "object", "properties": {"code": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorOfobject"}}, "id": {"type": "string"}, "message": {"type": "string"}, "url": {"type": "string"}}, "title": "ErrorResponseDTO"}, "FixedMonthlyIncome": {"type": "object", "properties": {"educationExpense": {"$ref": "#/definitions/Amount"}, "housingExpense": {"$ref": "#/definitions/Amount"}, "monthlyBills": {"$ref": "#/definitions/Amount"}, "otherExpense": {"$ref": "#/definitions/Amount"}}, "title": "FixedMonthlyIncome"}, "FixedMonthlyIncome0": {"type": "object", "properties": {"educationExpense": {"$ref": "#/definitions/InstructedAmount"}, "housingExpense": {"$ref": "#/definitions/InstructedAmount"}, "monthlyBills": {"$ref": "#/definitions/InstructedAmount"}, "otherExpense": {"$ref": "#/definitions/InstructedAmount"}}, "title": "FixedMonthlyIncome0"}, "FullCreditCardDetails": {"type": "object", "properties": {"cardLimit": {"$ref": "#/definitions/InstructedAmount"}, "cardName": {"type": "string"}, "cardType": {"type": "string"}, "cashWithdrawalFeeMinimum": {"$ref": "#/definitions/InstructedAmount"}, "cashWithdrawalFeePercentage": {"type": "number"}, "expiryDate": {"type": "string"}, "issueDate": {"type": "string"}, "minimumPaymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "overdueAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentDueDate": {"type": "string"}, "paymentOptionId": {"type": "number"}, "statementOutstandingBalance": {"$ref": "#/definitions/InstructedAmount"}, "totalUsedLimit": {"$ref": "#/definitions/InstructedAmount"}}, "title": "FullCreditCardDetails"}, "FullCreditCardDetailsDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/FullCreditCardDetails"}}}, "title": "FullCreditCardDetailsDataHolder"}, "FullCreditCardDetailsWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/FullCreditCardDetailsDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "FullCreditCardDetailsWrapper"}, "HateoasDTO": {"type": "object", "properties": {"href": {"type": "string"}, "method": {"type": "string"}, "rel": {"type": "string"}}, "title": "HateoasDTO"}, "IncomeAndExpenseRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "incomeExpenseDetails": {"$ref": "#/definitions/UserIncomeDetails"}, "status": {"type": "string"}}, "title": "IncomeAndExpenseRequest"}, "IncomeAndExpenseResponse": {"type": "object", "properties": {"ccReference": {"type": "string"}, "status": {"type": "string"}}, "title": "IncomeAndExpenseResponse"}, "IncomeAndExpenseResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/IncomeAndExpenseResponse"}}}, "title": "IncomeAndExpenseResponseDataHolder"}, "IncomeAndExpenseResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/IncomeAndExpenseResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "IncomeAndExpenseResponseWrapper"}, "IncomeDeclaration": {"type": "object", "properties": {"monthlySalary": {"$ref": "#/definitions/InstructedAmount"}}, "title": "IncomeDeclaration"}, "IncomeDeclaration0": {"type": "object", "properties": {"monthlySalary": {"$ref": "#/definitions/Amount"}}, "title": "IncomeDeclaration0"}, "IncomeExpenseDetails": {"type": "object", "properties": {"currentCommitments": {"$ref": "#/definitions/CurrentCommitments"}, "fixedMonthlyIncome": {"$ref": "#/definitions/FixedMonthlyIncome0"}, "incomeDeclaration": {"$ref": "#/definitions/IncomeDeclaration"}}, "title": "IncomeExpenseDetails"}, "InitiationRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "documentInfo": {"$ref": "#/definitions/DocumentInfo"}, "limit": {"$ref": "#/definitions/InstructedAmount"}, "paymentOptionId": {"type": "number"}}, "title": "InitiationRequest"}, "InstructedAmount": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}, "title": "InstructedAmount"}, "IssuanceValidationResponse": {"type": "object", "properties": {"cardCost": {"$ref": "#/definitions/InstructedAmount"}, "ccReference": {"type": "string"}, "documentInfo": {"$ref": "#/definitions/DocumentResponse"}, "limit": {"$ref": "#/definitions/InstructedAmount"}, "minimumLimit": {"$ref": "#/definitions/InstructedAmount"}, "salaried": {"type": "boolean"}}, "title": "IssuanceValidationResponse"}, "IssuanceValidationResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/IssuanceValidationResponse"}}}, "title": "IssuanceValidationResponseDataHolder"}, "IssuanceValidationResponseV3": {"type": "object", "properties": {"averageBalance": {"$ref": "#/definitions/InstructedAmount"}, "businessErrorCode": {"type": "string"}, "cardCost": {"$ref": "#/definitions/InstructedAmount"}, "ccReference": {"type": "string"}, "documentInfo": {"$ref": "#/definitions/DocumentResponse"}, "errorDescription": {"type": "string"}, "limit": {"$ref": "#/definitions/InstructedAmount"}, "minimumLimit": {"$ref": "#/definitions/InstructedAmount"}, "reApplicationDaysLeft": {"type": "integer", "format": "int32"}, "salaried": {"type": "boolean"}, "sscRequired": {"type": "boolean"}, "status": {"type": "string"}, "statusCode": {"type": "string"}, "walletOpenedDate": {"type": "string"}}, "title": "IssuanceValidationResponseV3"}, "IssuanceValidationResponseV3DataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/IssuanceValidationResponseV3"}}}, "title": "IssuanceValidationResponseV3DataHolder"}, "IssuanceValidationResponseV3Wrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/IssuanceValidationResponseV3DataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "IssuanceValidationResponseV3Wrapper"}, "IssuanceValidationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/IssuanceValidationResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "IssuanceValidationResponseWrapper"}, "MaintainCardCommand": {"type": "object", "properties": {"ccReference": {"type": "string"}, "maskedCardNumber": {"type": "string"}, "onlinePayments": {"type": "string", "enum": ["DISABLE", "ENABLE"]}}, "title": "MaintainCardCommand"}, "MerchantIdDetails": {"type": "object", "properties": {"merchantLogo": {"type": "string"}, "merchantName": {"type": "string"}, "totalSpendForMerchant": {"$ref": "#/definitions/AmountDetails"}, "totalSpendForMerchantAmount": {"type": "string"}, "transactionCount": {"type": "integer", "format": "int32"}}, "title": "MerchantIdDetails"}, "MerchantIdTransactionResponse": {"type": "object", "properties": {"merchantLogo": {"type": "string"}, "merchantName": {"type": "string"}, "totalSpendForMerchant": {"$ref": "#/definitions/AmountDetails"}, "transactionCount": {"type": "integer", "format": "int32"}, "transactionDetails": {"type": "array", "items": {"$ref": "#/definitions/TransactionDetails"}}, "transactionEndDate": {"type": "string"}, "transactionStartDate": {"type": "string"}}, "title": "MerchantIdTransactionResponse"}, "MerchantIdTransactionResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/MerchantIdTransactionResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "MerchantIdTransactionResponseWrapper"}, "MetadataDTO": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/PaginationDTO"}, "queryParameters": {"type": "object"}}, "title": "MetadataDTO"}, "MobileEventRequest": {"type": "object", "properties": {"additionalData": {"type": "object"}, "cardUid": {"type": "string"}, "event": {"type": "string"}}, "title": "MobileEventRequest"}, "PaginationDTO": {"type": "object", "properties": {"hasNext": {"type": "boolean"}, "totalPages": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int64"}}, "title": "PaginationDTO"}, "PaymentAnalysisResponse": {"type": "object", "properties": {"fromAccount": {"type": "string"}, "paymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "toAccount": {"type": "string"}, "transferDate": {"type": "string"}}, "title": "PaymentAnalysisResponse"}, "PaymentAnalysisResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/PaymentAnalysisResponse"}}}, "title": "PaymentAnalysisResponseDataHolder"}, "PaymentAnalysisResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/PaymentAnalysisResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "PaymentAnalysisResponseWrapper"}, "PaymentInitiationResponse": {"type": "object", "properties": {"fromAccount": {"type": "string"}, "paymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentStatus": {"type": "string", "enum": ["DECLINED", "SUCCESS"]}, "toAccount": {"type": "string"}, "transferDate": {"type": "string"}, "walletPaymentReference": {"type": "string"}}, "title": "PaymentInitiationResponse"}, "PaymentInitiationResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/PaymentInitiationResponse"}}}, "title": "PaymentInitiationResponseDataHolder"}, "PaymentInitiationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/PaymentInitiationResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "PaymentInitiationResponseWrapper"}, "PhysicalCardDetails": {"type": "object", "properties": {"cardPrinted": {"type": "boolean"}, "cardUId": {"type": "string"}, "deliveryFee": {"type": "number"}, "deliveryStatus": {"type": "string", "enum": ["DELIVERED", "OUT_FOR_DELIVERY", "RECEIVED"]}, "maskedCardNumber": {"type": "string"}, "nameOnCard": {"type": "string"}, "onlinePayments": {"type": "string", "enum": ["DISABLE", "ENABLE"]}, "reIssueFlag": {"type": "boolean"}, "status": {"type": "string"}}, "title": "PhysicalCardDetails"}, "ProfessionDetailsRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "profession": {"$ref": "#/definitions/CodeAndValue"}, "workSector": {"$ref": "#/definitions/CodeAndValue"}}, "title": "ProfessionDetailsRequest"}, "ReplaceRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "documentInfo": {"$ref": "#/definitions/DocumentInfo"}}, "title": "ReplaceRequest"}, "ResponseBodyTemplateDTOOfBasicCreditCardDetails": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/BasicCreditCardDetails"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfBasicCreditCardDetails"}, "ResponseBodyTemplateDTOOfCardDetailsResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/CardDetailsResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfCardDetailsResponse"}, "ResponseBodyTemplateDTOOfCreditCardPaymentResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/CreditCardPaymentResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfCreditCardPaymentResponse"}, "ResponseBodyTemplateDTOOfCustomerProfessionDetailsResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/CustomerProfessionDetailsResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfCustomerProfessionDetailsResponse"}, "ResponseBodyTemplateDTOOfEnrichedTransactionResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/EnrichedTransactionResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfEnrichedTransactionResponse"}, "ResponseBodyTemplateDTOOfFullCreditCardDetails": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/FullCreditCardDetails"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfFullCreditCardDetails"}, "ResponseBodyTemplateDTOOfIncomeAndExpenseResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/IncomeAndExpenseResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfIncomeAndExpenseResponse"}, "ResponseBodyTemplateDTOOfIssuanceValidationResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/IssuanceValidationResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfIssuanceValidationResponse"}, "ResponseBodyTemplateDTOOfIssuanceValidationResponseV3": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/IssuanceValidationResponseV3"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfIssuanceValidationResponseV3"}, "ResponseBodyTemplateDTOOfPaymentAnalysisResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/PaymentAnalysisResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfPaymentAnalysisResponse"}, "ResponseBodyTemplateDTOOfPaymentInitiationResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/PaymentInitiationResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfPaymentInitiationResponse"}, "ResponseBodyTemplateDTOOfStatusResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/StatusResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfStatusResponse"}, "ResponseBodyTemplateDTOOfTapToPayDefaultCardResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/TapToPayDefaultCardResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfTapToPayDefaultCardResponse"}, "ResponseBodyTemplateDTOOfViewLimitResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ViewLimitResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfViewLimitResponse"}, "ResponseBodyTemplateDTOOfViewPinResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/ViewPinResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfViewPinResponse"}, "ResponseTemplateDTOOfBasicCreditCardDetails": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfBasicCreditCardDetails"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfBasicCreditCardDetails"}, "ResponseTemplateDTOOfCardDetailsResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfCardDetailsResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfCardDetailsResponse"}, "ResponseTemplateDTOOfCreditCardPaymentResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfCreditCardPaymentResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfCreditCardPaymentResponse"}, "ResponseTemplateDTOOfCustomerProfessionDetailsResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfCustomerProfessionDetailsResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfCustomerProfessionDetailsResponse"}, "ResponseTemplateDTOOfEnrichedTransactionResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfEnrichedTransactionResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfEnrichedTransactionResponse"}, "ResponseTemplateDTOOfFullCreditCardDetails": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfFullCreditCardDetails"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfFullCreditCardDetails"}, "ResponseTemplateDTOOfIncomeAndExpenseResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfIncomeAndExpenseResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfIncomeAndExpenseResponse"}, "ResponseTemplateDTOOfIssuanceValidationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfIssuanceValidationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfIssuanceValidationResponse"}, "ResponseTemplateDTOOfIssuanceValidationResponseV3": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfIssuanceValidationResponseV3"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfIssuanceValidationResponseV3"}, "ResponseTemplateDTOOfPaymentAnalysisResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfPaymentAnalysisResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfPaymentAnalysisResponse"}, "ResponseTemplateDTOOfPaymentInitiationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfPaymentInitiationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfPaymentInitiationResponse"}, "ResponseTemplateDTOOfStatusResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfStatusResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfStatusResponse"}, "ResponseTemplateDTOOfTapToPayDefaultCardResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfTapToPayDefaultCardResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfTapToPayDefaultCardResponse"}, "ResponseTemplateDTOOfViewLimitResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfViewLimitResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfViewLimitResponse"}, "ResponseTemplateDTOOfViewPinResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfViewPinResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfViewPinResponse"}, "SingularResponseBodyTemplateDTOOfCategoryIdMerchantResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CategoryIdMerchantResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfCategoryIdMerchantResponse"}, "SingularResponseBodyTemplateDTOOfCategoryResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CategoryResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfCategoryResponse"}, "SingularResponseBodyTemplateDTOOfCcSscSendOtpResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CcSscSendOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfCcSscSendOtpResponse"}, "SingularResponseBodyTemplateDTOOfCcSscValidateOtpResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/CcSscValidateOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfCcSscValidateOtpResponse"}, "SingularResponseBodyTemplateDTOOfMerchantIdTransactionResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/MerchantIdTransactionResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfMerchantIdTransactionResponse"}, "SingularResponseBodyTemplateDTOOfTapToPayAnalysisResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TapToPayAnalysisResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfTapToPayAnalysisResponse"}, "SingularResponseBodyTemplateDTOOfTapToPayInitiationResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TapToPayInitiationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfTapToPayInitiationResponse"}, "SingularResponseBodyTemplateDTOOfThalesCardRegistrationResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ThalesCardRegistrationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfThalesCardRegistrationResponse"}, "SingularResponseBodyTemplateDTOOfTokenGetResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TokenGetResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfTokenGetResponse"}, "SingularResponseBodyTemplateDTOOfstring": {"type": "object", "properties": {"data": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfstring"}, "SingularResponseTemplateDTOOfCategoryIdMerchantResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfCategoryIdMerchantResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfCategoryIdMerchantResponse"}, "SingularResponseTemplateDTOOfCategoryResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfCategoryResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfCategoryResponse"}, "SingularResponseTemplateDTOOfCcSscSendOtpResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfCcSscSendOtpResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfCcSscSendOtpResponse"}, "SingularResponseTemplateDTOOfCcSscValidateOtpResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfCcSscValidateOtpResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfCcSscValidateOtpResponse"}, "SingularResponseTemplateDTOOfMerchantIdTransactionResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfMerchantIdTransactionResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfMerchantIdTransactionResponse"}, "SingularResponseTemplateDTOOfTapToPayAnalysisResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfTapToPayAnalysisResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfTapToPayAnalysisResponse"}, "SingularResponseTemplateDTOOfTapToPayInitiationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfTapToPayInitiationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfTapToPayInitiationResponse"}, "SingularResponseTemplateDTOOfThalesCardRegistrationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfThalesCardRegistrationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfThalesCardRegistrationResponse"}, "SingularResponseTemplateDTOOfTokenGetResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfTokenGetResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfTokenGetResponse"}, "SingularResponseTemplateDTOOfstring": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfstring"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfstring"}, "StatusResponse": {"type": "object", "properties": {"status": {"type": "string"}}, "title": "StatusResponse"}, "StatusResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/StatusResponse"}}}, "title": "StatusResponseDataHolder"}, "StatusResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/StatusResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "StatusResponseWrapper"}, "StringWrapper": {"type": "object", "properties": {"data": {"type": "string"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "StringWrapper"}, "TapToPayAnalysisRequest": {"type": "object", "properties": {"cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "maskedCardNumber": {"type": "string"}}, "title": "TapToPayAnalysisRequest"}, "TapToPayAnalysisResponse": {"type": "object", "properties": {"cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "maskedCardNumber": {"type": "string"}, "tncDocument": {"$ref": "#/definitions/TncDocument"}}, "title": "TapToPayAnalysisResponse"}, "TapToPayAnalysisResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TapToPayAnalysisResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "TapToPayAnalysisResponseWrapper"}, "TapToPayDefaultCardRequest": {"type": "object", "properties": {"cardMaskedNumber": {"type": "string"}, "cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "defaultFlag": {"type": "boolean"}}, "title": "TapToPayDefaultCardRequest"}, "TapToPayDefaultCardResponse": {"type": "object", "properties": {"status": {"type": "string"}}, "title": "TapToPayDefaultCardResponse"}, "TapToPayDefaultCardResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/TapToPayDefaultCardResponse"}}}, "title": "TapToPayDefaultCardResponseDataHolder"}, "TapToPayDefaultCardResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TapToPayDefaultCardResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "TapToPayDefaultCardResponseWrapper"}, "TapToPayInitiationRequest": {"type": "object", "properties": {"cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "maskedCardNumber": {"type": "string"}, "tncDocument": {"$ref": "#/definitions/TncInitiationDocument"}}, "title": "TapToPayInitiationRequest"}, "TapToPayInitiationResponse": {"type": "object", "properties": {"cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "maskedCardNumber": {"type": "string"}, "status": {"type": "string"}}, "title": "TapToPayInitiationResponse"}, "TapToPayInitiationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TapToPayInitiationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "TapToPayInitiationResponseWrapper"}, "ThalesCardRegistrationRequest": {"type": "object", "required": ["cardMaskNumber", "cardType"], "properties": {"cardMaskNumber": {"type": "string"}, "cardType": {"type": "string", "enum": ["DEBIT", "VIRTUAL"]}, "consumerId": {"type": "string"}}, "title": "ThalesCardRegistrationRequest"}, "ThalesCardRegistrationResponse": {"type": "object", "properties": {"cardUId": {"type": "string"}}, "title": "ThalesCardRegistrationResponse"}, "ThalesCardRegistrationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ThalesCardRegistrationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ThalesCardRegistrationResponseWrapper"}, "TncDocument": {"type": "object", "properties": {"href": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "version": {"type": "string"}}, "title": "TncDocument"}, "TncInitiationDocument": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "version": {"type": "string"}}, "title": "TncInitiationDocument"}, "TokenGetResponse": {"type": "object", "properties": {"token": {"type": "string"}}, "title": "TokenGetResponse"}, "TokenGetResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/TokenGetResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "TokenGetResponseWrapper"}, "TransactionDetails": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}, "date": {"type": "string"}, "transactionCode": {"type": "string"}, "transactionCodeDescription": {"type": "string"}, "transactionIcon": {"type": "string"}, "value": {"type": "string"}}, "title": "TransactionDetails"}, "UserIncomeDetails": {"type": "object", "properties": {"currentCommitments": {"$ref": "#/definitions/CurrentCommitments0"}, "fixedMonthlyIncome": {"$ref": "#/definitions/FixedMonthlyIncome"}, "incomeDeclaration": {"$ref": "#/definitions/IncomeDeclaration0"}}, "title": "UserIncomeDetails"}, "ViewLimitResponse": {"type": "object", "properties": {"approvedLimit": {"$ref": "#/definitions/InstructedAmount"}, "cardType": {"type": "string"}, "eligibilityStatus": {"type": "string", "enum": ["APPROVED", "PENDING_REVIEW", "REJECTED"]}, "interestRate": {"type": "number"}, "paymentOptionId": {"type": "number"}}, "title": "ViewLimitResponse"}, "ViewLimitResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/ViewLimitResponse"}}}, "title": "ViewLimitResponseDataHolder"}, "ViewLimitResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ViewLimitResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ViewLimitResponseWrapper"}, "ViewPinRequest": {"type": "object", "properties": {"ccReference": {"type": "string"}, "key": {"type": "string"}, "maskedCardNumber": {"type": "string"}}, "title": "ViewPinRequest"}, "ViewPinResponse": {"type": "object", "properties": {"encryptedPin": {"type": "string"}, "maskedCardNumber": {"type": "string"}}, "title": "ViewPinResponse"}, "ViewPinResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/ViewPinResponse"}}}, "title": "ViewPinResponseDataHolder"}, "ViewPinResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/ViewPinResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ViewPinResponseWrapper"}}}