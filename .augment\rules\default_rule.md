---
type: "always_apply"
---

always update unit test for specific code change even if it is not explicitely instructed
always update README.md for high level project setup and implementation even if it is not explicitely instructed
always update docstring as technical documentation on specific file even if it is not explicitely instructed
follow clean code architecture, modularity, readability, logging and exception handling for all code changes.