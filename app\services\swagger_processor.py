"""
SwaggerProcessor
----------------
Service for processing and transforming Swagger/OpenAPI specifications for Apigee proxies.

Features:
- Transform target service swagger to Apigee proxy swagger
- Add Apigee-specific basePath and headers
- Update server URLs for proxy endpoints
- Maintain original API structure while adapting for proxy

Usage:
    processor = SwaggerProcessor()
    proxy_swagger = processor.create_proxy_swagger(
        target_swagger_content=original_swagger,
        apigee_base_path="/v1/my-api",
        apigee_host="api.company.com",
        additional_headers=["x-api-key", "x-correlation-id"]
    )
"""

from __future__ import annotations

import json
import yaml
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse, urljoin

import structlog

from ..core.errors import ValidationError

log = structlog.get_logger(__name__)


class SwaggerProcessor:
    def __init__(self) -> None:
        pass

    def create_proxy_swagger(
        self,
        target_swagger_content: bytes,
        apigee_base_path: str,
        apigee_host: Optional[str] = None,
        apigee_schemes: Optional[List[str]] = None,
        additional_headers: Optional[List[str]] = None,
        proxy_description_suffix: str = " (via Apigee Proxy)"
    ) -> bytes:
        """
        Transform target service swagger to create Apigee proxy swagger specification.
        
        Args:
            target_swagger_content: Original swagger content from target service
            apigee_base_path: Base path for the Apigee proxy (e.g., "/v1/my-api")
            apigee_host: Host for the Apigee proxy (e.g., "api.company.com")
            apigee_schemes: URL schemes for the proxy (e.g., ["https"])
            additional_headers: Additional headers to add to all operations
            proxy_description_suffix: Suffix to add to API description
            
        Returns:
            bytes: Transformed swagger content for the Apigee proxy
        """
        log.info("creating_proxy_swagger", 
                base_path=apigee_base_path, 
                host=apigee_host,
                additional_headers=additional_headers)

        # Parse the original swagger
        swagger_data = self._parse_swagger_content(target_swagger_content)
        
        # Create a copy for transformation
        proxy_swagger = swagger_data.copy()
        
        # Transform for Apigee proxy
        self._update_server_info(proxy_swagger, apigee_base_path, apigee_host, apigee_schemes)
        self._update_description(proxy_swagger, proxy_description_suffix)
        self._add_proxy_headers(proxy_swagger, additional_headers or [])
        self._add_apigee_metadata(proxy_swagger)
        
        # Convert back to JSON bytes
        result = json.dumps(proxy_swagger, indent=2, ensure_ascii=False).encode('utf-8')
        
        log.info("proxy_swagger_created", 
                original_size=len(target_swagger_content),
                proxy_size=len(result))
        
        return result

    def _parse_swagger_content(self, content: bytes) -> Dict[str, Any]:
        """Parse swagger content from bytes to dict."""
        try:
            text = content.decode('utf-8', errors='ignore')
            
            # Try JSON first
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                # Try YAML
                return yaml.safe_load(text)
                
        except Exception as e:
            raise ValidationError(f"Failed to parse swagger content: {str(e)}")

    def _update_server_info(
        self, 
        swagger_data: Dict[str, Any], 
        base_path: str, 
        host: Optional[str] = None,
        schemes: Optional[List[str]] = None
    ) -> None:
        """Update server information for Apigee proxy."""
        
        # Normalize base path
        if not base_path.startswith('/'):
            base_path = '/' + base_path
        if base_path != '/' and base_path.endswith('/'):
            base_path = base_path[:-1]

        # Handle OpenAPI 3.x format
        if 'openapi' in swagger_data:
            servers = []
            
            if host:
                scheme = schemes[0] if schemes else 'https'
                server_url = f"{scheme}://{host}{base_path}"
                servers.append({
                    "url": server_url,
                    "description": "Apigee Proxy Endpoint"
                })
            else:
                # Use relative URL
                servers.append({
                    "url": base_path,
                    "description": "Apigee Proxy Endpoint"
                })
            
            swagger_data['servers'] = servers
            
        # Handle Swagger 2.0 format
        elif 'swagger' in swagger_data:
            if host:
                swagger_data['host'] = host
            
            if schemes:
                swagger_data['schemes'] = schemes
            elif 'schemes' not in swagger_data:
                swagger_data['schemes'] = ['https']
                
            swagger_data['basePath'] = base_path

    def _update_description(self, swagger_data: Dict[str, Any], suffix: str) -> None:
        """Update API description to indicate it's a proxy."""
        info = swagger_data.get('info', {})
        
        original_description = info.get('description', '')
        if original_description and not original_description.endswith(suffix):
            info['description'] = original_description + suffix
        elif not original_description:
            info['description'] = f"API Proxy{suffix}"
            
        # Update title if needed
        original_title = info.get('title', '')
        if original_title and not 'Proxy' in original_title:
            info['title'] = f"{original_title} Proxy"
        elif not original_title:
            info['title'] = "API Proxy"
            
        swagger_data['info'] = info

    def _add_proxy_headers(self, swagger_data: Dict[str, Any], additional_headers: List[str]) -> None:
        """Add Apigee-specific headers to all operations."""
        if not additional_headers:
            return
            
        # Standard Apigee headers that are commonly used
        standard_apigee_headers = [
            'x-correlation-id',
            'x-request-id', 
            'x-api-key',
            'x-client-id',
            'x-forwarded-for'
        ]
        
        # Combine with additional headers
        all_headers = list(set(standard_apigee_headers + additional_headers))
        
        paths = swagger_data.get('paths', {})
        for path_item in paths.values():
            if not isinstance(path_item, dict):
                continue
                
            for method, operation in path_item.items():
                if method.lower() in ['get', 'post', 'put', 'delete', 'patch', 'head', 'options']:
                    if isinstance(operation, dict):
                        self._add_headers_to_operation(operation, all_headers)

    def _add_headers_to_operation(self, operation: Dict[str, Any], headers: List[str]) -> None:
        """Add headers to a specific operation."""
        parameters = operation.get('parameters', [])
        
        # Get existing header parameter names
        existing_headers = set()
        for param in parameters:
            if isinstance(param, dict) and param.get('in') == 'header':
                existing_headers.add(param.get('name', '').lower())
        
        # Add new headers that don't already exist
        for header_name in headers:
            if header_name.lower() not in existing_headers:
                header_param = {
                    'name': header_name,
                    'in': 'header',
                    'required': False,
                    'description': f'Apigee proxy header: {header_name}',
                    'schema': {'type': 'string'} if 'openapi' in operation.get('swagger_version', '') else None
                }
                
                # For Swagger 2.0, use 'type' instead of 'schema'
                if 'openapi' not in str(operation):
                    header_param['type'] = 'string'
                    del header_param['schema']
                
                parameters.append(header_param)
        
        operation['parameters'] = parameters

    def _add_apigee_metadata(self, swagger_data: Dict[str, Any]) -> None:
        """Add Apigee-specific metadata to the swagger."""
        info = swagger_data.get('info', {})
        
        # Add vendor extension for Apigee
        info['x-apigee-proxy'] = True
        info['x-generated-by'] = 'Apigee Proxy Factory'
        
        # Add contact info if not present
        if 'contact' not in info:
            info['contact'] = {
                'name': 'API Team',
                'email': '<EMAIL>'
            }
        
        swagger_data['info'] = info
        
        # Add global vendor extensions
        swagger_data['x-apigee-proxy-metadata'] = {
            'generated_at': self._get_current_timestamp(),
            'proxy_type': 'api_proxy',
            'version': '1.0.0'
        }

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime
        return datetime.utcnow().isoformat() + 'Z'

    def extract_target_info(self, swagger_content: bytes) -> Dict[str, Any]:
        """
        Extract target service information from swagger content.
        
        Returns:
            Dict containing target host, base path, schemes, etc.
        """
        swagger_data = self._parse_swagger_content(swagger_content)
        
        target_info = {
            'host': None,
            'base_path': '/',
            'schemes': ['https'],
            'title': swagger_data.get('info', {}).get('title', 'API'),
            'version': swagger_data.get('info', {}).get('version', '1.0.0')
        }
        
        # Extract from OpenAPI 3.x
        if 'openapi' in swagger_data:
            servers = swagger_data.get('servers', [])
            if servers and isinstance(servers[0], dict):
                server_url = servers[0].get('url', '')
                if server_url:
                    parsed = urlparse(server_url)
                    target_info['host'] = parsed.netloc
                    target_info['base_path'] = parsed.path or '/'
                    target_info['schemes'] = [parsed.scheme] if parsed.scheme else ['https']
        
        # Extract from Swagger 2.0
        elif 'swagger' in swagger_data:
            target_info['host'] = swagger_data.get('host')
            target_info['base_path'] = swagger_data.get('basePath', '/')
            target_info['schemes'] = swagger_data.get('schemes', ['https'])
        
        return target_info
