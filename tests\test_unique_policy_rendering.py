#!/usr/bin/env python3
"""
Test unique policy rendering to prevent duplicate policy creation.
This test verifies that policies appearing in both proxy and target flows
are rendered only once, preventing 409 Conflict errors.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder


def test_unique_policy_rendering():
    """Test that duplicate policies are rendered only once."""
    print("============================================================")
    print("TESTING UNIQUE POLICY RENDERING")
    print("============================================================")
    
    try:
        # Load the actual system configuration
        system_config_path = "app/config/systems/REFLECT_FDID.yaml"
        with open(system_config_path, 'r') as f:
            system_cfg = yaml.safe_load(f)
        
        print(f"✅ Loaded system configuration: {len(system_cfg.get('policies', {}))} policies")
        
        # Analyze policy usage across flows
        print(f"\n🔍 Analyzing Policy Usage Across Flows...")
        
        # Check proxy flows
        proxy_flows = system_cfg.get("flows", {}).get("proxy", {})
        proxy_policies = []
        
        for flow_type, flow_config in proxy_flows.items():
            if isinstance(flow_config, dict):
                for direction, policies in flow_config.items():
                    if isinstance(policies, list):
                        proxy_policies.extend(policies)
                        print(f"   Proxy {flow_type}.{direction}: {policies}")
        
        # Check target flows
        targets_cfg = system_cfg.get("targets", {})
        target_policies = []
        
        for target_name, target_config in targets_cfg.items():
            target_flows = target_config.get("flows", {})
            print(f"   Target '{target_name}' flows:")
            
            for flow_type, flow_config in target_flows.items():
                if isinstance(flow_config, dict):
                    for direction, policies in flow_config.items():
                        if isinstance(policies, list):
                            target_policies.extend(policies)
                            print(f"      {flow_type}.{direction}: {policies}")
        
        # Find duplicates
        all_policies = proxy_policies + target_policies
        unique_policies = list(set(all_policies))
        duplicates = [p for p in unique_policies if all_policies.count(p) > 1]
        
        print(f"\n📊 Policy Usage Analysis:")
        print(f"   Total policy references: {len(all_policies)}")
        print(f"   Unique policies: {len(unique_policies)}")
        print(f"   Duplicate policies: {duplicates}")
        
        if duplicates:
            print(f"   ⚠️  Found {len(duplicates)} policies used in multiple flows")
            for dup in duplicates:
                count = all_policies.count(dup)
                print(f"      '{dup}': used {count} times")
        else:
            print(f"   ✅ No duplicate policies found")
        
        # Test policy builder with duplicate detection
        print(f"\n🏗️ Testing Policy Builder with Duplicate Detection...")
        policy_builder = PolicyBuilder()
        
        test_input_vars = {
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],
            "content_types": ["application/json"],
            "policy_params": {}
        }
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=system_cfg,
            input_vars=test_input_vars,
            system_name="REFLECT_FDID"
        )
        
        print(f"   ✅ Policy Builder: Rendered {len(policies)} policies")
        
        # Check for duplicate policy names in rendered policies
        rendered_policy_names = [p.name for p in policies]
        rendered_unique_names = list(set(rendered_policy_names))
        rendered_duplicates = [name for name in rendered_unique_names if rendered_policy_names.count(name) > 1]
        
        print(f"   📊 Rendered Policy Analysis:")
        print(f"      Total rendered policies: {len(rendered_policy_names)}")
        print(f"      Unique rendered policies: {len(rendered_unique_names)}")
        
        if rendered_duplicates:
            print(f"      ❌ Duplicate rendered policies found: {rendered_duplicates}")
            for dup in rendered_duplicates:
                count = rendered_policy_names.count(dup)
                print(f"         '{dup}': rendered {count} times")
            return False
        else:
            print(f"      ✅ No duplicate rendered policies - each policy rendered exactly once")
        
        # Verify specific duplicate policy (FC-ExtractCustomLogs)
        print(f"\n🎯 Testing Specific Duplicate Policy (FC-ExtractCustomLogs)...")
        
        fc_extract_count = rendered_policy_names.count("FC-ExtractCustomLogs")
        if fc_extract_count == 1:
            print(f"   ✅ FC-ExtractCustomLogs: Rendered exactly once (expected)")
        elif fc_extract_count == 0:
            print(f"   ⚠️  FC-ExtractCustomLogs: Not rendered (may be filtered)")
        else:
            print(f"   ❌ FC-ExtractCustomLogs: Rendered {fc_extract_count} times (should be 1)")
            return False
        
        # Check that the policy is properly defined
        fc_extract_policy = next((p for p in policies if p.name == "FC-ExtractCustomLogs"), None)
        if fc_extract_policy:
            if fc_extract_policy.xml and len(fc_extract_policy.xml) > 50:
                print(f"   ✅ FC-ExtractCustomLogs: Template rendered successfully ({len(fc_extract_policy.xml)} chars)")
            else:
                print(f"   ❌ FC-ExtractCustomLogs: Template rendering failed")
                return False
        
        # Test attach plan to ensure policies are properly attached
        print(f"\n📋 Testing Attach Plan...")
        
        total_attachments = (
            len(attach_plan.preflow_request) + 
            len(attach_plan.preflow_response) +
            len(attach_plan.perflow_request) + 
            len(attach_plan.perflow_response) +
            len(attach_plan.postclientflow_response) +
            len(attach_plan.nomatchfound_request)
        )
        
        print(f"   Total policy attachments: {total_attachments}")
        print(f"   PreFlow Request: {len(attach_plan.preflow_request)} policies")
        print(f"   PreFlow Response: {len(attach_plan.preflow_response)} policies")
        print(f"   PerFlow Request: {len(attach_plan.perflow_request)} policies")
        print(f"   PerFlow Response: {len(attach_plan.perflow_response)} policies")
        
        # Check if FC-ExtractCustomLogs is in the attach plan
        all_attached_policies = (
            attach_plan.preflow_request + attach_plan.preflow_response +
            attach_plan.perflow_request + attach_plan.perflow_response +
            attach_plan.postclientflow_response + attach_plan.nomatchfound_request
        )
        
        if "FC-ExtractCustomLogs" in all_attached_policies:
            print(f"   ✅ FC-ExtractCustomLogs: Found in attach plan")
        else:
            print(f"   ⚠️  FC-ExtractCustomLogs: Not found in attach plan")
        
        # Test that all rendered policies have unique names
        print(f"\n✅ Verification Summary:")
        print(f"   Policies rendered: {len(policies)}")
        print(f"   Unique policy names: {len(set(p.name for p in policies))}")
        print(f"   Duplicate prevention: {'✅ Working' if len(policies) == len(set(p.name for p in policies)) else '❌ Failed'}")
        
        print("\n============================================================")
        print("✅ UNIQUE POLICY RENDERING TEST PASSED!")
        print("Duplicate policies are properly handled - each policy rendered only once.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_unique_policy_rendering()
    sys.exit(0 if success else 1)
