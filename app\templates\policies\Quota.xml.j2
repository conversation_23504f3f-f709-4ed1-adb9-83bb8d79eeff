<!-- Smart Quota policy with validation and environment awareness -->
<Quota name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>

  <!-- Use resolved parameter value or apply smart defaults only if not provided -->
  {% if params.get('allow') %}
    <!-- Use the resolved parameter value (from system/env/input) -->
    {% set quota_rate = params.allow %}
  {% else %}
    <!-- Apply smart defaults only when no value is configured -->
    {% if is_production() %}
      {% set quota_rate = '1000pm' %}
    {% elif feature_enabled('high_quota_limits') %}
      {% set quota_rate = '2000pm' %}
    {% else %}
      {% set quota_rate = '500pm' %}
    {% endif %}
  {% endif %}

  <!-- Parse rate format into count and time unit -->
  {% set count_value, parsed_timeunit = parse_rate_format(quota_rate) %}
  <Allow count="{{ count_value }}"/>

  <!-- Smart interval and time unit with validation -->
  {% set interval_value = params.interval | default('1') %}
  <!-- Use parsed timeunit from rate format if allow parameter contains rate format -->
  {% if params.get('allow') and is_valid_rate(params.get('allow')) %}
    {% set timeunit_value = parsed_timeunit %}
  {% else %}
    {% set timeunit_value = params.timeunit | default('minute') %}
  {% endif %}
  {% if timeunit_value not in ['second', 'minute', 'hour', 'day', 'week', 'month'] %}
    {% set timeunit_value = 'minute' %}
  {% endif %}

  <Interval>{{ interval_value }}</Interval>
  <TimeUnit>{{ timeunit_value }}</TimeUnit>

  <!-- Smart identifier with fallback chain -->
  {% if params.get('identifier_ref') %}
    <Identifier ref="{{ params.identifier_ref }}"/>
  {% elif feature_enabled('client_id_identification') %}
    <Identifier ref="request.header.client_id"/>
  {% else %}
    <Identifier ref="request.header.x-api-key"/>
  {% endif %}

  <!-- Environment-aware distribution settings -->
  {% set distributed = params.distributed | default('true') %}
  {% set synchronous = params.synchronous | default('true') %}
  {% if is_production() and not feature_enabled('async_quota') %}
    {% set synchronous = 'true' %}  <!-- Force synchronous in production for accuracy -->
  {% endif %}

  <Distributed>{{ distributed }}</Distributed>
  <Synchronous>{{ synchronous }}</Synchronous>

  <!-- Optional start time for scheduled quotas -->
  {% if params.get('startTime') and params.startTime | length > 0 %}
    <StartTime>{{ params.startTime }}</StartTime>
  {% endif %}

  <!-- Advanced feature: Custom quota variables -->
  {% if feature_enabled('custom_quota_variables') and params.get('variables') %}
    {% for var in params.variables %}
      <Variable name="{{ var.name }}" ref="{{ var.ref }}"/>
    {% endfor %}
  {% endif %}
</Quota>