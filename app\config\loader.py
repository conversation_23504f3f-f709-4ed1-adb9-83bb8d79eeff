# src/app/config/loader.py
"""
ConfigLoader
------------
Class-based loader for System + Env YAML with a safe deep-merge.

Use from Orchestrator:
    loader = ConfigLoader()
    cfgs = loader.load(system_name="payments")
    # cfgs.system_cfg  -> pass into PolicyBuilder (let PB apply ENV+INPUT per param)
    # cfgs.effective   -> use for env-bound parts (TargetEndpoint, resources, etc.)

Merging semantics (deep_merge):
- dict vs dict  -> recurse
- list vs list  -> env list replaces system list (conservative; PolicyBuilder handles
                   list ops for policy params later using its catalog + directives)
- anything else -> env overrides system
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional

import structlog
import yaml

from ..core.errors import ValidationError
from ..core.settings import settings

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class LoadedConfigs:
    system_cfg: Dict[str, Any]
    env_cfg: Dict[str, Any]
    effective: Dict[str, Any]
    system_path: Path
    env_path: Optional[Path]


class ConfigLoader:
    def __init__(
        self,
        *,
        systems_dir: Optional[str] = None,
        envs_dir: Optional[str] = None,
        env_name: Optional[str] = None,
    ) -> None:
        self._systems_dir = Path(systems_dir or settings.systems_dir).resolve()
        # default env root to sibling "env" next to systems/
        default_env_root = self._systems_dir.parent / "env"
        self._envs_dir = Path(envs_dir or getattr(settings, "envs_dir", "") or default_env_root).resolve()
        self._env_name = (env_name or settings.apigee_env).strip()
        self._cache: Dict[str, tuple[float, Dict[str, Any]]] = {}  # path -> (mtime, data)

    # ---------- Public API ----------

    def load(self, system_name: str) -> LoadedConfigs:
        """
        Load system defaults and env overrides for a given system,
        returning both raw dicts and the deep-merged 'effective' view.
        """
        if not system_name or not system_name.strip():
            raise ValidationError("system_name is required")

        sys_path = self._systems_dir / f"{system_name}.yaml"
        if not sys_path.exists():
            raise ValidationError(
                f"System config file not found: {sys_path}",
                details={"system": system_name, "path": str(sys_path)},
            )

        system_cfg = self._read_yaml(sys_path, what=f"system:{system_name}")

        env_path = self._envs_dir / self._env_name / f"{system_name}.yaml"
        if env_path.exists():
            env_cfg = self._read_yaml(env_path, what=f"env:{self._env_name}/{system_name}")
        else:
            log.debug("env_config_absent", env=self._env_name, system=system_name, path=str(env_path))
            env_cfg = {}

        effective = self.deep_merge(system_cfg, env_cfg)

        return LoadedConfigs(
            system_cfg=system_cfg,
            env_cfg=env_cfg,
            effective=effective,
            system_path=sys_path,
            env_path=env_path if env_path.exists() else None,
        )

    def clear_cache(self) -> None:
        """Drop in-memory YAML cache (e.g., after config changes)."""
        self._cache.clear()

    # ---------- Static helpers ----------

    @staticmethod
    def deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively merge 'override' into 'base' and return a NEW dict.

        Rules:
        - dict + dict -> recurse
        - list + list -> override replaces base (safer across envs)
        - else        -> override wins if not None, otherwise base
        """
        if base is None:
            base = {}
        if override is None:
            override = {}
        if not isinstance(base, dict) or not isinstance(override, dict):
            return override if override is not None else base

        result: Dict[str, Any] = {}
        for key in set(base.keys()) | set(override.keys()):
            bv = base.get(key)
            ov = override.get(key)
            if isinstance(bv, dict) and isinstance(ov, dict):
                result[key] = ConfigLoader.deep_merge(bv, ov)
            elif isinstance(bv, list) and isinstance(ov, list):
                result[key] = list(ov)
            elif ov is not None:
                result[key] = ov
            else:
                result[key] = bv
        return result

    # ---------- Internal ----------

    def _read_yaml(self, path: Path, *, what: str) -> Dict[str, Any]:
        """
        Read YAML with a tiny mtime-based cache.
        """
        pstr = str(path)
        try:
            mtime = path.stat().st_mtime
        except FileNotFoundError:
            raise ValidationError(f"Config file not found for {what}", details={"path": pstr})

        cached = self._cache.get(pstr)
        if cached and cached[0] == mtime:
            return cached[1]

        try:
            with open(path, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f) or {}
            if not isinstance(data, dict):
                raise ValidationError(f"Invalid YAML structure for {what}", details={"path": pstr})
            self._cache[pstr] = (mtime, data)
            log.info("config_loaded", what=what, path=pstr)
            return data
        except yaml.YAMLError as e:
            raise ValidationError(f"YAML parse error for {what}", details={"path": pstr, "error": str(e)}) from e
        except ValidationError:
            raise
        except Exception as e:
            raise ValidationError(f"Failed to load {what}", details={"path": pstr, "error": str(e)}) from e