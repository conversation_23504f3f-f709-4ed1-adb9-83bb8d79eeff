#!/usr/bin/env python3
"""
Test policy catalog alignment with system configuration.
This test verifies that policy names in the catalog groups match
the actual policy names defined in the system configuration.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder


def test_policy_catalog_alignment():
    """Test that policy catalog groups use correct policy names from system config."""
    print("============================================================")
    print("TESTING POLICY CATALOG ALIGNMENT")
    print("============================================================")
    
    try:
        # Load system configuration
        system_config_path = "app/config/systems/REFLECT_FDID.yaml"
        with open(system_config_path, 'r') as f:
            system_cfg = yaml.safe_load(f)
        
        # Load policy catalog
        catalog_path = "app/config/policy_catalog.yaml"
        with open(catalog_path, 'r') as f:
            catalog = yaml.safe_load(f)
        
        # Get all policy names from system config
        system_policies = set(system_cfg.get('policies', {}).keys())
        print(f"✅ Found {len(system_policies)} policies in system config")
        
        # Get all policy names from catalog groups
        catalog_policies = set()
        groups = catalog.get('groups', {})
        
        for group_name, group_config in groups.items():
            group_policies = group_config.get('policies', [])
            catalog_policies.update(group_policies)
            print(f"   Group '{group_name}': {len(group_policies)} policies")
        
        print(f"✅ Found {len(catalog_policies)} unique policies in catalog groups")
        
        # Check alignment
        missing_in_system = catalog_policies - system_policies
        missing_in_catalog = system_policies - catalog_policies
        
        if missing_in_system:
            print(f"❌ Policies in catalog but missing in system config:")
            for policy in sorted(missing_in_system):
                print(f"   - {policy}")
        else:
            print("✅ All catalog policies exist in system config")
        
        if missing_in_catalog:
            print(f"ℹ️  Policies in system config but not in catalog groups:")
            for policy in sorted(missing_in_catalog):
                print(f"   - {policy}")
        else:
            print("✅ All system policies are referenced in catalog groups")
        
        # Test policy rendering with catalog policies
        print(f"\n🧪 Testing Policy Rendering...")
        policy_builder = PolicyBuilder()
        
        # Test a few policies from different groups
        test_policies = [
            "VK-VerifyApiKey",  # security group
            "SA-SpikeArrest",   # traffic_management group
            "AM-AddCors",       # cors_messaging group
            "FC-CommonSecurityPreFlow",  # integration group
            "RF-ResourceNotFound"  # error_handling group
        ]
        
        test_cfg = {
            "flows": {
                "proxy": {
                    "preflow": {
                        "request": test_policies[:3],
                        "response": []
                    }
                }
            },
            "policies": {policy: system_cfg['policies'][policy] for policy in test_policies if policy in system_cfg['policies']}
        }
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=test_cfg,
            input_vars={"policy_params": {}},
            system_name="test"
        )
        
        print(f"✅ Successfully rendered {len(policies)} test policies")
        
        # Verify templates were found
        for policy in policies:
            if policy.xml and len(policy.xml) > 0:
                print(f"   ✅ {policy.name}: Template rendered successfully")
            else:
                print(f"   ❌ {policy.name}: Template rendering failed")
                return False
        
        # Test high_security group specifically
        print(f"\n🔒 Testing High Security Group...")
        high_security_group = groups.get('high_security', {})
        high_security_policies = high_security_group.get('policies', [])
        
        print(f"High Security policies: {high_security_policies}")
        
        # Verify all high security policies exist in system config
        for policy in high_security_policies:
            if policy in system_policies:
                print(f"   ✅ {policy}: Found in system config")
            else:
                print(f"   ❌ {policy}: Missing in system config")
                return False
        
        print("\n============================================================")
        print("✅ POLICY CATALOG ALIGNMENT TEST PASSED!")
        print("Policy catalog groups are properly aligned with system configuration.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_policy_catalog_alignment()
    sys.exit(0 if success else 1)
