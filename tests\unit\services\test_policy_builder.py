"""
Unit tests for PolicyBuilder service
====================================
Comprehensive test coverage for policy rendering, template processing,
and attachment plan generation.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import os

from app.services.policy_builder import PolicyBuilder, PolicyRender, AttachPlan
from app.core.errors import ValidationError


class TestPolicyBuilder:
    """Test suite for PolicyBuilder class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.builder = PolicyBuilder()
        self.sample_system_cfg = {
            "metadata": {
                "system": "test-system",
                "owner_team": "test-team"
            },
            "policy_defaults": {
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "100pm"},
                "Quota": {"allow": "1000pm", "timeunit": "minute"}
            },
            "flows": {
                "preflow": {
                    "request": ["VerifyApiKey", "SpikeArrest"],
                    "response": []
                },
                "per_operation": {
                    "request": ["Quota"],
                    "response": []
                },
                "cors": {
                    "enabled": True,
                    "policy": "CORS"
                }
            },
            "target": {
                "baseurl": "https://api.example.com"
            }
        }
        
        self.sample_input_vars = {
            "methods": ["GET", "POST"],
            "policy_params": {
                "VerifyApiKey": {"keyref": "request.header.custom-key"},
                "SpikeArrest": {"rate": "200pm"}
            },
            "features": {
                "advanced_security": True
            }
        }
    
    def test_init(self):
        """Test PolicyBuilder initialization"""
        builder = PolicyBuilder()
        assert builder is not None
        assert hasattr(builder, '_template_helpers')
    
    def test_render_policies_basic(self):
        """Test basic policy rendering functionality"""
        with patch.object(self.builder, '_render_template') as mock_render:
            mock_render.return_value = '<SpikeArrest name="SA-Test"><Rate>100pm</Rate></SpikeArrest>'
            
            policies, plan = self.builder.render_policies(
                self.sample_system_cfg,
                self.sample_input_vars,
                system_name="test-system"
            )
            
            assert isinstance(policies, list)
            assert isinstance(plan, AttachPlan)
            assert len(policies) > 0
            assert mock_render.called
    
    def test_render_policies_with_conditional_policies(self):
        """Test policy rendering with conditional policy attachment"""
        with patch('app.services.policy_builder.get_feature_evaluator') as mock_evaluator:
            with patch('app.services.policy_builder.get_conditional_attacher') as mock_attacher:
                mock_eval_instance = Mock()
                mock_eval_instance.is_enabled.return_value = True
                mock_evaluator.return_value = mock_eval_instance
                
                mock_attacher_instance = Mock()
                mock_attacher_instance.get_applicable_policies.return_value = [
                    {"policy": "JSONThreatProtection", "display_name": "JTP-Test"}
                ]
                mock_attacher.return_value = mock_attacher_instance
                
                with patch.object(self.builder, '_render_template') as mock_render:
                    mock_render.return_value = '<Policy>test</Policy>'
                    
                    policies, plan = self.builder.render_policies(
                        self.sample_system_cfg,
                        self.sample_input_vars,
                        system_name="test-system"
                    )
                    
                    assert len(policies) > 0
                    mock_attacher_instance.get_applicable_policies.assert_called()
    
    def test_render_template_success(self):
        """Test successful template rendering"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "test_template.xml.j2"
            template_content = '<SpikeArrest name="{{ name }}"><Rate>{{ params.rate }}</Rate></SpikeArrest>'
            template_path.write_text(template_content)
            
            with patch('app.services.policy_builder.Path') as mock_path:
                mock_path.return_value = Path(temp_dir)
                
                result = self.builder._render_template(
                    "test_template.xml.j2",
                    name="SA-Test",
                    params={"rate": "100pm"},
                    input={"methods": ["GET"]},
                    env={"name": "test"}
                )
                
                assert "SA-Test" in result
                assert "100pm" in result
    
    def test_render_template_missing_file(self):
        """Test template rendering with missing template file"""
        with pytest.raises(ValidationError, match="Template not found"):
            self.builder._render_template(
                "nonexistent_template.xml.j2",
                name="Test",
                params={},
                input={},
                env={}
            )
    
    def test_render_template_with_helpers(self):
        """Test template rendering with helper functions"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "helper_template.xml.j2"
            template_content = '''
            <SpikeArrest name="{{ name }}">
                {% set rate_value = safe_default(params.rate, '100pm', is_valid_rate) %}
                <Rate>{{ rate_value }}</Rate>
            </SpikeArrest>
            '''
            template_path.write_text(template_content)
            
            with patch('app.services.policy_builder.Path') as mock_path:
                mock_path.return_value = Path(temp_dir)
                
                result = self.builder._render_template(
                    "helper_template.xml.j2",
                    name="SA-Test",
                    params={"rate": "invalid_rate"},
                    input={"methods": ["GET"]},
                    env={"name": "test"}
                )
                
                # Should fallback to default rate due to invalid input
                assert "100pm" in result
    
    def test_extract_content_types(self):
        """Test content type extraction from input variables"""
        input_vars = {
            "oas": {
                "request_content_types": ["application/json", "application/xml"]
            },
            "methods": ["POST", "PUT"]
        }
        
        content_types = self.builder._extract_content_types(input_vars)
        
        assert "application/json" in content_types
        assert "application/xml" in content_types
    
    def test_extract_content_types_default(self):
        """Test default content type extraction"""
        input_vars = {
            "methods": ["POST", "PUT", "PATCH"]
        }
        
        content_types = self.builder._extract_content_types(input_vars)
        
        assert "application/json" in content_types
        assert "application/xml" in content_types
    
    def test_extract_content_types_get_only(self):
        """Test content type extraction for GET-only APIs"""
        input_vars = {
            "methods": ["GET"]
        }
        
        content_types = self.builder._extract_content_types(input_vars)
        
        # GET-only APIs shouldn't have default content types
        assert len(content_types) == 0
    
    def test_policy_render_creation(self):
        """Test PolicyRender object creation"""
        policy = PolicyRender(
            name="SA-TestPolicy",
            xml="<SpikeArrest>test</SpikeArrest>",
            display_name="Test Policy"
        )
        
        assert policy.name == "SA-TestPolicy"
        assert policy.xml == "<SpikeArrest>test</SpikeArrest>"
        assert policy.display_name == "Test Policy"
    
    def test_attach_plan_creation(self):
        """Test AttachPlan object creation"""
        plan = AttachPlan(
            preflow_request=["VerifyApiKey", "SpikeArrest"],
            preflow_response=[],
            perflow_request=["Quota"],
            perflow_response=[],
            cors_policy="CORS"
        )
        
        assert plan.preflow_request == ["VerifyApiKey", "SpikeArrest"]
        assert plan.perflow_request == ["Quota"]
        assert plan.cors_policy == "CORS"
    
    def test_render_policies_empty_config(self):
        """Test policy rendering with empty configuration"""
        empty_config = {
            "metadata": {"system": "test"},
            "policy_defaults": {},
            "flows": {"preflow": {"request": [], "response": []}},
            "target": {"baseurl": "https://api.example.com"}
        }
        
        policies, plan = self.builder.render_policies(
            empty_config,
            {"methods": ["GET"]},
            system_name="test"
        )
        
        assert isinstance(policies, list)
        assert isinstance(plan, AttachPlan)
    
    def test_render_policies_invalid_system_config(self):
        """Test policy rendering with invalid system configuration"""
        invalid_config = {
            "metadata": {"system": "test"},
            # Missing required fields
        }
        
        with pytest.raises((ValidationError, KeyError)):
            self.builder.render_policies(
                invalid_config,
                {"methods": ["GET"]},
                system_name="test"
            )
    
    def test_render_policies_with_environment_overrides(self):
        """Test policy rendering with environment-specific settings"""
        with patch.dict(os.environ, {'APIGEE_ENV': 'prod'}):
            with patch.object(self.builder, '_render_template') as mock_render:
                mock_render.return_value = '<SpikeArrest><Rate>50pm</Rate></SpikeArrest>'
                
                policies, plan = self.builder.render_policies(
                    self.sample_system_cfg,
                    self.sample_input_vars,
                    system_name="test-system"
                )
                
                assert len(policies) > 0
                # Verify that production environment affects template rendering
                mock_render.assert_called()
    
    def test_render_policies_with_features(self):
        """Test policy rendering with feature flags"""
        input_vars_with_features = {
            **self.sample_input_vars,
            "features": {
                "advanced_security": True,
                "json_threat_protection": True,
                "conditional_policies": True
            }
        }
        
        with patch('app.services.policy_builder.get_feature_evaluator') as mock_evaluator:
            mock_eval_instance = Mock()
            mock_eval_instance.is_enabled.return_value = True
            mock_evaluator.return_value = mock_eval_instance
            
            with patch.object(self.builder, '_render_template') as mock_render:
                mock_render.return_value = '<Policy>test</Policy>'
                
                policies, plan = self.builder.render_policies(
                    self.sample_system_cfg,
                    input_vars_with_features,
                    system_name="test-system"
                )
                
                assert len(policies) > 0
                mock_eval_instance.is_enabled.assert_called()


class TestPolicyBuilderIntegration:
    """Integration tests for PolicyBuilder with real templates"""
    
    def setup_method(self):
        """Set up integration test fixtures"""
        self.builder = PolicyBuilder()
    
    @pytest.mark.integration
    def test_render_real_spike_arrest_template(self):
        """Test rendering with actual SpikeArrest template"""
        # This test requires the actual template files to exist
        try:
            result = self.builder._render_template(
                "SpikeArrest.xml.j2",
                name="SA-IntegrationTest",
                params={"rate": "100pm"},
                input={"methods": ["GET"]},
                env={"name": "test"}
            )
            
            assert "SA-IntegrationTest" in result
            assert "100pm" in result
            assert "<SpikeArrest" in result
        except ValidationError:
            pytest.skip("SpikeArrest template not found - skipping integration test")
    
    @pytest.mark.integration
    def test_render_real_quota_template(self):
        """Test rendering with actual Quota template"""
        try:
            result = self.builder._render_template(
                "Quota.xml.j2",
                name="Q-IntegrationTest",
                params={"allow": "1000pm", "timeunit": "minute"},
                input={"methods": ["GET"]},
                env={"name": "test"}
            )
            
            assert "Q-IntegrationTest" in result
            assert "1000pm" in result
            assert "<Quota" in result
        except ValidationError:
            pytest.skip("Quota template not found - skipping integration test")


class TestPolicyBuilderErrorHandling:
    """Test error handling scenarios"""
    
    def setup_method(self):
        """Set up error handling test fixtures"""
        self.builder = PolicyBuilder()
    
    def test_template_rendering_jinja_error(self):
        """Test handling of Jinja2 template errors"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "bad_template.xml.j2"
            # Invalid Jinja2 syntax
            template_content = '<Policy>{{ unclosed_variable</Policy>'
            template_path.write_text(template_content)
            
            with patch('app.services.policy_builder.Path') as mock_path:
                mock_path.return_value = Path(temp_dir)
                
                with pytest.raises(ValidationError):
                    self.builder._render_template(
                        "bad_template.xml.j2",
                        name="Test",
                        params={},
                        input={},
                        env={}
                    )
    
    def test_template_rendering_missing_params(self):
        """Test template rendering with missing required parameters"""
        with tempfile.TemporaryDirectory() as temp_dir:
            template_path = Path(temp_dir) / "param_template.xml.j2"
            template_content = '<Policy><Required>{{ params.required_param }}</Required></Policy>'
            template_path.write_text(template_content)
            
            with patch('app.services.policy_builder.Path') as mock_path:
                mock_path.return_value = Path(temp_dir)
                
                # Should handle missing parameters gracefully
                result = self.builder._render_template(
                    "param_template.xml.j2",
                    name="Test",
                    params={},  # Missing required_param
                    input={},
                    env={}
                )
                
                # Template should render with empty/None values
                assert "<Policy>" in result
    
    def test_render_policies_with_none_input(self):
        """Test policy rendering with None input variables"""
        with pytest.raises((TypeError, AttributeError)):
            self.builder.render_policies(
                self.sample_system_cfg,
                None,  # None input
                system_name="test"
            )
    
    def test_render_policies_with_malformed_flows(self):
        """Test policy rendering with malformed flow configuration"""
        malformed_config = {
            "metadata": {"system": "test"},
            "policy_defaults": {"VerifyApiKey": {"keyref": "test"}},
            "flows": {
                "preflow": "invalid_flow_config"  # Should be dict, not string
            },
            "target": {"baseurl": "https://api.example.com"}
        }
        
        with pytest.raises((ValidationError, TypeError, AttributeError)):
            self.builder.render_policies(
                malformed_config,
                {"methods": ["GET"]},
                system_name="test"
            )
