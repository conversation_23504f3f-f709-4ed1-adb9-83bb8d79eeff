<LookupCache name="{{ name }}">
  {% if params.cache_resource %}<CacheResource>{{ params.cache_resource }}</CacheResource>{% endif %}
  {% if params.scope %}<Scope>{{ params.scope }}</Scope>{% endif %}
  <CacheKey>
    {% for frag in params.key_fragments %}
    <KeyFragment>{{ frag }}</KeyFragment>
    {% endfor %}
  </CacheKey>
  {% if params.assign_to %}<AssignTo>{{ params.assign_to }}</AssignTo>{% endif %}
</LookupCache>