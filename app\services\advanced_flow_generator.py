"""
Advanced Flow Generation System
==============================
Intelligent flow generation with conditional flows, dynamic conditions,
and context-aware routing for the Apigee proxy framework.
"""

from typing import Any, Dict, List, Optional, Tuple
import structlog

from .feature_flags import get_feature_evaluator

log = structlog.get_logger(__name__)


class FlowCondition:
    """Represents a condition for flow execution"""
    
    def __init__(self, condition_type: str, **kwargs):
        self.type = condition_type
        self.params = kwargs
    
    def to_apigee_condition(self) -> str:
        """Convert to Apigee flow condition format"""
        if self.type == "path_match":
            path_pattern = self.params.get("pattern", "/**")
            return f"(proxy.pathsuffix MatchesPath \"{path_pattern}\")"
        
        elif self.type == "method_match":
            methods = self.params.get("methods", ["GET"])
            if len(methods) == 1:
                return f"(request.verb = \"{methods[0]}\")"
            else:
                method_conditions = [f"request.verb = \"{method}\"" for method in methods]
                return f"({' or '.join(method_conditions)})"
        
        elif self.type == "header_exists":
            header_name = self.params.get("header")
            return f"(request.header.{header_name} != null)"
        
        elif self.type == "header_value":
            header_name = self.params.get("header")
            header_value = self.params.get("value")
            return f"(request.header.{header_name} = \"{header_value}\")"
        
        elif self.type == "query_param":
            param_name = self.params.get("param")
            param_value = self.params.get("value")
            if param_value:
                return f"(request.queryparam.{param_name} = \"{param_value}\")"
            else:
                return f"(request.queryparam.{param_name} != null)"
        
        elif self.type == "content_type":
            content_type = self.params.get("content_type")
            return f"(request.header.content-type ~~ \"*{content_type}*\")"
        
        elif self.type == "custom":
            return self.params.get("condition", "true")
        
        return "true"


class ConditionalFlow:
    """Represents a conditional flow with dynamic conditions"""
    
    def __init__(self, name: str, description: Optional[str] = None,
                 conditions: Optional[List[FlowCondition]] = None,
                 request_policies: Optional[List[str]] = None,
                 response_policies: Optional[List[str]] = None,
                 priority: int = 100):
        self.name = name
        self.description = description
        self.conditions = conditions or []
        self.request_policies = request_policies or []
        self.response_policies = response_policies or []
        self.priority = priority
    
    def get_condition_string(self) -> str:
        """Get the combined condition string for Apigee"""
        if not self.conditions:
            return "true"
        
        condition_strings = [condition.to_apigee_condition() for condition in self.conditions]
        
        if len(condition_strings) == 1:
            return condition_strings[0]
        else:
            return f"({' and '.join(condition_strings)})"
    
    def to_apigee_flow(self) -> Dict[str, Any]:
        """Convert to Apigee flow XML structure"""
        flow = {
            "name": self.name,
            "condition": self.get_condition_string()
        }
        
        if self.description:
            flow["description"] = self.description
        
        if self.request_policies:
            flow["request"] = [{"policy": policy} for policy in self.request_policies]
        
        if self.response_policies:
            flow["response"] = [{"policy": policy} for policy in self.response_policies]
        
        return flow


class AdvancedFlowGenerator:
    """Generates advanced flows with conditional logic and context awareness"""
    
    def __init__(self, env_name: str = "test", system_name: Optional[str] = None):
        self.env_name = env_name
        self.system_name = system_name
        self.feature_evaluator = get_feature_evaluator(env_name, system_name)
    
    def generate_conditional_flows(self, context: Dict[str, Any]) -> List[ConditionalFlow]:
        """Generate conditional flows based on API characteristics and context"""
        flows = []
        
        # Check if dynamic flows are enabled
        if not self.feature_evaluator.is_enabled("dynamic_flows", context):
            return flows
        
        # Generate security flows
        security_flows = self._generate_security_flows(context)
        flows.extend(security_flows)
        
        # Generate method-specific flows
        method_flows = self._generate_method_specific_flows(context)
        flows.extend(method_flows)
        
        # Generate content-type specific flows
        content_flows = self._generate_content_type_flows(context)
        flows.extend(content_flows)
        
        # Generate error handling flows
        error_flows = self._generate_error_handling_flows(context)
        flows.extend(error_flows)
        
        # Sort by priority
        flows.sort(key=lambda f: f.priority)
        
        return flows
    
    def _generate_security_flows(self, context: Dict[str, Any]) -> List[ConditionalFlow]:
        """Generate security-related conditional flows"""
        flows = []
        
        # Admin endpoint protection
        if self.feature_evaluator.is_enabled("advanced_security", context):
            admin_flow = ConditionalFlow(
                name="AdminEndpointProtection",
                description="Enhanced security for admin endpoints",
                conditions=[
                    FlowCondition("path_match", pattern="/admin/**"),
                ],
                request_policies=["VerifyApiKey", "RegularExpressionProtection", "JSONThreatProtection"],
                priority=10
            )
            flows.append(admin_flow)
        
        # High-value endpoint protection
        if context.get("has_sensitive_data"):
            sensitive_flow = ConditionalFlow(
                name="SensitiveDataProtection",
                description="Extra protection for sensitive data endpoints",
                conditions=[
                    FlowCondition("path_match", pattern="/users/**"),
                    FlowCondition("method_match", methods=["POST", "PUT", "PATCH"])
                ],
                request_policies=["VerifyApiKey", "Quota", "JSONThreatProtection"],
                priority=20
            )
            flows.append(sensitive_flow)
        
        return flows
    
    def _generate_method_specific_flows(self, context: Dict[str, Any]) -> List[ConditionalFlow]:
        """Generate method-specific flows"""
        flows = []
        
        methods = context.get("methods", [])
        
        # DELETE method protection
        if "DELETE" in methods and self.feature_evaluator.is_enabled("advanced_security", context):
            delete_flow = ConditionalFlow(
                name="DeleteMethodProtection",
                description="Enhanced protection for DELETE operations",
                conditions=[
                    FlowCondition("method_match", methods=["DELETE"])
                ],
                request_policies=["VerifyApiKey", "Quota", "RegularExpressionProtection"],
                priority=30
            )
            flows.append(delete_flow)
        
        # Bulk operations protection
        if any(method in ["POST", "PUT", "PATCH"] for method in methods):
            bulk_flow = ConditionalFlow(
                name="BulkOperationProtection",
                description="Protection for bulk data operations",
                conditions=[
                    FlowCondition("method_match", methods=["POST", "PUT", "PATCH"]),
                    FlowCondition("header_exists", header="x-bulk-operation")
                ],
                request_policies=["VerifyApiKey", "SpikeArrest", "JSONThreatProtection"],
                priority=40
            )
            flows.append(bulk_flow)
        
        return flows
    
    def _generate_content_type_flows(self, context: Dict[str, Any]) -> List[ConditionalFlow]:
        """Generate content-type specific flows"""
        flows = []
        
        content_types = context.get("content_types", [])
        
        # JSON-specific protection
        if "application/json" in content_types:
            json_flow = ConditionalFlow(
                name="JSONContentProtection",
                description="JSON-specific threat protection",
                conditions=[
                    FlowCondition("content_type", content_type="application/json")
                ],
                request_policies=["JSONThreatProtection"],
                priority=50
            )
            flows.append(json_flow)
        
        # XML-specific protection
        if any("xml" in ct for ct in content_types):
            xml_flow = ConditionalFlow(
                name="XMLContentProtection",
                description="XML-specific threat protection",
                conditions=[
                    FlowCondition("content_type", content_type="xml")
                ],
                request_policies=["XMLThreatProtection"],
                priority=50
            )
            flows.append(xml_flow)
        
        return flows
    
    def _generate_error_handling_flows(self, context: Dict[str, Any]) -> List[ConditionalFlow]:
        """Generate error handling flows"""
        flows = []
        
        if self.feature_evaluator.is_enabled("enhanced_monitoring", context):
            error_flow = ConditionalFlow(
                name="ErrorHandling",
                description="Enhanced error handling and monitoring",
                conditions=[
                    FlowCondition("custom", condition="(response.status.code >= 400)")
                ],
                response_policies=["AssignMessage", "FlowCallout"],
                priority=90
            )
            flows.append(error_flow)
        
        return flows
    
    def generate_flow_xml(self, flows: List[ConditionalFlow]) -> str:
        """Generate XML representation of conditional flows"""
        if not flows:
            return ""
        
        xml_parts = []
        xml_parts.append("  <Flows>")
        
        for flow in flows:
            flow_dict = flow.to_apigee_flow()
            xml_parts.append(f"    <Flow name=\"{flow_dict['name']}\">")
            
            if flow_dict.get("description"):
                xml_parts.append(f"      <Description>{flow_dict['description']}</Description>")
            
            if flow_dict.get("condition"):
                xml_parts.append(f"      <Condition>{flow_dict['condition']}</Condition>")
            
            if flow_dict.get("request"):
                xml_parts.append("      <Request>")
                for policy in flow_dict["request"]:
                    xml_parts.append(f"        <Step><Name>{policy['policy']}</Name></Step>")
                xml_parts.append("      </Request>")
            
            if flow_dict.get("response"):
                xml_parts.append("      <Response>")
                for policy in flow_dict["response"]:
                    xml_parts.append(f"        <Step><Name>{policy['policy']}</Name></Step>")
                xml_parts.append("      </Response>")
            
            xml_parts.append("    </Flow>")
        
        xml_parts.append("  </Flows>")
        
        return "\n".join(xml_parts)


def create_flow_condition_from_config(config: Dict[str, Any]) -> FlowCondition:
    """Create a FlowCondition from configuration"""
    condition_type = config.get("type")
    params = {k: v for k, v in config.items() if k != "type"}
    return FlowCondition(condition_type, **params)


def create_conditional_flow_from_config(config: Dict[str, Any]) -> ConditionalFlow:
    """Create a ConditionalFlow from configuration"""
    name = config.get("name")
    description = config.get("description")
    priority = config.get("priority", 100)
    request_policies = config.get("request_policies", [])
    response_policies = config.get("response_policies", [])
    
    conditions = []
    for condition_config in config.get("conditions", []):
        conditions.append(create_flow_condition_from_config(condition_config))
    
    return ConditionalFlow(
        name=name,
        description=description,
        conditions=conditions,
        request_policies=request_policies,
        response_policies=response_policies,
        priority=priority
    )
