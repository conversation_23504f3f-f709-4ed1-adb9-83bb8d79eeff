"""
Structured logging configuration.

- Standard logging + structlog for JSON logs
- Uvicorn/fastapi loggers unified under the same config
- Correlation ID middleware (X-Request-Id) + generator fallback
"""

from __future__ import annotations

import logging
import sys
import uuid
from typing import Optional

import structlog
from fastapi import Fast<PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

# Header names
REQ_ID_HEADER = "x-request-id"
RESP_ID_HEADER = "x-request-id"


def _configure_stdlib(level: str = "INFO") -> None:
    """Configure stdlib logging as the sink for structlog."""
    logging.basicConfig(
        level=getattr(logging, level.upper(), logging.INFO),
        format="%(message)s",  # structlog renders JSON message
        stream=sys.stdout,
    )

    # Align Uvicorn loggers
    for name in ("uvicorn", "uvicorn.error", "uvicorn.access"):
        logging.getLogger(name).handlers.clear()
        logging.getLogger(name).propagate = True


def _configure_structlog(level: str = "INFO") -> None:
    structlog.configure(
        processors=[
            structlog.contextvars.merge_contextvars,  # merge request-scoped context
            structlog.processors.add_log_level,
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    # Ensure root level matches requested level
    logging.getLogger().setLevel(getattr(logging, level.upper(), logging.INFO))


def configure_logging(level: str = "INFO") -> None:
    """Call once at app startup."""
    _configure_stdlib(level)
    _configure_structlog(level)


class CorrelationIdMiddleware(BaseHTTPMiddleware):
    """
    Adds/propagates a correlation ID:

    - Reads `X-Request-Id` if provided by Apigee pass-through proxy
    - Otherwise generates a UUID4
    - Injects into structlog contextvars
    - Echoes the ID back on the response header
    """

    async def dispatch(self, request: Request, call_next):
        req_id = _get_request_id(request)
        structlog.contextvars.bind_contextvars(request_id=req_id)
        try:
            response: Response = await call_next(request)
        finally:
            # Make sure to unbind to avoid leaking across requests in workers
            structlog.contextvars.clear_contextvars()

        response.headers.setdefault(RESP_ID_HEADER, req_id)
        return response


def _get_request_id(request: Request) -> str:
    value: Optional[str] = request.headers.get(REQ_ID_HEADER)
    value = (value or "").strip()
    return value if value else str(uuid.uuid4())


def install_logging(app: FastAPI, level: str = "INFO") -> None:
    """
    Convenience installer:
    - configure logging/structlog
    - add correlation-id middleware
    """
    configure_logging(level)
    app.add_middleware(CorrelationIdMiddleware)
