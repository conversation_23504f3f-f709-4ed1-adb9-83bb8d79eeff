# Development and Testing Dependencies
# Include base requirements
-r requirements.txt

# Testing Framework Dependencies
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=4.1.0
pytest-xdist>=3.3.0
pytest-mock>=3.12.0
pytest-asyncio>=0.21.0
pytest-timeout>=2.2.0
pytest-benchmark>=4.0.0
pytest-watch>=4.2.0

# Test utilities and mocking
responses>=0.24.0
factory-boy>=3.3.0
faker>=20.0.0
freezegun>=1.2.0
httpx-mock>=0.10.0

# Code quality and linting
flake8>=6.1.0
black>=23.9.0
isort>=5.12.0
mypy>=1.6.0
bandit>=1.7.5
pylint>=3.0.0
autopep8>=2.0.0

# Coverage and reporting
coverage>=7.3.0
coverage-badge>=1.1.0
pytest-json-report>=1.5.0

# Performance testing
locust>=2.17.0
memory-profiler>=0.61.0
line-profiler>=4.1.0

# Development utilities
pre-commit>=3.5.0
tox>=4.11.0
wheel>=0.41.0
twine>=4.0.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
mkdocs>=1.5.0
mkdocs-material>=9.4.0

# Debugging and profiling
pdb++>=0.10.3
ipython>=8.16.0
jupyter>=1.0.0
notebook>=7.0.0

# API testing
tavern>=2.4.0
postman-collection>=0.0.10

# Load testing
artillery>=0.1.0

# Security testing
safety>=2.3.0
semgrep>=1.45.0

# Type checking enhancements
types-PyYAML>=6.0.0
types-requests>=2.31.0
types-setuptools>=68.2.0
