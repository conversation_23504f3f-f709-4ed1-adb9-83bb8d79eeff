"""
ApigeeClient
------------
Thin, resilient client for Apigee Edge Management API.

Implements:
- import_proxy_zip(name, zip_bytes)
- create_policy(api, rev, policy_name, xml)
- update_proxy_endpoint(api, rev, endpoint_name, xml)
- update_target_endpoint(api, rev, target_name, xml)
- upload_resource_file(api, rev, res_type, name, content_bytes)
- export_revision_zip(api, rev) -> bytes

All methods require a Bearer token obtained via AuthService.
"""

from __future__ import annotations

import io
from dataclasses import dataclass
from typing import Any, Dict, Optional
from pydantic import AnyHttpUrl

import httpx
import structlog

from ..core.errors import AuthError, ConflictError, NotFoundError, UpstreamError, ValidationError
from ..core.settings import settings
from ..adapters.http import sleep_with_backoff

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class ImportResult:
    name: str
    revision: int


class ApigeeClient:
    def __init__(
        self,
        *,
        base_url: Optional[AnyHttpUrl] = None,  # Assuming AnyHttpUrl type is used
        org: Optional[str] = None,
        timeout_s: Optional[int] = None,
        http: Optional[httpx.Client] = None,
    ) -> None:
        # Convert base_url or settings.mgmt.base_url to a string before calling rstrip("/")
        self._base = str(base_url or settings.mgmt.base_url).rstrip("/")
        self._org = org or settings.apigee_org
        self._timeout = timeout_s or settings.http_timeout_s
        self._http = http or httpx.Client(timeout=self._timeout)

    def close(self) -> None:
        try:
            self._http.close()
        except Exception:  # pragma: no cover
            pass

    # ---------- High-level helpers ----------

    def import_proxy_zip(self, name: str, zip_bytes: bytes, *, bearer: str) -> ImportResult:
        """
        Create a new API proxy (revision 1) by importing a ZIP bundle.
        """
        if not name or not name.strip():
            raise ValidationError("Proxy name cannot be blank")

        url = f"{self._base}/organizations/{self._org}/apis"
        params = {"action": "import", "name": name.strip()}

        files = {
            "file": ("bundle.zip", io.BytesIO(zip_bytes), "application/zip"),
        }

        log.debug("import_proxy_zip_call", params=params)

        resp = self._request("POST", url, bearer=bearer, params=params, files=files)
        body = _safe_json(resp)

        if resp.status_code == 409:
            raise ConflictError("Proxy already exists", details={"name": name, "body": body})
        _ensure_ok(resp, "Import proxy failed", expected=(200, 201))

        result = ImportResult(name=body.get("name", name), revision=int(body.get("revision", 1)))
        log.info("proxy_imported", name=result.name, revision=result.revision)
        return result

    def create_policy(self, api: str, rev: int, policy_name: str, xml: str, *, bearer: str) -> None:
        """
        Add a policy XML to a specific revision.
        Note: This creates the policy file in the revision; attaching happens via ProxyEndpoint Flows.
        """
        if not policy_name or not policy_name.strip():
            raise ValidationError("Policy name cannot be blank")

        url = (
            f"{self._base}/organizations/{self._org}/apis/{api}/revisions/{rev}/policies"
        )
        params = {"name": policy_name.strip()}
        headers = {"Content-Type": "application/xml"}

        resp = self._request("POST", url, bearer=bearer, params=params, headers=headers, content=xml)
        if resp.status_code == 409:
            raise ConflictError("Policy already exists", details={"policy": policy_name})
        _ensure_ok(resp, "Create policy failed", expected=(200, 201))
        log.info("policy_created", api=api, rev=rev, policy=policy_name)

    def update_proxy_endpoint(
        self, api: str, rev: int, endpoint_name: str, xml: str, *, bearer: str
    ) -> None:
        """
        Update ProxyEndpoint XML (adds Flows, Pre/PostFlow steps, attachments).
        """
        url = (
            f"{self._base}/organizations/{self._org}/apis/{api}/revisions/{rev}/proxies/{endpoint_name}"
        )
        headers = {"Content-Type": "application/xml"}

        resp = self._request("PUT", url, bearer=bearer, headers=headers, content=xml)
        _ensure_ok(resp, "Update ProxyEndpoint failed")
        log.info("proxy_endpoint_updated", api=api, rev=rev, endpoint=endpoint_name)

    def update_target_endpoint(
        self, api: str, rev: int, target_name: str, xml: str, *, bearer: str
    ) -> None:
        """
        Update TargetEndpoint XML (HTTP target connection, SSLInfo, timeouts).
        """
        url = (
            f"{self._base}/organizations/{self._org}/apis/{api}/revisions/{rev}/targets/{target_name}"
        )
        headers = {"Content-Type": "application/xml"}

        resp = self._request("PUT", url, bearer=bearer, headers=headers, content=xml)
        _ensure_ok(resp, "Update TargetEndpoint failed")
        log.info("target_endpoint_updated", api=api, rev=rev, target=target_name)

    def upload_resource_file(
        self,
        api: str,
        rev: int,
        *,
        res_type: str,
        name: str,
        content_bytes: bytes,
        bearer: str,
        content_type: str | None = None,
    ) -> None:
        """
        Uploads a resource file into the proxy revision.
        Common types: jsc, py, xsl, xsd, wsdl, java, (generic) resource
        For swagger.json we typically use a generic folder under the proxy resources; `res_type="resource"`
        and `name="oas/swagger.json"` works well.
        """
        if not name or not name.strip():
            raise ValidationError("Resource name cannot be blank")

        url = (
            f"{self._base}/organizations/{self._org}/apis/{api}/revisions/{rev}/resourcefiles"
        )
        params = {"type": res_type, "name": name}
        # Two upload modes: raw body or multipart. Use raw body with correct content-type.
        headers = {"Content-Type": content_type or "application/octet-stream"}

        resp = self._request(
            "POST",
            url,
            bearer=bearer,
            params=params,
            headers=headers,
            content=content_bytes,
        )
        _ensure_ok(resp, "Upload resource file failed", expected=(200, 201))
        log.info("resource_uploaded", api=api, rev=rev, type=res_type, name=name)

    def export_revision_zip(self, api: str, rev: int, *, bearer: str) -> bytes:
        """
        Export a revision as a ZIP bundle.
        """
        url = f"{self._base}/organizations/{self._org}/apis/{api}/revisions/{rev}"
        params = {"format": "bundle"}
        headers = {"Accept": "application/zip"}

        resp = self._request("GET", url, bearer=bearer, params=params, headers=headers)
        _ensure_ok(resp, "Export revision bundle failed")
        log.info("revision_exported", api=api, rev=rev, size=len(resp.content))
        return resp.content

    # ---------- Internal request helper ----------

    def _request(
        self,
        method: str,
        url: str,
        *,
        bearer: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        content: Any = None,
        files: Any = None,
    ) -> httpx.Response:
        h = {"Authorization": bearer, "Accept": "application/json"}
        if headers:
            h.update(headers)

        max_retries = settings.mgmt.retries.total
        backoff = settings.mgmt.retries.backoff_factor

        for attempt in range(0, max_retries + 1):
            try:
                resp = self._http.request(
                    method, url, params=params, headers=h, content=content, files=files
                )
            except httpx.RequestError as e:
                if attempt >= max_retries:
                    raise UpstreamError(
                        "Network error calling Apigee Management API",
                        details={"url": url, "error": str(e)},
                    ) from e
                sleep_with_backoff(backoff, attempt)
                continue

            # Auth errors
            if resp.status_code in (401, 403):
                # Detect CloudFront wrong-host early
                if _looks_like_cloudfront_misroute(resp):
                    raise UpstreamError(
                        "Likely wrong host (CloudFront runtime) for Management API",
                        details={"status": resp.status_code, "url": url, "body": _safe_json(resp)},
                    )
                raise AuthError(
                    "Unauthorized calling Apigee Management API",
                    details={"status": resp.status_code, "url": url, "body": _safe_json(resp)},
                )

            # Transient server errors
            if resp.status_code >= 500:
                if attempt >= max_retries:
                    raise UpstreamError(
                        "Apigee Management API server error",
                        details={"status": resp.status_code, "url": url, "body": _safe_json(resp)},
                    )
                sleep_with_backoff(backoff, attempt)
                continue

            return resp

        # Shouldn't reach here
        raise UpstreamError("Exhausted retries calling Apigee Management API")


# ---------- Utilities ----------

def _ensure_ok(resp: httpx.Response, msg: str = "Request failed", expected: tuple[int, ...] = ()) -> None:
    """
    Centralized response validator for Apigee Management API calls.

    - Returns None on success (2xx) or if status in `expected`
    - Raises domain exceptions with detailed context otherwise
    - Detects CloudFront HTML to flag wrong-host issues clearly
    """
    url = str(resp.request.url) if resp.request else "unknown"
    method = resp.request.method if resp.request else "?"
    status = resp.status_code
    allowed = set(expected or ())

    # Success: any 2xx or explicitly allowed status codes
    if (200 <= status < 300) or (status in allowed):
        return

    # Common misroute: Management calls pointing at runtime/CDN
    if _looks_like_cloudfront_misroute(resp):
        log.error(
            "apigee_mgmt_wrong_host_cloudfront",
            method=method, url=url, status=status,
            hint=("Use Management API base_url. "
                  "Edge: https://api.enterprise.apigee.com/v1 | "
                  "X/Hybrid: https://apigee.googleapis.com/v1"),
        )
        raise UpstreamError(
            "Likely wrong host (CloudFront runtime) for Management API",
            details={"status": status, "url": url, "body": _safe_json(resp)},
        )

    if status in (401, 403):
        raise AuthError(msg, details={"status": status, "url": url, "body": _safe_json(resp)})

    if status == 404:
        raise NotFoundError(msg, details={"status": status, "url": url, "body": _safe_json(resp)})

    if status == 409:
        raise ConflictError(msg, details={"status": status, "url": url, "body": _safe_json(resp)})

    if status >= 500:
        raise UpstreamError(msg, details={"status": status, "url": url, "body": _safe_json(resp)})

    # Any other 4xx
    raise ValidationError(msg, details={"status": status, "url": url, "body": _safe_json(resp)})


def _looks_like_cloudfront_misroute(resp: httpx.Response) -> bool:
    """
    Heuristic for CloudFront runtime errors (wrong host).
    """
    try:
        server = (resp.headers.get("server") or "").lower()
        if "cloudfront" in server:
            return True
        txt = resp.text or ""
        if "The request could not be satisfied" in txt:
            return True
    except Exception:
        pass
    return False


def _safe_json(resp: httpx.Response):
    try:
        return resp.json()
    except Exception:
        return {"text": resp.text[:1000]}
