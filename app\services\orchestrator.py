"""
Orchestrator
------------
Coordinates end-to-end bootstrap:

1) Exchange passcode -> bearer token
2) Build minimal apiproxy bundle (rev 1 skeleton: apiproxy.xml, proxies/default.xml, targets/default.xml)
3) Import proxy ZIP -> revision 1
4) Render policies from system YAML + user input -> create policy files
5) Build ProxyEndpoint with Flows from OAS -> update proxy endpoint
6) Build TargetEndpoint -> update target endpoint
7) Upload resource files (swagger.json under /oas/, plus configured JS)
8) Export revision ZIP
9) (Optional) Push ZIP to Git (zip-only, develop branch)

Notes
- Compensation (delete revision/proxy) can be added when delete endpoints are exposed.
- All outbound calls use ApigeeClient and AuthService.
"""

from __future__ import annotations

import io
import json
import zipfile
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse, quote_plus

import structlog
import yaml

from ..core.errors import (
    <PERSON><PERSON><PERSON>r,
    DomainError,
    UpstreamError,
    ValidationError,
)
from ..core.settings import settings
from .apigee_client import ApigeeClient, ImportResult
from .auth_service import AuthService
from .flow_builder import FlowBuilder
from app.config.loader import ConfigLoader
from .policy_builder import PolicyBuilder
from .target_builder import TargetBuilder
from .resource_uploader import ResourceUploader
from .exporter import Exporter
from .git_service import GitService
from .swagger_fetcher import SwaggerFetcher, fetch_swagger_content
from .swagger_processor import SwaggerProcessor
from .bruno_generator import BrunoGenerator


log = structlog.get_logger(__name__)


# ---------- DTOs ----------

@dataclass(frozen=True)
class GitOptions:
    commit_message: Optional[str] = None
    # repo_url and branch now read from system config


@dataclass(frozen=True)
class BootstrapRequest:
    passcode: str
    system_name: str
    proxy_name: str
    swagger_content: bytes  # raw file content or will be fetched from URL
    git: Optional[GitOptions] = None

    # Enhanced parameters - fully configuration-driven
    policy_params: Optional[Dict[str, Dict[str, Any]]] = None  # Direct policy name mapping
    features: Optional[Dict[str, Any]] = None  # Feature flags

    # Swagger processing options
    swagger_url: Optional[str] = None  # If provided, fetch swagger from URL instead of using content


@dataclass(frozen=True)
class BootstrapResult:
    proxy_name: str
    revision: int
    org: str
    env: str
    bundle_sha256: str
    export_size: int
    git_commit_id: Optional[str] = None
    git_web_url: Optional[str] = None

    # New: Feature-driven policy recommendations
    feature_impact: Optional[Dict[str, Any]] = None
    policies_generated: Optional[List[str]] = None
    environment_notes: Optional[str] = None


# ---------- Orchestrator ----------

class Orchestrator:
    def __init__(
        self,
        auth: Optional[AuthService] = None,
        apigee: Optional[ApigeeClient] = None,
        policy_builder: Optional[PolicyBuilder] = None,
        flow_builder: Optional[FlowBuilder] = None,
        target_builder: Optional[TargetBuilder] = None,
        resource_uploader: Optional[ResourceUploader] = None,
        exporter: Optional[Exporter] = None,
        git_service: Optional[GitService] = None,
        swagger_fetcher: Optional[SwaggerFetcher] = None,
        swagger_processor: Optional[SwaggerProcessor] = None,
        bruno_generator: Optional[BrunoGenerator] = None,
    ) -> None:
        self.auth = auth or AuthService()
        self.apigee = apigee or ApigeeClient()
        self.policy_builder = policy_builder or PolicyBuilder()
        self.flow_builder = flow_builder or FlowBuilder()
        self.target_builder = target_builder or TargetBuilder()
        self.resource_uploader = resource_uploader or ResourceUploader(self.apigee)
        self.exporter = exporter or Exporter(self.apigee)
        self.git_service = git_service or GitService()
        self.swagger_fetcher = swagger_fetcher or SwaggerFetcher()
        self.swagger_processor = swagger_processor or SwaggerProcessor()
        self.bruno_generator = bruno_generator or BrunoGenerator()

    def bootstrap(self, req: BootstrapRequest) -> BootstrapResult:
        """
        Executes the full bootstrap flow and returns export + (optional) Git info.
        """
        # 1) Auth
        token = self.auth.exchange_passcode(req.passcode)
        bearer = f"{token.token_type} {token.token}"

        # 2) Load system config
        system_cfg = _load_system_yaml(req.system_name)

        # 3) Fetch swagger content if URL is provided
        target_swagger_content = req.swagger_content
        if req.swagger_url:
            log.info("fetching_swagger_from_url", url=req.swagger_url)
            target_swagger_content = self.swagger_fetcher.fetch_swagger_from_url(
                req.swagger_url,
                req.swagger_headers
            )

        # 4) Build minimal proxy/target skeleton and import (rev 1)
        base_path = _infer_basepath_from_oas(target_swagger_content)
        target_xml = self.target_builder.build_target_endpoint_xml(system_cfg)
        proxy_xml_min = _minimal_proxy_xml(base_path=base_path)

        bundle = _make_initial_zip(req.proxy_name, proxy_xml_min, target_xml)
        imp: ImportResult = self.apigee.import_proxy_zip(req.proxy_name, bundle, bearer=bearer)
        rev = imp.revision




        # 5) Render policies from system + inputs, then create policy files

        methods, headers = _extract_methods_and_headers(target_swagger_content)

        # Build enhanced input variables - fully configuration-driven
        input_vars = {
            # OAS-derived values
            "methods": methods,
            "headers": headers,

            # Enhanced parameters
            "policy_params": req.policy_params or {},
            # Features removed - now system-driven
            "content_types": _extract_content_types_from_oas(target_swagger_content),
        }

        loader = ConfigLoader()
        cfgs = loader.load(system_name=req.system_name)
        # Policy layer (DEFAULT < ENV < INPUT) with system-driven feature evaluation:
        render_result = self.policy_builder.render_policies(
            cfgs.system_cfg,
            input_vars,
            system_name=req.system_name,
        )

        # Handle feature impact from PolicyBuilder
        if len(render_result) == 3:
            policies, attach_plan, feature_impact = render_result
        else:
            # Backward compatibility
            policies, attach_plan = render_result
            feature_impact = {}
        # Create policies in Apigee
        policy_names = []
        for p in policies:
            self.apigee.create_policy(req.proxy_name, rev, p.name, p.xml, bearer=bearer)
            policy_names.append(p.name)

        # 6) Build ProxyEndpoint with flows (attaching policies via Steps) and update
        proxy_xml_full = self.flow_builder.build_proxy_endpoint_xml(
            oas_content=target_swagger_content,
            attach_plan=attach_plan,
            system_cfg=system_cfg,
            proxy_endpoint_name="default",
            target_endpoint_name="default",
            virtual_host="default",
        )
        self.apigee.update_proxy_endpoint(req.proxy_name, rev, "default", proxy_xml_full, bearer=bearer)

        # 7) Ensure target matches the builder output
        self.apigee.update_target_endpoint(req.proxy_name, rev, "default", target_xml, bearer=bearer)

        # 8) Process swagger for proxy and upload resources
        # Create proxy swagger with Apigee-specific modifications
        proxy_swagger_content = self.swagger_processor.create_proxy_swagger(
            target_swagger_content=target_swagger_content,
            apigee_base_path=base_path,
            apigee_host=settings.apigee_host,
            apigee_schemes=settings.apigee_schemes,
            additional_headers=headers
        )

        # Upload both proxy swagger and original target swagger
        self.resource_uploader.upload_swagger(req.proxy_name, rev, proxy_swagger_content, bearer)
        self.resource_uploader.upload_target_swagger(req.proxy_name, rev, target_swagger_content, bearer)
        self.resource_uploader.upload_js_from_system(req.proxy_name, rev, system_cfg, bearer)

        # 9) Export revision bundle
        export = self.exporter.export(req.proxy_name, rev, bearer)

        # 10) Git operations: Create project, push ZIP, and generate Bruno collection
        commit_id = web_url = None
        if self.git_service:
            bundles_repo = system_cfg["git"]["bundles_repo"]

            # Create project if it doesn't exist
            try:
                project_details = self.git_service.create_project(
                    project_name=req.proxy_name,
                    namespace_path=bundles_repo,
                    visibility="private",
                    description=f"Project for {req.proxy_name} proxy",
                )
                project_id = project_details['project_id']
                log.info("git_project_created", name=req.proxy_name, namespace_path=bundles_repo)
            except UpstreamError as e:
                if e.details.get("status") == 409:  # Conflict: Project already exists
                    log.warning("git_project_exists", name=req.proxy_name, namespace_path=bundles_repo)
                else:
                    raise

            # Generate Bruno API collection from proxy swagger
            bruno_collection_name = f"{req.proxy_name} API Collection"
            bruno_zip = self.bruno_generator.generate_bruno_collection(
                swagger_content=proxy_swagger_content,
                collection_name=bruno_collection_name,
                base_url=f"https://{settings.apigee_host or 'api.example.com'}{base_path}" if settings.apigee_host else None
            )

            # Push both ZIP bundle and Bruno collection to the repository
            branch = system_cfg.get("git", {}).get("default_branch") or settings.git.default_branch
            message = (req.git.commit_message if req.git else None) or f"feat: bootstrap {req.proxy_name}@rev{rev}"

            # Create a combined ZIP with both bundle and Bruno collection
            log.info("creating_combined_git_content",
                    bundle_size=len(export.data),
                    bruno_size=len(bruno_zip),
                    proxy_name=req.proxy_name,
                    revision=rev)

            combined_zip = self._create_combined_git_content(
                export.data,
                bruno_zip,
                req.proxy_name,
                rev
            )

            log.info("combined_zip_created",
                    combined_size=len(combined_zip),
                    file_path=f"combined-{req.proxy_name}-{rev}.zip")

            commit_id, web_url = self.git_service.push_zip_source_code(
                combined_zip,
                project_id=project_id,
                branch=branch,
                commit_message=message,
                file_path=f"combined-{req.proxy_name}-{rev}.zip"
            )

        # Generate environment notes
        env_name = settings.apigee_env.lower()
        environment_notes = None
        if env_name in ['dev', 'test']:
            environment_notes = f"Proxy created in {env_name} environment with system-driven feature evaluation. For production deployment, use the production pipeline."

        return BootstrapResult(
            proxy_name=req.proxy_name,
            revision=rev,
            org=settings.apigee_org,
            env=settings.apigee_env,
            bundle_sha256=export.sha256,
            export_size=export.size,
            git_commit_id=commit_id,
            git_web_url=web_url,
            feature_impact=feature_impact,
            policies_generated=policy_names,
            environment_notes=environment_notes,
        )

    def _create_combined_git_content(
        self,
        bundle_zip: bytes,
        bruno_zip: bytes,
        proxy_name: str,
        rev: int
    ) -> bytes:
        """
        Create a combined ZIP containing both the Apigee bundle and Bruno collection.
        Structure:
        - bundles/{proxy_name}-{rev}.zip (Apigee bundle)
        - api-collection/{proxy_name}-collection.zip (Bruno collection)
        """
        # Validate inputs
        if not bundle_zip:
            raise ValidationError("Bundle ZIP content is empty")
        if not bruno_zip:
            raise ValidationError("Bruno collection ZIP content is empty")
        if not proxy_name or not proxy_name.strip():
            raise ValidationError("Proxy name is required")

        # Sanitize proxy name for file paths
        safe_proxy_name = self._sanitize_filename(proxy_name)

        combined_buffer = io.BytesIO()

        try:
            with zipfile.ZipFile(combined_buffer, 'w', zipfile.ZIP_DEFLATED) as combined_zip:
                # Add Apigee bundle
                bundle_path = f"bundles/{safe_proxy_name}-{rev}.zip"
                combined_zip.writestr(bundle_path, bundle_zip)
                log.debug("added_bundle_to_combined_zip", path=bundle_path, size=len(bundle_zip))

                # Add Bruno collection
                collection_path = f"api-collection/{safe_proxy_name}-collection.zip"
                combined_zip.writestr(collection_path, bruno_zip)
                log.debug("added_bruno_to_combined_zip", path=collection_path, size=len(bruno_zip))

                # Add README
                readme_content = self._generate_git_readme(safe_proxy_name, rev)
                combined_zip.writestr("README.md", readme_content)
                log.debug("added_readme_to_combined_zip", size=len(readme_content))

            result = combined_buffer.getvalue()
            log.info("combined_zip_structure_created",
                    total_size=len(result),
                    bundle_size=len(bundle_zip),
                    bruno_size=len(bruno_zip),
                    readme_size=len(readme_content))

            return result

        except Exception as e:
            log.error("failed_to_create_combined_zip", error=str(e))
            raise ValidationError(f"Failed to create combined ZIP: {str(e)}")

    def _sanitize_filename(self, name: str) -> str:
        """Sanitize string for use as filename."""
        # Replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            name = name.replace(char, '_')

        # Remove extra spaces and underscores
        name = '_'.join(name.split())

        return name[:50]  # Limit length

    def _generate_git_readme(self, proxy_name: str, rev: int) -> str:
        """Generate README content for the Git repository."""
        return f"""# {proxy_name} API Proxy

This repository contains the Apigee API proxy bundle and related resources.

## Contents

- `bundles/{proxy_name}-{rev}.zip` - Apigee proxy bundle (revision {rev})
- `api-collection/{proxy_name}-collection.zip` - Bruno API collection for testing

## Usage

### Apigee Bundle
The bundle in the `bundles/` folder can be imported into Apigee Edge or Apigee X.

### Bruno Collection
The Bruno collection in the `api-collection/` folder can be imported into Bruno API client for testing the proxy endpoints.

## Generated
This repository was automatically generated by Apigee Proxy Factory.
Generated at: {self._get_current_timestamp()}
"""

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')


# ---------- Helper functions ----------

def _load_system_yaml(system_name: str) -> Dict[str, Any]:
    if not system_name or not system_name.strip():
        raise ValidationError("system_name cannot be blank")
    path = Path(settings.systems_dir).joinpath(f"{system_name}.yaml").resolve()
    if not path.exists():
        raise ValidationError("System config not found", details={"path": str(path)})
    with path.open("r", encoding="utf-8") as fh:
        return yaml.safe_load(fh) or {}


def _infer_basepath_from_oas(content: bytes) -> str:
    """
    Extracts basePath (swagger 2.0) or servers[0].url path (oas3).
    Fallback to "/".
    """
    text = content.decode("utf-8", errors="ignore")
    try:
        data = json.loads(text)
    except Exception:
        data = yaml.safe_load(text)

    if not isinstance(data, dict):
        return "/"

    if "openapi" in data:
        servers = data.get("servers") or []
        if servers and isinstance(servers[0], dict):
            from urllib.parse import urlparse
            url = servers[0].get("url")
            try:
                path = urlparse(url).path if url else "/"
            except Exception:
                path = "/"
            return _normalize_basepath(path or "/")
        return "/"
    elif "swagger" in data:
        return _normalize_basepath(str(data.get("basePath") or "/"))

    return "/"


def _normalize_basepath(base: str) -> str:
    base = base or "/"
    if not base.startswith("/"):
        base = "/" + base
    if base != "/" and base.endswith("/"):
        base = base[:-1]
    return base

def _extract_methods_and_headers(swagger_content: bytes) -> Tuple[List[str], List[str]]:
    """
    Extracts unique HTTP methods and headers from Swagger content.
    """
    text = swagger_content.decode("utf-8", errors="ignore")
    try:
        data = json.loads(text)
    except Exception:
        data = yaml.safe_load(text)

    if not isinstance(data, dict):
        return [], []

    methods = set()
    headers = set()

    # Swagger 2.0 paths
    paths = data.get("paths", {})
    for _, operations in paths.items():
        for method, operation in operations.items():
            methods.add(method.upper())  # HTTP methods are case-insensitive
            parameters = operation.get("parameters", [])
            for param in parameters:
                if param.get("in") == "header":
                    headers.add(param.get("name"))

    return sorted(methods), sorted(headers)


def _extract_content_types_from_oas(swagger_content: bytes) -> List[str]:
    """Extract content types from OAS content for feature flag evaluation."""
    text = swagger_content.decode("utf-8", errors="ignore")
    try:
        data = json.loads(text)
    except Exception:
        data = yaml.safe_load(text)

    if not isinstance(data, dict):
        return []

    content_types = set()

    # Extract content types from paths
    paths = data.get("paths", {})
    for _, operations in paths.items():
        for _, operation in operations.items():
            if not isinstance(operation, dict):
                continue

            # Check request body content types
            request_body = operation.get("requestBody", {})
            if isinstance(request_body, dict):
                content = request_body.get("content", {})
                content_types.update(content.keys())

            # Check response content types
            responses = operation.get("responses", {})
            for response in responses.values():
                if isinstance(response, dict):
                    response_content = response.get("content", {})
                    content_types.update(response_content.keys())

            # Check consumes/produces for Swagger 2.0
            content_types.update(operation.get("consumes", []))
            content_types.update(operation.get("produces", []))

    # Also check global consumes/produces
    content_types.update(data.get("consumes", []))
    content_types.update(data.get("produces", []))

    return sorted([ct for ct in content_types if ct])


def _make_initial_zip(proxy_name: str, proxy_xml: str, target_xml: str) -> bytes:
    """
    Creates a minimal apiproxy bundle:
      apiproxy/apiproxy.xml
      apiproxy/proxies/default.xml
      apiproxy/targets/default.xml
    """
    buf = io.BytesIO()
    with zipfile.ZipFile(buf, "w", zipfile.ZIP_DEFLATED) as z:
        # apiproxy.xml (minimal)
        apiproxy_xml = f'<APIProxy name="{proxy_name}"></APIProxy>'
        z.writestr("apiproxy/apiproxy.xml", apiproxy_xml)
        z.writestr("apiproxy/proxies/default.xml", proxy_xml)
        z.writestr("apiproxy/targets/default.xml", target_xml)
    return buf.getvalue()


def _minimal_proxy_xml(*, base_path: str) -> str:
    """
    Minimal ProxyEndpoint XML (no flows/policies yet). We'll PUT full flows later.
    """
    return f"""
<ProxyEndpoint name="default">
  <HTTPProxyConnection>
    <BasePath>{base_path}</BasePath>
    <VirtualHost>default</VirtualHost>
  </HTTPProxyConnection>
  <RouteRule name="default">
    <TargetEndpoint>default</TargetEndpoint>
  </RouteRule>
  <PreFlow name="PreFlow">
    <Request/>
    <Response/>
  </PreFlow>
  <PostFlow name="PostFlow">
    <Request/>
    <Response/>
  </PostFlow>
  <PostClientFlow name="PostClientFlow">
    <Request/>
    <Response/>
  </PostClientFlow>
  <Flows/>
</ProxyEndpoint>
""".strip()





