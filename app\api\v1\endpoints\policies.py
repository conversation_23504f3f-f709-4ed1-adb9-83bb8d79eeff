"""
Policy Catalog API Endpoints
Provides access to policy metadata, categories, and templates.
"""

from typing import Any, Dict, List, Optional, Tuple
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from ....services.policy_catalog import get_policy_catalog
from ....core.errors import ValidationError

router = APIRouter()


# Response Models
class PolicyInfo(BaseModel):
    """Policy information model"""
    name: str
    template: str
    description: str
    required_params: List[str] = Field(default_factory=list)
    optional_params: List[str] = Field(default_factory=list)
    param_config: Dict[str, Dict[str, Any]] = Field(default_factory=dict)


class GroupInfo(BaseModel):
    """Group information model (categories and templates)"""
    name: str
    description: str
    icon: Optional[str] = None
    type: str  # "category" or "template"
    policies: List[str] = Field(default_factory=list)


# Backward compatibility models
class CategoryInfo(BaseModel):
    """Category information model (backward compatibility)"""
    name: str
    description: str
    icon: Optional[str] = None
    policies: List[str] = Field(default_factory=list)


class TemplateInfo(BaseModel):
    """Template information model (backward compatibility)"""
    name: str
    description: str
    icon: Optional[str] = None
    policies: List[str]


# Validation Models
class PolicyValidationRequest(BaseModel):
    """Request model for policy parameter validation"""
    policy_name: str
    params: Dict[str, Any]


class GroupValidationRequest(BaseModel):
    """Request model for group policy validation"""
    group_name: str
    policy_params: Dict[str, Dict[str, Any]]


class ValidationResponse(BaseModel):
    """Response model for validation results"""
    is_valid: bool
    errors: List[str] = Field(default_factory=list)


class PolicyCatalogResponse(BaseModel):
    """Complete policy catalog response"""
    policies: Dict[str, PolicyInfo]
    groups: Dict[str, GroupInfo]
    # Backward compatibility
    categories: Dict[str, CategoryInfo] = Field(default_factory=dict)
    templates: Dict[str, TemplateInfo] = Field(default_factory=dict)


@router.get(
    "",
    response_model=PolicyCatalogResponse,
    summary="Get complete policy catalog",
    description="Returns all policies, categories, and templates with their metadata"
)
async def get_catalog():
    """Get the complete policy catalog"""
    try:
        catalog = get_policy_catalog()
        
        # Build policies response
        policies = {}
        for policy_name in catalog.list_all_policies():
            policy_info = catalog.get_policy_info(policy_name)
            if policy_info:
                policies[policy_name] = PolicyInfo(
                    name=policy_info.get("name", policy_name),
                    template=policy_info.get("template", ""),
                    description=policy_info.get("description", ""),
                    required_params=policy_info.get("required_params", []),
                    optional_params=policy_info.get("optional_params", []),
                    param_config=policy_info.get("param_config", {})
                )

        # Build groups response
        groups = {}
        for group_name in catalog.list_all_groups():
            group_info = catalog.get_group_info(group_name)
            if group_info:
                groups[group_name] = GroupInfo(
                    name=group_info.get("name", group_name),
                    description=group_info.get("description", ""),
                    icon=group_info.get("icon"),
                    type=group_info.get("type", ""),
                    policies=catalog.get_group_policies(group_name)
                )

        # Build backward compatibility responses
        categories = {}
        for category_name in catalog.list_all_categories():
            category_info = catalog.get_category_info(category_name)
            if category_info:
                categories[category_name] = CategoryInfo(
                    name=category_info.get("name", category_name),
                    description=category_info.get("description", ""),
                    icon=category_info.get("icon"),
                    policies=catalog.get_policies_by_category(category_name)
                )

        templates = {}
        for template_name in catalog.list_all_templates():
            template_info = catalog.get_template_info(template_name)
            if template_info:
                templates[template_name] = TemplateInfo(
                    name=template_info.get("name", template_name),
                    description=template_info.get("description", ""),
                    icon=template_info.get("icon"),
                    policies=template_info.get("policies", [])
                )

        return PolicyCatalogResponse(
            policies=policies,
            groups=groups,
            categories=categories,
            templates=templates
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load policy catalog: {str(e)}")


@router.get(
    "/policies",
    response_model=Dict[str, PolicyInfo],
    summary="Get all policies",
    description="Returns all available policies with their metadata"
)
async def get_policies(
    group: Optional[str] = Query(None, description="Filter by group (category or template)")
):
    """Get all policies, optionally filtered by group"""
    try:
        catalog = get_policy_catalog()
        policies = {}

        policy_names = (
            catalog.get_group_policies(group) if group
            else catalog.list_all_policies()
        )

        for policy_name in policy_names:
            policy_info = catalog.get_policy_info(policy_name)
            if policy_info:
                policies[policy_name] = PolicyInfo(
                    name=policy_info.get("name", policy_name),
                    template=policy_info.get("template", ""),
                    description=policy_info.get("description", ""),
                    required_params=policy_info.get("required_params", []),
                    optional_params=policy_info.get("optional_params", []),
                    param_config=policy_info.get("param_config", {})
                )

        return policies

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get policies: {str(e)}")


@router.get(
    "/policies/{policy_name}",
    response_model=PolicyInfo,
    summary="Get specific policy information",
    description="Returns detailed information about a specific policy"
)
async def get_policy(policy_name: str):
    """Get information about a specific policy"""
    try:
        catalog = get_policy_catalog()
        policy_info = catalog.get_policy_info(policy_name)
        
        if not policy_info:
            raise HTTPException(status_code=404, detail=f"Policy '{policy_name}' not found")
        
        return PolicyInfo(
            name=policy_info.get("name", policy_name),
            template=policy_info.get("template", ""),
            description=policy_info.get("description", ""),
            required_params=policy_info.get("required_params", []),
            optional_params=policy_info.get("optional_params", []),
            param_config=policy_info.get("param_config", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get policy: {str(e)}")


@router.get(
    "/groups",
    response_model=Dict[str, GroupInfo],
    summary="Get all groups",
    description="Returns all policy groups (categories and templates) with their metadata"
)
async def get_groups(
    type: Optional[str] = Query(None, description="Filter by type: 'category' or 'template'")
):
    """Get all policy groups, optionally filtered by type"""
    try:
        catalog = get_policy_catalog()

        if type:
            groups_data = catalog.get_groups_by_type(type)
        else:
            groups_data = catalog.get_groups()

        groups = {}
        for group_name, group_info in groups_data.items():
            groups[group_name] = GroupInfo(
                name=group_info.get("name", group_name),
                description=group_info.get("description", ""),
                icon=group_info.get("icon"),
                type=group_info.get("type", ""),
                policies=catalog.get_group_policies(group_name)
            )

        return groups

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get groups: {str(e)}")


@router.get(
    "/categories",
    response_model=Dict[str, CategoryInfo],
    summary="Get all categories (backward compatibility)",
    description="Returns all policy categories with their metadata"
)
async def get_categories():
    """Get all policy categories (backward compatibility)"""
    try:
        catalog = get_policy_catalog()
        categories = {}

        for category_name in catalog.list_all_categories():
            category_info = catalog.get_category_info(category_name)
            if category_info:
                categories[category_name] = CategoryInfo(
                    name=category_info.get("name", category_name),
                    description=category_info.get("description", ""),
                    icon=category_info.get("icon"),
                    policies=catalog.get_policies_by_category(category_name)
                )

        return categories

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")


@router.get(
    "/templates",
    response_model=Dict[str, TemplateInfo],
    summary="Get all templates (backward compatibility)",
    description="Returns all policy templates with their metadata"
)
async def get_templates():
    """Get all policy templates (backward compatibility)"""
    try:
        catalog = get_policy_catalog()
        templates = {}

        for template_name in catalog.list_all_templates():
            template_info = catalog.get_template_info(template_name)
            if template_info:
                templates[template_name] = TemplateInfo(
                    name=template_info.get("name", template_name),
                    description=template_info.get("description", ""),
                    icon=template_info.get("icon"),
                    policies=template_info.get("policies", [])
                )

        return templates

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get templates: {str(e)}")


@router.get(
    "/groups/{group_name}",
    response_model=GroupInfo,
    summary="Get specific group information",
    description="Returns detailed information about a specific group (category or template)"
)
async def get_group(group_name: str):
    """Get information about a specific group"""
    try:
        catalog = get_policy_catalog()
        group_info = catalog.get_group_info(group_name)

        if not group_info:
            raise HTTPException(status_code=404, detail=f"Group '{group_name}' not found")

        return GroupInfo(
            name=group_info.get("name", group_name),
            description=group_info.get("description", ""),
            icon=group_info.get("icon"),
            type=group_info.get("type", ""),
            policies=catalog.get_group_policies(group_name)
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get group: {str(e)}")


@router.get(
    "/templates/{template_name}",
    response_model=TemplateInfo,
    summary="Get specific template information (backward compatibility)",
    description="Returns detailed information about a specific template"
)
async def get_template(template_name: str):
    """Get information about a specific template (backward compatibility)"""
    try:
        catalog = get_policy_catalog()
        template_info = catalog.get_template_info(template_name)

        if not template_info:
            raise HTTPException(status_code=404, detail=f"Template '{template_name}' not found")

        return TemplateInfo(
            name=template_info.get("name", template_name),
            description=template_info.get("description", ""),
            icon=template_info.get("icon"),
            policies=template_info.get("policies", [])
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get template: {str(e)}")


# ==================== Validation Endpoints ====================

@router.post(
    "/validate/policy",
    response_model=ValidationResponse,
    summary="Validate policy parameters",
    description="Validates parameters for a specific policy against its requirements"
)
async def validate_policy_params(request: PolicyValidationRequest):
    """Validate parameters for a specific policy"""
    try:
        catalog = get_policy_catalog()
        is_valid, errors = catalog.validate_policy_params(request.policy_name, request.params)

        return ValidationResponse(
            is_valid=is_valid,
            errors=errors
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to validate policy parameters: {str(e)}")


@router.post(
    "/validate/group",
    response_model=ValidationResponse,
    summary="Validate group policy parameters",
    description="Validates parameters for all policies in a group"
)
async def validate_group_params(request: GroupValidationRequest):
    """Validate parameters for all policies in a group"""
    try:
        catalog = get_policy_catalog()
        is_valid, errors = catalog.validate_group_policies(request.group_name, request.policy_params)

        return ValidationResponse(
            is_valid=is_valid,
            errors=errors
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to validate group parameters: {str(e)}")


@router.get(
    "/policies/{policy_name}/params",
    response_model=Dict[str, List[str]],
    summary="Get policy parameter requirements",
    description="Returns required and optional parameters for a specific policy"
)
async def get_policy_params(policy_name: str):
    """Get parameter requirements for a specific policy"""
    try:
        catalog = get_policy_catalog()

        if not catalog.validate_policy_exists(policy_name):
            raise HTTPException(status_code=404, detail=f"Policy '{policy_name}' not found")

        return {
            "required": catalog.get_required_params(policy_name),
            "optional": catalog.get_optional_params(policy_name),
            "all": catalog.get_all_params(policy_name)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get policy parameters: {str(e)}")


@router.get(
    "/groups/{group_name}/params",
    response_model=Dict[str, Dict[str, List[str]]],
    summary="Get group policy parameter requirements",
    description="Returns parameter requirements for all policies in a group"
)
async def get_group_params(group_name: str):
    """Get parameter requirements for all policies in a group"""
    try:
        catalog = get_policy_catalog()

        if not catalog.validate_group_exists(group_name):
            raise HTTPException(status_code=404, detail=f"Group '{group_name}' not found")

        group_policies = catalog.get_group_policies(group_name)
        result = {}

        for policy_name in group_policies:
            result[policy_name] = {
                "required": catalog.get_required_params(policy_name),
                "optional": catalog.get_optional_params(policy_name),
                "all": catalog.get_all_params(policy_name)
            }

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get group parameters: {str(e)}")
