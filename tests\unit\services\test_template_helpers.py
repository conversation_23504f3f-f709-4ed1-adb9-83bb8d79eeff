"""
Unit tests for TemplateHelpers service
=====================================
Comprehensive test coverage for template helper functions,
validation utilities, and template context management.
"""

import pytest
from unittest.mock import Mock, patch
import xml.etree.ElementTree as ET

from app.services.template_helpers import TemplateHelpers


class TestTemplateHelpers:
    """Test suite for TemplateHelpers class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.helpers = TemplateHelpers(env_name="test", features={"test_feature": True})
    
    def test_init(self):
        """Test TemplateHelpers initialization"""
        helpers = TemplateHelpers("prod", {"feature1": True})
        
        assert helpers.env_name == "prod"
        assert helpers.features == {"feature1": True}
    
    def test_init_defaults(self):
        """Test TemplateHelpers initialization with defaults"""
        helpers = TemplateHelpers()
        
        assert helpers.env_name == "test"
        assert helpers.features == {}
    
    # Validation helper tests
    def test_is_valid_rate_valid_formats(self):
        """Test rate validation with valid formats"""
        valid_rates = ["100pm", "50ps", "200ph", "10pd", "1pm", "999ps"]
        
        for rate in valid_rates:
            assert self.helpers.is_valid_rate(rate) is True, f"Rate {rate} should be valid"
    
    def test_is_valid_rate_invalid_formats(self):
        """Test rate validation with invalid formats"""
        invalid_rates = ["100", "pm", "100px", "invalid", "", "100 pm", "100PM"]
        
        for rate in invalid_rates:
            assert self.helpers.is_valid_rate(rate) is False, f"Rate {rate} should be invalid"
    
    def test_is_valid_url_valid_urls(self):
        """Test URL validation with valid URLs"""
        valid_urls = [
            "https://api.example.com",
            "http://localhost:8080",
            "https://api.example.com/v1/users",
            "https://subdomain.example.com:443/path?query=value"
        ]
        
        for url in valid_urls:
            assert self.helpers.is_valid_url(url) is True, f"URL {url} should be valid"
    
    def test_is_valid_url_invalid_urls(self):
        """Test URL validation with invalid URLs"""
        invalid_urls = [
            "not-a-url",
            "ftp://example.com",
            "example.com",
            "",
            "https://",
            "http://"
        ]
        
        for url in invalid_urls:
            assert self.helpers.is_valid_url(url) is False, f"URL {url} should be invalid"
    
    def test_is_valid_header_name_valid_names(self):
        """Test header name validation with valid names"""
        valid_names = [
            "Content-Type",
            "Authorization",
            "X-API-Key",
            "x-request-id",
            "Custom-Header"
        ]
        
        for name in valid_names:
            assert self.helpers.is_valid_header_name(name) is True, f"Header {name} should be valid"
    
    def test_is_valid_header_name_invalid_names(self):
        """Test header name validation with invalid names"""
        invalid_names = [
            "Invalid Header!",
            "Header With Spaces",
            "Header@Symbol",
            "",
            "Header\nNewline",
            "Header\tTab"
        ]
        
        for name in invalid_names:
            assert self.helpers.is_valid_header_name(name) is False, f"Header {name} should be invalid"
    
    def test_is_valid_json_path_valid_paths(self):
        """Test JSON path validation with valid paths"""
        valid_paths = [
            "$.user.id",
            "$.data[0].name",
            "$.response.users[*].email",
            "$.root"
        ]
        
        for path in valid_paths:
            assert self.helpers.is_valid_json_path(path) is True, f"JSON path {path} should be valid"
    
    def test_is_valid_json_path_invalid_paths(self):
        """Test JSON path validation with invalid paths"""
        invalid_paths = [
            "user.id",  # Missing $
            "$.user.",  # Trailing dot
            "",
            "invalid",
            "$."  # Just root
        ]
        
        for path in invalid_paths:
            assert self.helpers.is_valid_json_path(path) is False, f"JSON path {path} should be invalid"

    def test_parse_rate_format_valid_formats(self):
        """Test rate format parsing with valid formats"""
        test_cases = [
            ("500pm", ("500", "minute")),
            ("100ps", ("100", "second")),
            ("200ph", ("200", "hour")),
            ("10pd", ("10", "day")),
            ("1pm", ("1", "minute")),
            ("999ps", ("999", "second")),
        ]

        for rate, expected in test_cases:
            result = self.helpers.parse_rate_format(rate)
            assert result == expected, f"Rate {rate} should parse to {expected}, got {result}"

    def test_parse_rate_format_invalid_formats(self):
        """Test rate format parsing with invalid formats"""
        invalid_rates = ["100", "pm", "100px", "invalid", "", "100 pm", "100PM", None]

        for rate in invalid_rates:
            result = self.helpers.parse_rate_format(rate)
            assert result == ("500", "minute"), f"Invalid rate {rate} should return default ('500', 'minute'), got {result}"

    # Utility helper tests
    def test_safe_default_valid_value(self):
        """Test safe_default with valid value"""
        result = self.helpers.safe_default("100pm", "50pm", self.helpers.is_valid_rate)
        assert result == "100pm"
    
    def test_safe_default_invalid_value(self):
        """Test safe_default with invalid value"""
        result = self.helpers.safe_default("invalid", "50pm", self.helpers.is_valid_rate)
        assert result == "50pm"
    
    def test_safe_default_no_validator(self):
        """Test safe_default without validator"""
        result = self.helpers.safe_default("any_value", "default")
        assert result == "any_value"
    
    def test_safe_default_none_value(self):
        """Test safe_default with None value"""
        result = self.helpers.safe_default(None, "default", self.helpers.is_valid_rate)
        assert result == "default"
    
    def test_normalize_list_string_input(self):
        """Test normalize_list with string input"""
        result = self.helpers.normalize_list("a,b,c", ",")
        assert result == ["a", "b", "c"]
    
    def test_normalize_list_list_input(self):
        """Test normalize_list with list input"""
        result = self.helpers.normalize_list(["a", "b", "c"], ",")
        assert result == ["a", "b", "c"]
    
    def test_normalize_list_empty_string(self):
        """Test normalize_list with empty string"""
        result = self.helpers.normalize_list("", ",")
        assert result == [""]
    
    def test_normalize_list_different_separator(self):
        """Test normalize_list with different separator"""
        result = self.helpers.normalize_list("a;b;c", ";")
        assert result == ["a", "b", "c"]
    
    def test_xml_escape_basic(self):
        """Test XML escaping with basic characters"""
        result = self.helpers.xml_escape("Hello & <World>")
        assert result == "Hello &amp; &lt;World&gt;"
    
    def test_xml_escape_quotes(self):
        """Test XML escaping with quotes"""
        result = self.helpers.xml_escape('Say "Hello" & \'World\'')
        assert result == "Say &quot;Hello&quot; &amp; &apos;World&apos;"
    
    def test_xml_escape_empty_string(self):
        """Test XML escaping with empty string"""
        result = self.helpers.xml_escape("")
        assert result == ""
    
    def test_xml_escape_no_special_chars(self):
        """Test XML escaping with no special characters"""
        result = self.helpers.xml_escape("Hello World")
        assert result == "Hello World"
    
    # Environment helper tests
    def test_is_production_false(self):
        """Test is_production returns False for test environment"""
        assert self.helpers.is_production() is False
    
    def test_is_production_true(self):
        """Test is_production returns True for prod environment"""
        prod_helpers = TemplateHelpers(env_name="prod")
        assert prod_helpers.is_production() is True
    
    def test_is_development_true(self):
        """Test is_development returns True for dev environment"""
        dev_helpers = TemplateHelpers(env_name="dev")
        assert dev_helpers.is_development() is True
    
    def test_is_development_false(self):
        """Test is_development returns False for prod environment"""
        prod_helpers = TemplateHelpers(env_name="prod")
        assert prod_helpers.is_development() is False
    
    def test_feature_enabled_true(self):
        """Test feature_enabled returns True for enabled feature"""
        assert self.helpers.feature_enabled("test_feature") is True
    
    def test_feature_enabled_false(self):
        """Test feature_enabled returns False for disabled feature"""
        assert self.helpers.feature_enabled("nonexistent_feature") is False
    
    def test_get_env_specific_value_with_match(self):
        """Test get_env_specific_value with matching environment"""
        values = {"test": "test_value", "prod": "prod_value"}
        result = self.helpers.get_env_specific_value(values, "default")
        assert result == "test_value"
    
    def test_get_env_specific_value_without_match(self):
        """Test get_env_specific_value without matching environment"""
        values = {"prod": "prod_value", "staging": "staging_value"}
        result = self.helpers.get_env_specific_value(values, "default")
        assert result == "default"
    
    def test_get_env_specific_value_empty_values(self):
        """Test get_env_specific_value with empty values"""
        result = self.helpers.get_env_specific_value({}, "default")
        assert result == "default"
    
    # Format helper tests
    def test_format_origins_list_single_origin(self):
        """Test format_origins_list with single origin"""
        result = self.helpers.format_origins_list(["https://example.com"])
        assert result == "https://example.com"
    
    def test_format_origins_list_multiple_origins(self):
        """Test format_origins_list with multiple origins"""
        origins = ["https://example.com", "https://api.example.com"]
        result = self.helpers.format_origins_list(origins)
        assert result == "https://example.com,https://api.example.com"
    
    def test_format_origins_list_wildcard(self):
        """Test format_origins_list with wildcard"""
        result = self.helpers.format_origins_list(["*"])
        assert result == "*"
    
    def test_format_headers_list_basic(self):
        """Test format_headers_list with basic headers"""
        headers = ["Content-Type", "Authorization"]
        result = self.helpers.format_headers_list(headers)
        assert result == "Content-Type,Authorization"
    
    def test_format_headers_list_empty(self):
        """Test format_headers_list with empty list"""
        result = self.helpers.format_headers_list([])
        assert result == ""
    
    def test_format_headers_list_single_header(self):
        """Test format_headers_list with single header"""
        result = self.helpers.format_headers_list(["Content-Type"])
        assert result == "Content-Type"


class TestTemplateHelpersErrorHandling:
    """Test error handling scenarios"""
    
    def setup_method(self):
        """Set up error handling test fixtures"""
        self.helpers = TemplateHelpers()
    
    def test_is_valid_rate_none_input(self):
        """Test rate validation with None input"""
        assert self.helpers.is_valid_rate(None) is False
    
    def test_is_valid_url_none_input(self):
        """Test URL validation with None input"""
        assert self.helpers.is_valid_url(None) is False
    
    def test_is_valid_header_name_none_input(self):
        """Test header name validation with None input"""
        assert self.helpers.is_valid_header_name(None) is False
    
    def test_is_valid_json_path_none_input(self):
        """Test JSON path validation with None input"""
        assert self.helpers.is_valid_json_path(None) is False
    
    def test_safe_default_validator_exception(self):
        """Test safe_default when validator raises exception"""
        def failing_validator(value):
            raise Exception("Validator error")
        
        result = self.helpers.safe_default("test", "default", failing_validator)
        assert result == "default"
    
    def test_normalize_list_none_input(self):
        """Test normalize_list with None input"""
        result = self.helpers.normalize_list(None, ",")
        assert result == []
    
    def test_normalize_list_non_string_non_list(self):
        """Test normalize_list with non-string, non-list input"""
        result = self.helpers.normalize_list(123, ",")
        assert result == [123]
    
    def test_xml_escape_none_input(self):
        """Test XML escaping with None input"""
        result = self.helpers.xml_escape(None)
        assert result == ""
    
    def test_xml_escape_non_string_input(self):
        """Test XML escaping with non-string input"""
        result = self.helpers.xml_escape(123)
        assert result == "123"
    
    def test_get_env_specific_value_none_values(self):
        """Test get_env_specific_value with None values"""
        result = self.helpers.get_env_specific_value(None, "default")
        assert result == "default"
    
    def test_format_origins_list_none_input(self):
        """Test format_origins_list with None input"""
        result = self.helpers.format_origins_list(None)
        assert result == ""
    
    def test_format_headers_list_none_input(self):
        """Test format_headers_list with None input"""
        result = self.helpers.format_headers_list(None)
        assert result == ""


class TestTemplateHelpersIntegration:
    """Integration tests for template helpers"""
    
    def setup_method(self):
        """Set up integration test fixtures"""
        self.helpers = TemplateHelpers(
            env_name="prod",
            features={
                "advanced_security": True,
                "json_threat_protection": True,
                "cors_credentials": False
            }
        )
    
    def test_production_environment_behavior(self):
        """Test helper behavior in production environment"""
        assert self.helpers.is_production() is True
        assert self.helpers.is_development() is False
        
        # Production-specific defaults
        values = {"dev": "dev_value", "prod": "prod_value"}
        assert self.helpers.get_env_specific_value(values, "default") == "prod_value"
    
    def test_feature_flag_integration(self):
        """Test feature flag integration"""
        assert self.helpers.feature_enabled("advanced_security") is True
        assert self.helpers.feature_enabled("json_threat_protection") is True
        assert self.helpers.feature_enabled("cors_credentials") is False
        assert self.helpers.feature_enabled("nonexistent_feature") is False
    
    def test_validation_chain_integration(self):
        """Test validation helpers working together"""
        # Test a complete validation chain
        rate = "100pm"
        validated_rate = self.helpers.safe_default(rate, "50pm", self.helpers.is_valid_rate)
        assert validated_rate == "100pm"
        
        # Test with invalid input
        invalid_rate = "invalid"
        validated_rate = self.helpers.safe_default(invalid_rate, "50pm", self.helpers.is_valid_rate)
        assert validated_rate == "50pm"
    
    def test_cors_configuration_helpers(self):
        """Test CORS-specific helper integration"""
        origins = ["https://example.com", "https://api.example.com"]
        headers = ["Content-Type", "Authorization", "X-API-Key"]
        
        formatted_origins = self.helpers.format_origins_list(origins)
        formatted_headers = self.helpers.format_headers_list(headers)
        
        assert formatted_origins == "https://example.com,https://api.example.com"
        assert formatted_headers == "Content-Type,Authorization,X-API-Key"
    
    def test_xml_content_preparation(self):
        """Test XML content preparation helpers"""
        user_input = 'User said: "Hello & <World>"'
        escaped_content = self.helpers.xml_escape(user_input)
        
        assert escaped_content == "User said: &quot;Hello &amp; &lt;World&gt;&quot;"
        
        # Verify it creates valid XML
        xml_content = f"<Message>{escaped_content}</Message>"
        try:
            ET.fromstring(xml_content)
            xml_valid = True
        except ET.ParseError:
            xml_valid = False
        
        assert xml_valid is True
    
    @pytest.mark.performance
    def test_helper_performance(self):
        """Test performance of helper functions"""
        import time
        
        # Test validation performance
        start_time = time.time()
        for _ in range(1000):
            self.helpers.is_valid_rate("100pm")
            self.helpers.is_valid_url("https://api.example.com")
            self.helpers.is_valid_header_name("Content-Type")
        end_time = time.time()
        
        elapsed = end_time - start_time
        assert elapsed < 0.1, f"Helper validation too slow: {elapsed:.3f}s for 3000 validations"
        
        # Test formatting performance
        start_time = time.time()
        origins = ["https://example.com"] * 10
        headers = ["Content-Type"] * 10
        
        for _ in range(100):
            self.helpers.format_origins_list(origins)
            self.helpers.format_headers_list(headers)
        end_time = time.time()
        
        elapsed = end_time - start_time
        assert elapsed < 0.05, f"Helper formatting too slow: {elapsed:.3f}s for 200 operations"
