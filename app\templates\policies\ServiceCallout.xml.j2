<ServiceCallout name="{{ name }}"
{% if params.enabled is defined %} enabled="{{ 'true' if params.enabled else 'false' }}"{% endif %}
{% if params.continueOnError is defined %} continueOnError="{{ 'true' if params.continueOnError else 'false' }}"{% endif %}
{% if params.async is defined %} async="{{ 'true' if params.async else 'false' }}"{% endif %}>
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  {% if params.request %}
  <Request clearPayload="{{ 'true' if params.request.clear_payload else 'false' }}" variable="{{ params.request.variable | default('serviceCalloutRequest') }}">
    {% if params.request.ignore_unresolved_variables is defined %}
    <IgnoreUnresolvedVariables>{{ 'true' if params.request.ignore_unresolved_variables else 'false' }}</IgnoreUnresolvedVariables>
    {% endif %}
    {% if params.request.set %}
    <Set>
      {% if params.request.set.headers %}
      <Headers>
        {% for h in params.request.set.headers %}
        <Header name="{{ h.name }}">{{ h.value }}</Header>
        {% endfor %}
      </Headers>
      {% endif %}
      {% if params.request.set.query_params %}
      <QueryParams>
        {% for qp in params.request.set.query_params %}
        <QueryParam name="{{ qp.name }}">{{ qp.value }}</QueryParam>
        {% endfor %}
      </QueryParams>
      {% endif %}
      {% if params.request.set.form_params %}
      <FormParams>
        {% for fp in params.request.set.form_params %}
        <FormParam name="{{ fp.name }}">{{ fp.value }}</FormParam>
        {% endfor %}
      </FormParams>
      {% endif %}
      {% if params.request.set.payload is defined %}
      <Payload{% if params.request.set.content_type %} contentType="{{ params.request.set.content_type }}"{% endif %}>{{ params.request.set.payload }}</Payload>
      {% endif %}
      {% if params.request.set.verb is defined %}
      <Verb>{{ params.request.set.verb }}</Verb>
      {% endif %}
    </Set>
    {% endif %}
    {% if params.request.copy %}
    <Copy source="{{ params.request.copy.source | default('request') }}">
      {% if params.request.copy.headers %}
      <Headers>
        {% for h in params.request.copy.headers %}
        <Header name="{{ h.name }}">{{ h.from | default(h.name) }}</Header>
        {% endfor %}
      </Headers>
      {% endif %}
      {% if params.request.copy.query_params %}
      <QueryParams>
        {% for qp in params.request.copy.query_params %}
        <QueryParam name="{{ qp.name }}">{{ qp.from | default(qp.name) }}</QueryParam>
        {% endfor %}
      </QueryParams>
      {% endif %}
      {% if params.request.copy.form_params %}
      <FormParams>
        {% for fp in params.request.copy.form_params %}
        <FormParam name="{{ fp.name }}">{{ fp.from | default(fp.name) }}</FormParam>
        {% endfor %}
      </FormParams>
      {% endif %}
      {% if params.request.copy.payload is defined %}
      <Payload>{{ 'true' if params.request.copy.payload else 'false' }}</Payload>
      {% endif %}
      {% if params.request.copy.path is defined %}
      <Path>{{ 'true' if params.request.copy.path else 'false' }}</Path>
      {% endif %}
      {% if params.request.copy.verb is defined %}
      <Verb>{{ 'true' if params.request.copy.verb else 'false' }}</Verb>
      {% endif %}
    </Copy>
    {% endif %}
  </Request>
  {% endif %}
  {% if params.response %}
  <Response>{{ params.response.variable | default('serviceCalloutResponse') }}</Response>
  {% endif %}
  {% if params.timeout is defined %}
  <Timeout>{{ params.timeout }}</Timeout>
  {% endif %}
  {% if params.http_target_connection %}
  <HTTPTargetConnection>
    {% if params.http_target_connection.properties %}
    <Properties>
      {% for prop in params.http_target_connection.properties %}
      <Property name="{{ prop.name }}">{{ prop.value }}</Property>
      {% endfor %}
    </Properties>
    {% endif %}
    {% if params.http_target_connection.url %}
    <URL>{{ params.http_target_connection.url }}</URL>
    {% endif %}
    {% if params.http_target_connection.load_balancer %}
    <LoadBalancer>
      {% if params.http_target_connection.load_balancer.algorithm %}
      <Algorithm>{{ params.http_target_connection.load_balancer.algorithm }}</Algorithm>
      {% endif %}
      {% if params.http_target_connection.load_balancer.servers %}
      {% for server in params.http_target_connection.load_balancer.servers %}
      <Server name="{{ server.name }}"/>
      {% endfor %}
      {% endif %}
    </LoadBalancer>
    {% endif %}
  </HTTPTargetConnection>
  {% endif %}
  {% if params.local_target_connection %}
  <LocalTargetConnection>
    <APIProxy>{{ params.local_target_connection.api_proxy }}</APIProxy>
    <ProxyEndpoint>{{ params.local_target_connection.proxy_endpoint }}</ProxyEndpoint>
  </LocalTargetConnection>
  {% endif %}
</ServiceCallout>
