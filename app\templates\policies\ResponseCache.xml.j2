<ResponseCache name="{{ name }}">
  {% if params.cache_resource %}<CacheResource>{{ params.cache_resource }}</CacheResource>{% endif %}
  {% if params.scope %}<Scope>{{ params.scope }}</Scope>{% endif %}
  {% if params.expiry_seconds is defined %}
  <ExpirySettings>
    <TimeoutInSeconds>{{ params.expiry_seconds }}</TimeoutInSeconds>
  </ExpirySettings>
  {% endif %}
  <CacheKey>
    {% for frag in params.key_fragments %}
    <KeyFragment>{{ frag }}</KeyFragment>
    {% endfor %}
  </CacheKey>
  {% if params.included_headers %}
  <Include>
    <Headers>
      {% for h in params.included_headers %}
      <Header name="{{ h }}"/>
      {% endfor %}
    </Headers>
  </Include>
  {% endif %}
</ResponseCache>