"""
Bruno<PERSON>enerator
--------------
Service for generating Bruno API collection from Swagger/OpenAPI specifications.

Features:
- Convert Swagger/OpenAPI to Bruno collection format
- Generate .bru files for each API endpoint
- Create collection structure with folders
- Include authentication and environment variables
- Generate collection metadata

Usage:
    generator = BrunoGenerator()
    bruno_zip = generator.generate_bruno_collection(
        swagger_content=swagger_bytes,
        collection_name="My API Collection",
        base_url="https://api.example.com"
    )
"""

from __future__ import annotations

import io
import json
import yaml
import zipfile
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlparse

import structlog

from ..core.errors import ValidationError

log = structlog.get_logger(__name__)


class BrunoGenerator:
    def __init__(self) -> None:
        pass

    def generate_bruno_collection(
        self,
        swagger_content: bytes,
        collection_name: str,
        base_url: Optional[str] = None,
        environment_name: str = "default"
    ) -> bytes:
        """
        Generate a Bruno API collection ZIP file from Swagger/OpenAPI specification.
        
        Args:
            swagger_content: Swagger/OpenAPI specification as bytes
            collection_name: Name for the Bruno collection
            base_url: Base URL for the API (overrides swagger servers)
            environment_name: Name for the environment
            
        Returns:
            bytes: ZIP file containing Bruno collection
        """
        log.info("generating_bruno_collection", 
                collection_name=collection_name,
                base_url=base_url)

        # Parse swagger content
        swagger_data = self._parse_swagger_content(swagger_content)
        
        # Extract API information
        api_info = self._extract_api_info(swagger_data, base_url)
        
        # Generate Bruno files
        bruno_files = self._generate_bruno_files(swagger_data, api_info, collection_name)
        
        # Create ZIP file
        zip_content = self._create_zip_file(bruno_files, collection_name)
        
        log.info("bruno_collection_generated", 
                collection_name=collection_name,
                file_count=len(bruno_files),
                zip_size=len(zip_content))
        
        return zip_content

    def _parse_swagger_content(self, content: bytes) -> Dict[str, Any]:
        """Parse swagger content from bytes to dict."""
        try:
            text = content.decode('utf-8', errors='ignore')
            
            # Try JSON first
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                # Try YAML
                return yaml.safe_load(text)
                
        except Exception as e:
            raise ValidationError(f"Failed to parse swagger content: {str(e)}")

    def _extract_api_info(self, swagger_data: Dict[str, Any], base_url: Optional[str] = None) -> Dict[str, Any]:
        """Extract API information from swagger data."""
        info = swagger_data.get('info', {})
        
        # Determine base URL
        if base_url:
            api_base_url = base_url
        else:
            api_base_url = self._extract_base_url(swagger_data)
        
        return {
            'title': info.get('title', 'API Collection'),
            'description': info.get('description', ''),
            'version': info.get('version', '1.0.0'),
            'base_url': api_base_url,
            'host': urlparse(api_base_url).netloc if api_base_url else 'localhost',
            'schemes': ['https'] if api_base_url and api_base_url.startswith('https') else ['http']
        }

    def _extract_base_url(self, swagger_data: Dict[str, Any]) -> str:
        """Extract base URL from swagger specification."""
        # OpenAPI 3.x
        if 'openapi' in swagger_data:
            servers = swagger_data.get('servers', [])
            if servers and isinstance(servers[0], dict):
                return servers[0].get('url', 'http://localhost')
        
        # Swagger 2.0
        elif 'swagger' in swagger_data:
            host = swagger_data.get('host', 'localhost')
            schemes = swagger_data.get('schemes', ['http'])
            base_path = swagger_data.get('basePath', '')
            
            scheme = schemes[0] if schemes else 'http'
            return f"{scheme}://{host}{base_path}"
        
        return 'http://localhost'

    def _generate_bruno_files(
        self, 
        swagger_data: Dict[str, Any], 
        api_info: Dict[str, Any],
        collection_name: str
    ) -> Dict[str, str]:
        """Generate all Bruno files for the collection."""
        files = {}
        
        # Generate collection metadata
        files['bruno.json'] = self._generate_collection_metadata(api_info, collection_name)
        
        # Generate environment file
        files['environments/default.bru'] = self._generate_environment_file(api_info)
        
        # Generate request files from paths
        paths = swagger_data.get('paths', {})
        for path, path_item in paths.items():
            if not isinstance(path_item, dict):
                continue
                
            for method, operation in path_item.items():
                if method.lower() in ['get', 'post', 'put', 'delete', 'patch', 'head', 'options']:
                    if isinstance(operation, dict):
                        file_path, file_content = self._generate_request_file(
                            path, method, operation, api_info
                        )
                        files[file_path] = file_content
        
        return files

    def _generate_collection_metadata(self, api_info: Dict[str, Any], collection_name: str) -> str:
        """Generate bruno.json collection metadata file."""
        metadata = {
            "version": "1",
            "name": collection_name,
            "type": "collection",
            "ignore": [
                "node_modules",
                ".git"
            ]
        }
        
        return json.dumps(metadata, indent=2)

    def _generate_environment_file(self, api_info: Dict[str, Any]) -> str:
        """Generate environment .bru file."""
        content = f"""vars {{
  baseUrl: {api_info['base_url']}
  apiVersion: {api_info['version']}
  host: {api_info['host']}
}}

vars:secret [
  apiKey,
  authToken
]
"""
        return content

    def _generate_request_file(
        self, 
        path: str, 
        method: str, 
        operation: Dict[str, Any],
        api_info: Dict[str, Any]
    ) -> Tuple[str, str]:
        """Generate a .bru file for a specific API operation."""
        
        # Generate file name and path
        operation_id = operation.get('operationId', f"{method}_{path.replace('/', '_').replace('{', '').replace('}', '')}")
        safe_operation_id = self._sanitize_filename(operation_id)
        
        # Organize by tags if available
        tags = operation.get('tags', ['General'])
        folder = self._sanitize_filename(tags[0]) if tags else 'General'
        
        file_path = f"requests/{folder}/{safe_operation_id}.bru"
        
        # Generate file content
        summary = operation.get('summary', operation_id)
        description = operation.get('description', '')
        
        content = f"""meta {{
  name: {summary}
  type: http
  seq: 1
}}

{method.upper()} {{{{baseUrl}}}}{path}
"""

        # Add headers
        headers = self._extract_headers(operation)
        if headers:
            content += "\nheaders {\n"
            for header_name, header_info in headers.items():
                if header_info.get('required', False):
                    content += f"  {header_name}: \n"
                else:
                    content += f"  # {header_name}: {header_info.get('description', '')}\n"
            content += "}\n"

        # Add query parameters
        query_params = self._extract_query_params(operation)
        if query_params:
            content += "\nparams:query {\n"
            for param_name, param_info in query_params.items():
                if param_info.get('required', False):
                    content += f"  {param_name}: \n"
                else:
                    content += f"  # {param_name}: {param_info.get('description', '')}\n"
            content += "}\n"

        # Add request body for POST/PUT/PATCH
        if method.upper() in ['POST', 'PUT', 'PATCH']:
            body_example = self._generate_request_body_example(operation)
            if body_example:
                content += f"\nbody:json {{\n{body_example}\n}}\n"

        # Add documentation
        if description:
            content += f"\ndocs {{\n  {description}\n}}\n"

        return file_path, content

    def _extract_headers(self, operation: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract header parameters from operation."""
        headers = {}
        parameters = operation.get('parameters', [])
        
        for param in parameters:
            if isinstance(param, dict) and param.get('in') == 'header':
                headers[param.get('name', '')] = {
                    'required': param.get('required', False),
                    'description': param.get('description', ''),
                    'type': param.get('type', param.get('schema', {}).get('type', 'string'))
                }
        
        return headers

    def _extract_query_params(self, operation: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract query parameters from operation."""
        params = {}
        parameters = operation.get('parameters', [])
        
        for param in parameters:
            if isinstance(param, dict) and param.get('in') == 'query':
                params[param.get('name', '')] = {
                    'required': param.get('required', False),
                    'description': param.get('description', ''),
                    'type': param.get('type', param.get('schema', {}).get('type', 'string'))
                }
        
        return params

    def _generate_request_body_example(self, operation: Dict[str, Any]) -> Optional[str]:
        """Generate example request body for the operation."""
        request_body = operation.get('requestBody', {})
        if not request_body:
            return None
        
        content = request_body.get('content', {})
        
        # Look for JSON content type
        json_content = content.get('application/json', {})
        if json_content:
            schema = json_content.get('schema', {})
            example = json_content.get('example')
            
            if example:
                return json.dumps(example, indent=2)
            elif schema:
                return self._generate_example_from_schema(schema)
        
        return None

    def _generate_example_from_schema(self, schema: Dict[str, Any]) -> str:
        """Generate example JSON from schema."""
        # Simple example generation - can be enhanced
        if schema.get('type') == 'object':
            properties = schema.get('properties', {})
            example = {}
            for prop_name, prop_schema in properties.items():
                example[prop_name] = self._get_example_value(prop_schema)
            return json.dumps(example, indent=2)
        
        return json.dumps(self._get_example_value(schema), indent=2)

    def _get_example_value(self, schema: Dict[str, Any]) -> Any:
        """Get example value based on schema type."""
        schema_type = schema.get('type', 'string')
        
        if schema_type == 'string':
            return schema.get('example', 'string')
        elif schema_type == 'integer':
            return schema.get('example', 0)
        elif schema_type == 'number':
            return schema.get('example', 0.0)
        elif schema_type == 'boolean':
            return schema.get('example', True)
        elif schema_type == 'array':
            return []
        elif schema_type == 'object':
            return {}
        else:
            return None

    def _sanitize_filename(self, name: str) -> str:
        """Sanitize string for use as filename."""
        # Replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            name = name.replace(char, '_')
        
        # Remove extra spaces and underscores
        name = '_'.join(name.split())
        
        return name[:50]  # Limit length

    def _create_zip_file(self, files: Dict[str, str], collection_name: str) -> bytes:
        """Create ZIP file from Bruno files."""
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for file_path, content in files.items():
                # Add collection name as root folder
                full_path = f"{self._sanitize_filename(collection_name)}/{file_path}"
                zip_file.writestr(full_path, content)
        
        return zip_buffer.getvalue()
