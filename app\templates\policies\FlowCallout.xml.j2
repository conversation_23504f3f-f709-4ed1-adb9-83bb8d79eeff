<FlowCallout name="{{ name }}">
  <DisplayName>{{ params.get('display_name', name) }}</DisplayName>
  {% if params.get('shared_flow') %}
  <SharedFlowBundle>{{ params.shared_flow }}</SharedFlowBundle>
  {% endif %}
  {% if params.get('parameters') %}
  <Parameters>
    {% for p in params.parameters %}
    <Parameter name="{{ p.name }}">{{ p.value }}</Parameter>
    {% endfor %}
  </Parameters>
  {% endif %}
</FlowCallout>