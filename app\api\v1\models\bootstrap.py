from __future__ import annotations

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


# Removed LimitsModel - now handled via policyParams


class PolicyParametersModel(BaseModel):
    """
    Dynamic policy parameters using direct policy name mapping.
    Each key should be a policy name (e.g., VerifyApiKey, CORS, Quota)
    and value should be a dict of parameters for that policy.

    Example:
    {
        "VerifyApiKey": {"keyref": "request.header.x-custom-key"},
        "CORS": {
            "allow_origins": ["https://app.example.com"],
            "allow_methods": ["GET", "POST", "PUT"]
        },
        "Quota": {"allow": "2000pm", "interval": "1", "timeunit": "minute"}
    }
    """
    class Config:
        extra = "allow"  # Allow any additional policy parameters

    def __init__(self, **data):
        super().__init__(**data)
        # Store all additional fields as policy parameters
        for key, value in data.items():
            if key not in self.__fields__:
                setattr(self, key, value)


class GitOptionsModel(BaseModel):
    commitMessage: str = Field(..., description="Commit message")
    # repoUrl and branch now read from system YAML config


class BootstrapJSONRequest(BaseModel):
    """
    Enhanced JSON body variant with simplified policy parameter structure:
      - Provide swaggerB64 for the Swagger/OAS content
      - Use direct policy name mapping in policyParams
    """
    passcode: str
    systemName: str
    proxyName: str

    # Enhanced policy parameter structure
    policyParams: Optional[Dict[str, Dict[str, Any]]] = Field(
        None,
        description="Policy parameters using direct policy name mapping. Key=PolicyName, Value=Parameters dict"
    )

    # Base64-encoded Swagger/OpenAPI content
    swaggerB64: Optional[str] = Field(
        None,
        description="Base64-encoded Swagger/OpenAPI (JSON or YAML). Required if no file upload.",
    )
    git: GitOptionsModel


class PolicyRecommendation(BaseModel):
    """Recommendation for policy changes based on feature flags"""
    policy_name: str
    action: str  # "added", "removed", "modified"
    reason: str
    feature_flag: Optional[str] = None
    environment_impact: Optional[str] = None


class FeatureImpactSummary(BaseModel):
    """Summary of feature flag impacts on policy selection"""
    policies_added: list[str] = Field(default_factory=list)
    policies_removed: list[str] = Field(default_factory=list)
    policies_modified: list[str] = Field(default_factory=list)
    recommendations: list[PolicyRecommendation] = Field(default_factory=list)
    active_feature_flags: Dict[str, bool] = Field(default_factory=dict)


class BootstrapResponse(BaseModel):
    proxyName: str
    revision: int
    apigeeOrg: str
    apigeeEnv: str
    bundleSha256: str
    exportSize: int
    gitCommitId: Optional[str] = None
    gitWebUrl: Optional[str] = None

    # New: Feature-driven policy recommendations
    feature_impact: Optional[FeatureImpactSummary] = None
    policies_generated: list[str] = Field(default_factory=list)
    environment_notes: Optional[str] = None
