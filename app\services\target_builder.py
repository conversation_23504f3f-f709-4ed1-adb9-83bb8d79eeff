# app/services/target_builder.py
from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import jinja2
import structlog

from ..core.errors import ValidationError
from ..core.settings import settings
from ..config.loader import ConfigLoader  # <-- NEW

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class TargetEndpointSpec:
    name: str
    url: str
    connect_ms: int
    read_ms: int
    ssl_enabled: bool
    sni_hostname: Optional[str] = None


class TargetBuilder:
    """
    Enhanced TargetEndpoint builder for Apigee proxy target endpoint generation.

    Implements clean code architecture with single responsibility principle:
    - Target endpoint XML generation from configuration
    - Multiple target endpoints support with unique configurations
    - PreFlow and PostFlow policy integration
    - Environment-aware configuration loading with overrides

    Architecture:
    - Uses dependency injection for template engine and configuration loader
    - Implements comprehensive error handling with structured logging
    - Supports multiple target endpoints with different backend URLs
    - Integrates flow-based policy attachment for target-specific processing

    Configuration Structure (New Optimized Format):
        targets:
          default:
            name: "default"
            baseurl: "https://payments.internal.example.com"
            connect_timeout_ms: 4000
            read_timeout_ms: 30000
            ssl_profile: "SSLInfo"
            flows:
              preflow:
                request: ["AM-SetTargetStartTime", "FC-CommonTargetErrorHandler"]
                response: ["AM-SetTargetEndTime"]
              postflow:
                request: []
                response: ["FC-ExtractCustomLogs"]

          default-jo:
            name: "default-jo"
            baseurl: "https://payments-jo.internal.example.com"
            connect_timeout_ms: 5000
            read_timeout_ms: 45000
            flows:
              preflow:
                request: ["AM-SetTargetStartTime"]
                response: ["AM-SetTargetEndTime"]

    Usage:
        builder = TargetBuilder()
        xml = builder.build_target_endpoint_xml(
            system_cfg=config,
            endpoint_name="default"
        )

    Returns:
        str: Complete TargetEndpoint XML with PreFlow and PostFlow policies

    Raises:
        ValidationError: For configuration validation failures
        TemplateError: For template rendering issues
    """

    def __init__(self, templates_dir: Optional[str] = None, loader: Optional[ConfigLoader] = None) -> None:
        tdir = templates_dir or settings.templates_dir
        base = Path(tdir).joinpath("endpoints").resolve()
        if not base.exists():
            raise ValidationError(
                f"Target templates directory not found: {base}",
                details={"templates_dir": str(base)},
            )
        self._env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(base)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._env.undefined = jinja2.StrictUndefined
        self._templates_root = base

        # Optional shared loader (can be injected from orchestrator for testability)
        self._loader = loader or ConfigLoader()

    def build_target_endpoint_xml(
        self,
        system_cfg: Optional[Dict[str, Any]] = None,
        *,
        system_name: Optional[str] = None,
        endpoint_name: Optional[str] = None,
    ) -> str:
        """
        Build TargetEndpoint XML using the new optimized targets structure.

        Args:
            system_cfg: System configuration dict containing 'targets' section
            system_name: Optional system name for env-aware config loading
            endpoint_name: Target endpoint name (defaults to 'default')

        Returns:
            str: Complete TargetEndpoint XML with PreFlow and PostFlow policies

        Raises:
            ValidationError: If targets configuration is invalid or target not found

        Expected structure:
            targets:
              default:
                name: "default"
                baseurl: "https://api.example.com"
                flows:
                  preflow:
                    request: [policy1, policy2]
                    response: [policy3]
                  postflow:
                    request: []
                    response: [policy4]
        """
        if system_name:
            loaded = self._loader.load(system_name=system_name)
            effective_cfg = loaded.effective or {}
        else:
            effective_cfg = system_cfg or {}

        # Extract targets configuration - expect new structure only
        targets_cfg = effective_cfg.get("targets", {})
        if not targets_cfg:
            raise ValidationError(
                "Invalid system configuration: 'targets' section is required",
                details={"available_keys": list(effective_cfg.keys())}
            )

        # Determine which target to use
        target_name = endpoint_name or "default"
        cfg = targets_cfg.get(target_name, {})

        if not cfg:
            # Fallback to first available target if specified target not found
            if targets_cfg:
                cfg = next(iter(targets_cfg.values()), {})
                target_name = next(iter(targets_cfg.keys()), "default")
                log.warning(
                    "target_fallback",
                    requested=endpoint_name or "default",
                    using=target_name,
                    available=list(targets_cfg.keys())
                )
            else:
                raise ValidationError(
                    f"Target '{target_name}' not found in configuration",
                    details={"target": target_name, "available": list(targets_cfg.keys())}
                )

        log.info(
            "target_config_resolved",
            source="targets",
            system=system_name,
            target_name=target_name,
            available_targets=list(targets_cfg.keys()),
            env=self._loader._env_name if system_name else None,
            has_env_overrides=bool(loaded.env_path) if system_name else False,
        )

        # --- Validate and map to spec ---
        name = (target_name or cfg.get("name") or "default").strip()
        url = str(cfg.get("baseurl") or "").strip()
        if not url:
            raise ValidationError(f"target.baseurl is required for target '{name}'", details={"target": name, "config": cfg})

        parsed = urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            raise ValidationError("target.baseurl must be an absolute HTTP(S) URL", details={"url": url, "target": name})

        connect_ms = int(cfg.get("connect_timeout_ms") or 4000)
        read_ms = int(cfg.get("read_timeout_ms") or 30000)

        explicit_ssl_enabled = cfg.get("enabled")
        ssl_enabled = bool(explicit_ssl_enabled) if explicit_ssl_enabled is not None else (parsed.scheme == "https")

        sni_hostname = cfg.get("sni_hostname")

        # Extract flow information for the new template
        flows = cfg.get("flows", {})
        preflow = flows.get("preflow", {})
        postflow = flows.get("postflow", {})

        spec = TargetEndpointSpec(
            name=name,
            url=url,
            connect_ms=connect_ms,
            read_ms=read_ms,
            ssl_enabled=ssl_enabled,
            sni_hostname=sni_hostname.strip() if isinstance(sni_hostname, str) and sni_hostname.strip() else None,
        )

        # Render with flow information
        xml = self._render_with_flows("target.xml.j2", spec, preflow, postflow)
        log.info(
            "target_endpoint_built",
            name=spec.name,
            url=spec.url,
            connect_ms=spec.connect_ms,
            read_ms=spec.read_ms,
            ssl=spec.ssl_enabled,
            sni=bool(spec.sni_hostname),
            preflow_policies=len(preflow.get("request", []) + preflow.get("response", [])),
            postflow_policies=len(postflow.get("request", []) + postflow.get("response", [])),
        )
        return xml.strip()

    # -------- internal --------

    def _render_with_flows(self, template_name: str, spec: TargetEndpointSpec, preflow: Dict[str, Any], postflow: Dict[str, Any]) -> str:
        """Render target template with flow information"""
        try:
            tmpl = self._env.get_template(template_name)
        except jinja2.TemplateNotFound as e:
            raise ValidationError(
                "Target template not found",
                details={"template": template_name, "templates_root": str(self._templates_root)},
            ) from e

        try:
            xml: str = tmpl.render(
                name=spec.name,
                url=spec.url,
                timeouts={"connect_ms": spec.connect_ms, "read_ms": spec.read_ms},
                ssl={"enabled": spec.ssl_enabled, "sni_hostname": spec.sni_hostname},
                # Flow information for new template
                preflow_request=preflow.get("request", []),
                preflow_response=preflow.get("response", []),
                postflow_request=postflow.get("request", []),
                postflow_response=postflow.get("response", []),
            )
        except jinja2.UndefinedError as e:
            raise ValidationError(
                "Missing variable while rendering Target template",
                details={"template": template_name, "error": str(e)},
            ) from e

        return xml.strip()

    def _render(self, template_name: str, spec: TargetEndpointSpec) -> str:
        """Render target template without flows (for simple targets)"""
        return self._render_with_flows(template_name, spec, {}, {})