/* 
 * x-correlation.js
 * - Ensures an X-Request-Id header exists.
 * - Stores it in context variables for policies/logging.
 *
 * Apigee JS (Rhino) has access to Java types via `Packages`.
 */
/* global context, Packages */
(function () {
  try {
    var existing = context.getVariable('request.header.x-request-id');
    var id = existing && String(existing).trim();
    if (!id) {
      var UUID = Packages.java.util.UUID;
      id = String(UUID.randomUUID());
      // Set request header so downstream systems get it
      context.setVariable('request.header.x-request-id', id);
    }
    // Common context variables for logging/analytics
    context.setVariable('correlation.id', id);
    context.setVariable('request.correlation_id', id);
  } catch (e) {
    // Best-effort; never throw from callout
    context.setVariable('js.x_correlation.error', String(e));
  }
})();
