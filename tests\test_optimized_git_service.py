#!/usr/bin/env python3
"""
Test script for the optimized GitLab service.
Tests the new commit optimization logic that handles mixed create/update operations.
"""

import json
import zipfile
import io
from unittest.mock import Mock, patch
from app.services.git_service import GitService


def create_test_zip() -> bytes:
    """Create a test ZIP file with some sample files."""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        # Add some test files
        zip_file.writestr("apiproxy/test-proxy.xml", "<APIProxy>test content</APIProxy>")
        zip_file.writestr("apiproxy/policies/VerifyApiKey.xml", "<VerifyAPIKey>test</VerifyAPIKey>")
        zip_file.writestr("apiproxy/proxies/default.xml", "<ProxyEndpoint>test</ProxyEndpoint>")
        zip_file.writestr("apiproxy/targets/default.xml", "<TargetEndpoint>test</TargetEndpoint>")
    
    return zip_buffer.getvalue()


def test_optimization_with_mixed_files():
    """Test that optimization correctly handles mixed existing/new files."""
    print("🧪 Testing GitLab service optimization with mixed files...")
    
    # Mock HTTP client
    mock_http = Mock()
    
    # Mock repository tree response (some files exist)
    existing_files_response = Mock()
    existing_files_response.status_code = 200
    existing_files_response.json.return_value = [
        {"type": "blob", "path": "apiproxy/test-proxy.xml"},  # This file exists
        {"type": "blob", "path": "bundles/old-bundle.zip"},   # This file exists
        {"type": "tree", "path": "apiproxy/policies"},        # Directory (should be ignored)
    ]
    
    # Mock successful commit response
    commit_response = Mock()
    commit_response.status_code = 201
    commit_response.json.return_value = {
        "id": "abc123",
        "short_id": "abc123",
        "web_url": "https://gitlab.example.com/test/repo/-/commit/abc123"
    }
    
    # Set up mock responses
    mock_http.get.return_value = existing_files_response
    mock_http.post.return_value = commit_response
    
    # Create GitService with mocked HTTP client
    with patch('app.services.git_service.settings') as mock_settings:
        mock_settings.git.base_url = "https://gitlab.example.com/api/v4"
        mock_settings.git.pat.get_secret_value.return_value = "test-token"
        
        git_service = GitService(http=mock_http)
        
        # Create test ZIP
        test_zip = create_test_zip()
        
        # Test the push operation
        try:
            commit_id, web_url = git_service.push_zip_source_code(
                zip_bytes=test_zip,
                project_id="test%2Frepo",
                branch="main",
                commit_message="feat: bootstrap test-proxy@rev1",
                file_path="bundles/test-proxy/rev-1.zip"
            )
            
            print(f"✅ Commit successful: {commit_id}")
            print(f"   Web URL: {web_url}")
            
            # Verify that the optimization was called
            assert mock_http.get.called, "Repository files should have been fetched for optimization"
            assert mock_http.post.called, "Commit should have been posted"
            
            # Check the commit request
            commit_call = mock_http.post.call_args
            commit_body = commit_call[1]['json']
            actions = commit_body['actions']
            
            print(f"   Total actions: {len(actions)}")
            
            # Verify action types
            create_actions = [a for a in actions if a['action'] == 'create']
            update_actions = [a for a in actions if a['action'] == 'update']
            
            print(f"   Create actions: {len(create_actions)}")
            print(f"   Update actions: {len(update_actions)}")
            
            # Should have both create and update actions
            assert len(create_actions) > 0, "Should have some create actions for new files"
            assert len(update_actions) > 0, "Should have some update actions for existing files"
            
            # Verify specific files
            bundle_action = next((a for a in actions if a['file_path'] == 'bundles/test-proxy/rev-1.zip'), None)
            proxy_action = next((a for a in actions if a['file_path'] == 'apiproxy/test-proxy.xml'), None)
            
            assert bundle_action['action'] == 'create', "Bundle file should be created (new file)"
            assert proxy_action['action'] == 'update', "Proxy file should be updated (existing file)"
            
            print("✅ All optimization tests passed!")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            raise


def test_optimization_skip_large_commit():
    """Test that optimization is skipped for large commits."""
    print("🧪 Testing optimization skip for large commits...")
    
    mock_http = Mock()
    
    with patch('app.services.git_service.settings') as mock_settings:
        mock_settings.git.base_url = "https://gitlab.example.com/api/v4"
        mock_settings.git.pat.get_secret_value.return_value = "test-token"
        
        git_service = GitService(http=mock_http)
        
        # Create a large list of actions (> 1000)
        large_actions = []
        for i in range(1500):
            large_actions.append({
                "action": "create",
                "file_path": f"test/file_{i}.txt",
                "content": f"content {i}"
            })
        
        # Test the optimization method directly
        headers = {"PRIVATE-TOKEN": "test-token"}
        optimized = git_service._optimize_commit_actions("test%2Frepo", "main", large_actions, headers)
        
        # Should skip optimization and set all to "update"
        assert all(action['action'] == 'update' for action in optimized), "All actions should be 'update' for large commits"
        assert not mock_http.get.called, "Should not fetch repository files for large commits"
        
        print("✅ Large commit optimization skip test passed!")


def test_cache_functionality():
    """Test that repository files caching works correctly."""
    print("🧪 Testing repository files caching...")
    
    mock_http = Mock()
    
    # Mock repository tree response
    files_response = Mock()
    files_response.status_code = 200
    files_response.json.return_value = [
        {"type": "blob", "path": "file1.txt"},
        {"type": "blob", "path": "file2.txt"},
    ]
    
    mock_http.get.return_value = files_response
    
    with patch('app.services.git_service.settings') as mock_settings:
        mock_settings.git.base_url = "https://gitlab.example.com/api/v4"
        mock_settings.git.pat.get_secret_value.return_value = "test-token"
        
        git_service = GitService(http=mock_http)
        headers = {"PRIVATE-TOKEN": "test-token"}
        
        # First call should fetch from API
        files1 = git_service._get_repository_files("test%2Frepo", "main", headers)
        assert len(files1) == 2
        assert "file1.txt" in files1
        assert "file2.txt" in files1
        
        # Second call should use cache
        files2 = git_service._get_repository_files("test%2Frepo", "main", headers)
        assert files1 == files2
        
        # Should only have called the API once
        assert mock_http.get.call_count == 1, "Should only call API once due to caching"
        
        # Clear cache and call again
        git_service.clear_cache()
        files3 = git_service._get_repository_files("test%2Frepo", "main", headers)
        
        # Should have called API again after cache clear
        assert mock_http.get.call_count == 2, "Should call API again after cache clear"
        
        print("✅ Caching functionality test passed!")


if __name__ == "__main__":
    print("🚀 Running optimized GitLab service tests...\n")
    
    try:
        test_optimization_with_mixed_files()
        print()
        test_optimization_skip_large_commit()
        print()
        test_cache_functionality()
        print()
        print("🎉 All tests passed! The optimized GitLab service is working correctly.")
        
    except Exception as e:
        print(f"\n💥 Tests failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
