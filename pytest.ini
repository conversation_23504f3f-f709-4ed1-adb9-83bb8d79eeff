[tool:pytest]
# Pytest configuration for Apigee Proxy Factory

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    integration: Integration tests that may require external services
    slow: Tests that take longer than usual to run
    external: Tests that require external services (Apigee, GitLab, etc.)
    unit: Unit tests (default)
    api: API endpoint tests
    services: Service layer tests
    config: Configuration tests
    templates: Template tests
    performance: Performance tests

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --durations=10
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80

# Test filtering
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Parallel execution
# addopts = -n auto  # Uncomment to enable parallel execution with pytest-xdist
