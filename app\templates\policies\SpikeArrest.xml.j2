<!-- Smart Spike Arrest policy with validation and conditional logic -->
<SpikeArrest name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>

  <!-- Use resolved parameter value or apply smart defaults only if not provided -->
  {% if params.get('rate') %}
    <!-- Use the resolved parameter value (from system/env/input) -->
    {% set rate_value = params.rate %}
  {% else %}
    <!-- Apply smart defaults only when no value is configured -->
    {% if is_production() %}
      {% set rate_value = '50pm' %}
    {% else %}
      {% set rate_value = '100pm' %}
    {% endif %}
  {% endif %}

  <!-- Rate formats: 100ps, 600pm, 1000ph, 5000pd -->
  <Rate>{{ rate_value }}</Rate>

  <!-- Optional message weight for more sophisticated rate limiting -->
  {% if params.get('message_weight_ref') and params.message_weight_ref | length > 0 %}
    <MessageWeight ref="{{ params.message_weight_ref }}"/>
  {% endif %}

  <!-- Advanced feature: Custom identifier for distributed rate limiting -->
  {% if feature_enabled('advanced_traffic_management') and params.get('identifier_ref') %}
    <Identifier ref="{{ params.identifier_ref }}"/>
  {% endif %}
</SpikeArrest>