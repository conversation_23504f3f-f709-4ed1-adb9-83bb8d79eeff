# Sample Curl Commands for Bootstrap API

## 1. Using Swagger URL (No File Upload)

### Basic Swagger URL Request
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swaggerUrl=https://petstore.swagger.io/v2/swagger.json" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=petstore-api" \
  -F "gitCommitMessage=feat: bootstrap petstore-api from URL"
```

### Swagger URL with Authentication Headers
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swaggerUrl=https://api.example.com/swagger.json" \
  -F "swaggerHeaders={\"Authorization\": \"Bearer your-token\", \"X-API-Key\": \"your-api-key\"}" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  -F "gitCommitMessage=feat: bootstrap my-api from authenticated URL"
```

### Swagger URL with Apigee Proxy Configuration
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "swaggerUrl=https://api.example.com/swagger.json" \
  -F "apigeeHost=api.company.com" \
  -F "apigeeSchemes=https,http" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  -F "gitCommitMessage=feat: bootstrap my-api with proxy config"
```

### Swagger URL with Policy Parameters
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swaggerUrl=https://api.example.com/swagger.json" \
  -F "policyParams={\"VerifyApiKey\": {\"keyref\": \"request.header.x-api-key\"}, \"CORS\": {\"allow_origins\": [\"https://app.example.com\"]}}" \
  -F "policyGroup=basic_security,cors_enabled" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  -F "gitCommitMessage=feat: bootstrap my-api with policies"
```

## 2. Using File Upload (Traditional Method)

### Basic File Upload
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swagger_file=@swagger.json" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  -F "gitCommitMessage=feat: bootstrap my-api from file"
```

### File Upload with Apigee Configuration
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swagger_file=@swagger.json" \
  -F "apigeeHost=api.company.com" \
  -F "apigeeSchemes=https" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  -F "gitCommitMessage=feat: bootstrap my-api from file"
```

## 3. Testing with Public Swagger APIs

### Petstore API (Public, No Auth)
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1/development/bootstrap" \
  -F "swaggerUrl=https://petstore.swagger.io/v2/swagger.json" \
  -F "apigeeHost=api.mycompany.com" \
  -F "apigeeSchemes=https" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=petstore-proxy" \
  -F "gitCommitMessage=feat: bootstrap petstore proxy"
```

### JSONPlaceholder API (Public, No Auth)
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "swaggerUrl=https://jsonplaceholder.typicode.com/swagger.json" \
  -F "apigeeHost=api.mycompany.com" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=jsonplaceholder-proxy" \
  -F "gitCommitMessage=feat: bootstrap jsonplaceholder proxy"
```

## 4. Error Cases to Avoid

### ❌ Don't do this - Empty swagger_file parameter
```bash
# This will cause the error you encountered
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "swagger_file=" \
  -F "swaggerUrl=https://api.example.com/swagger.json" \
  ...
```

### ❌ Don't do this - Both swagger_file and swaggerUrl
```bash
# This will cause validation error
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "swagger_file=@swagger.json" \
  -F "swaggerUrl=https://api.example.com/swagger.json" \
  ...
```

### ❌ Don't do this - Neither swagger_file nor swaggerUrl
```bash
# This will cause validation error
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "passcode=your-apigee-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=my-api" \
  ...
```

## 5. Response Examples

### Successful Response
```json
{
  "proxyName": "petstore-proxy",
  "revision": 1,
  "apigeeOrg": "your-org",
  "apigeeEnv": "test",
  "bundleSha256": "abc123...",
  "exportSize": 12345,
  "gitCommitId": "commit-hash-123",
  "gitWebUrl": "https://gitlab.example.com/project/-/commit/commit-hash-123",
  "policies_generated": ["VK-VerifyApiKey", "CORS-CORS", "XTP-XMLThreatProtection"],
  "environment_notes": "Proxy created in test environment..."
}
```

### Error Response (Missing Required Parameters)
```json
{
  "code": "VALIDATION_ERROR",
  "msg": "Either swagger_file or swaggerUrl must be provided",
  "details": {...}
}
```

## 6. Testing Locally

### Start the Application
```bash
cd /path/to/apigee-proxy-factory
python -m uvicorn app.main:app --reload --port 8000
```

### Test with Petstore (Safe Public API)
```bash
curl -X POST "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap" \
  -F "swaggerUrl=https://petstore.swagger.io/v2/swagger.json" \
  -F "passcode=test-passcode" \
  -F "systemName=reflect" \
  -F "proxyName=test-petstore" \
  -F "gitCommitMessage=test: bootstrap petstore"
```

## 7. PowerShell Commands (Windows)

### Basic PowerShell Request
```powershell
$uri = "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap"
$form = @{
    swaggerUrl = "https://petstore.swagger.io/v2/swagger.json"
    passcode = "your-apigee-passcode"
    systemName = "reflect"
    proxyName = "petstore-proxy"
    gitCommitMessage = "feat: bootstrap petstore proxy"
}

Invoke-RestMethod -Uri $uri -Method Post -Form $form
```

### PowerShell with Authentication Headers
```powershell
$uri = "http://localhost:8000/arabi-assist/api-automation/v1development/bootstrap"
$swaggerHeaders = @{
    Authorization = "Bearer your-token"
    "X-API-Key" = "your-api-key"
} | ConvertTo-Json

$form = @{
    swaggerUrl = "https://api.example.com/swagger.json"
    swaggerHeaders = $swaggerHeaders
    apigeeHost = "api.company.com"
    passcode = "your-apigee-passcode"
    systemName = "reflect"
    proxyName = "my-api"
    gitCommitMessage = "feat: bootstrap my-api"
}

Invoke-RestMethod -Uri $uri -Method Post -Form $form
```

## Notes

1. **Parameter Exclusion**: When using `swaggerUrl`, do NOT include `swagger_file` parameter at all
2. **Authentication**: Use `swaggerHeaders` for authenticated swagger endpoints
3. **Validation**: The API validates that exactly one of `swagger_file` or `swaggerUrl` is provided
4. **Testing**: Use public APIs like Petstore for initial testing
5. **Environment**: Make sure you're running in a test/dev environment (not production)
