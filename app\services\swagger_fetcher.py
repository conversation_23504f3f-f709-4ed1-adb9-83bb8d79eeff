"""
SwaggerFetcher
--------------
Service for fetching Swagger/OpenAPI specifications from URLs and processing them.

Features:
- Fetch swagger content from URLs with retry logic
- Validate and parse JSON/YAML content
- Convert to standardized JSON format
- Handle authentication headers if needed

Usage:
    fetcher = SwaggerFetcher()
    swagger_content = fetcher.fetch_swagger_from_url("https://api.example.com/swagger.json")
"""

from __future__ import annotations

import json
import yaml
from typing import Any, Dict, Optional
from urllib.parse import urlparse

import httpx
import structlog

from ..core.errors import ValidationError, UpstreamError
from ..adapters.http import request_with_retries, new_client

log = structlog.get_logger(__name__)


class SwaggerFetcher:
    def __init__(
        self,
        *,
        http: Optional[httpx.Client] = None,
        timeout_s: Optional[int] = None,
    ) -> None:
        self._http = http or new_client(timeout_s=timeout_s or 30)

    def fetch_swagger_from_url(
        self, 
        url: str, 
        headers: Optional[Dict[str, str]] = None
    ) -> bytes:
        """
        Fetch Swagger/OpenAPI specification from a URL.
        
        Args:
            url: The URL to fetch the swagger specification from
            headers: Optional headers to include in the request (e.g., for authentication)
            
        Returns:
            bytes: The swagger content as bytes
            
        Raises:
            ValidationError: If URL is invalid or content is not valid swagger
            UpstreamError: If request fails or returns non-200 status
        """
        # Validate URL
        if not url or not url.strip():
            raise ValidationError("Swagger URL cannot be empty")
            
        try:
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValidationError(f"Invalid URL format: {url}")
        except Exception as e:
            raise ValidationError(f"Invalid URL: {url}") from e

        # Prepare headers
        request_headers = {
            "Accept": "application/json, application/yaml, text/yaml, text/plain, */*",
            "User-Agent": "Apigee-Proxy-Factory/1.0"
        }
        if headers:
            request_headers.update(headers)

        log.info("fetching_swagger_from_url", url=url)

        try:
            # Use retry logic for robust fetching
            response = request_with_retries(
                self._http,
                "GET",
                url,
                headers=request_headers,
                retry_total=3,
                backoff_factor=1.0
            )

            if response.status_code != 200:
                raise UpstreamError(
                    f"Failed to fetch swagger from URL: HTTP {response.status_code}",
                    details={
                        "url": url,
                        "status_code": response.status_code,
                        "response_body": response.text[:500] if response.text else None
                    }
                )

            content = response.content
            if not content:
                raise ValidationError(f"Empty response from swagger URL: {url}")

            # Validate that the content is valid swagger/openapi
            self._validate_swagger_content(content, url)

            log.info("swagger_fetched_successfully", 
                    url=url, 
                    content_length=len(content),
                    content_type=response.headers.get("content-type"))

            return content

        except httpx.RequestError as e:
            raise UpstreamError(
                f"Network error fetching swagger from URL: {url}",
                details={"url": url, "error": str(e)}
            ) from e
        except (ValidationError, UpstreamError):
            raise
        except Exception as e:
            raise UpstreamError(
                f"Unexpected error fetching swagger from URL: {url}",
                details={"url": url, "error": str(e)}
            ) from e

    def _validate_swagger_content(self, content: bytes, url: str) -> None:
        """
        Validate that the content is a valid Swagger/OpenAPI specification.
        
        Args:
            content: The content to validate
            url: The source URL (for error reporting)
            
        Raises:
            ValidationError: If content is not valid swagger/openapi
        """
        try:
            text = content.decode("utf-8", errors="ignore")
            
            # Try to parse as JSON first
            try:
                data = json.loads(text)
            except json.JSONDecodeError:
                # Try to parse as YAML
                try:
                    data = yaml.safe_load(text)
                except yaml.YAMLError as e:
                    raise ValidationError(
                        f"Content from {url} is not valid JSON or YAML: {str(e)}"
                    )

            if not isinstance(data, dict):
                raise ValidationError(
                    f"Content from {url} is not a valid object (got {type(data).__name__})"
                )

            # Check for swagger/openapi indicators
            has_swagger = "swagger" in data
            has_openapi = "openapi" in data
            has_paths = "paths" in data

            if not (has_swagger or has_openapi):
                raise ValidationError(
                    f"Content from {url} does not appear to be a Swagger/OpenAPI specification "
                    f"(missing 'swagger' or 'openapi' field)"
                )

            if not has_paths:
                log.warning("swagger_missing_paths", 
                           url=url, 
                           msg="Swagger specification has no 'paths' field")

            # Log swagger version info
            if has_swagger:
                version = data.get("swagger", "unknown")
                log.info("swagger_version_detected", url=url, swagger_version=version)
            elif has_openapi:
                version = data.get("openapi", "unknown")
                log.info("openapi_version_detected", url=url, openapi_version=version)

        except UnicodeDecodeError as e:
            raise ValidationError(
                f"Content from {url} is not valid UTF-8 text: {str(e)}"
            )

    def close(self) -> None:
        """Close the HTTP client."""
        try:
            self._http.close()
        except Exception:
            pass


def fetch_swagger_content(
    url_or_content: str | bytes, 
    headers: Optional[Dict[str, str]] = None
) -> bytes:
    """
    Utility function to fetch swagger content from URL or return content if it's already bytes.
    
    Args:
        url_or_content: Either a URL string or swagger content as bytes
        headers: Optional headers for URL requests
        
    Returns:
        bytes: The swagger content
    """
    if isinstance(url_or_content, bytes):
        return url_or_content
    
    if isinstance(url_or_content, str):
        # Check if it looks like a URL
        if url_or_content.startswith(('http://', 'https://')):
            fetcher = SwaggerFetcher()
            try:
                return fetcher.fetch_swagger_from_url(url_or_content, headers)
            finally:
                fetcher.close()
        else:
            # Assume it's swagger content as string
            return url_or_content.encode('utf-8')
    
    raise ValidationError(f"Invalid swagger input type: {type(url_or_content)}")
