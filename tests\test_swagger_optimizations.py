#!/usr/bin/env python3
"""
Test script for the new swagger processing optimizations.
Tests swagger URL fetching, proxy swagger generation, and Bruno collection creation.
"""

import json
import zipfile
import io
from unittest.mock import Mock, patch

def test_swagger_fetcher():
    """Test SwaggerFetcher functionality."""
    print("🧪 Testing SwaggerFetcher...")
    
    from app.services.swagger_fetcher import SwaggerFetcher
    
    # Mock HTTP response
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.content = json.dumps({
        "swagger": "2.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "paths": {"/test": {"get": {"summary": "Test endpoint"}}}
    }).encode('utf-8')
    mock_response.headers = {"content-type": "application/json"}
    
    mock_http = Mock()
    mock_http.request.return_value = mock_response
    
    with patch('app.adapters.http.request_with_retries', return_value=mock_response):
        fetcher = SwaggerFetcher(http=mock_http)
        
        try:
            content = fetcher.fetch_swagger_from_url(
                "https://api.example.com/swagger.json",
                headers={"Authorization": "Bearer token"}
            )
            
            print("✅ SwaggerFetcher: Successfully fetched swagger from URL")
            print(f"   Content length: {len(content)} bytes")
            
            # Verify content is valid JSON
            parsed = json.loads(content.decode('utf-8'))
            assert parsed["swagger"] == "2.0"
            assert parsed["info"]["title"] == "Test API"
            
            print("✅ SwaggerFetcher: Content validation passed")
            
        except Exception as e:
            print(f"❌ SwaggerFetcher test failed: {e}")
            raise
        finally:
            fetcher.close()


def test_swagger_processor():
    """Test SwaggerProcessor functionality."""
    print("\n🧪 Testing SwaggerProcessor...")
    
    from app.services.swagger_processor import SwaggerProcessor
    
    # Create test swagger content
    target_swagger = {
        "swagger": "2.0",
        "info": {
            "title": "Target Service API",
            "version": "1.0.0",
            "description": "Original target service"
        },
        "host": "backend.example.com",
        "basePath": "/api/v1",
        "schemes": ["https"],
        "paths": {
            "/users": {
                "get": {
                    "summary": "Get users",
                    "parameters": [
                        {"name": "limit", "in": "query", "type": "integer"}
                    ]
                }
            }
        }
    }
    
    target_content = json.dumps(target_swagger).encode('utf-8')
    
    processor = SwaggerProcessor()
    
    try:
        proxy_content = processor.create_proxy_swagger(
            target_swagger_content=target_content,
            apigee_base_path="/v1/my-api",
            apigee_host="api.company.com",
            apigee_schemes=["https"],
            additional_headers=["x-api-key", "x-correlation-id"]
        )
        
        print("✅ SwaggerProcessor: Successfully created proxy swagger")
        
        # Verify proxy swagger
        proxy_swagger = json.loads(proxy_content.decode('utf-8'))
        
        assert proxy_swagger["host"] == "api.company.com"
        assert proxy_swagger["basePath"] == "/v1/my-api"
        assert "Proxy" in proxy_swagger["info"]["title"]
        assert proxy_swagger["info"]["x-apigee-proxy"] == True
        
        # Check that additional headers were added
        users_get = proxy_swagger["paths"]["/users"]["get"]
        header_names = [p["name"] for p in users_get["parameters"] if p.get("in") == "header"]
        assert "x-api-key" in header_names
        assert "x-correlation-id" in header_names
        
        print("✅ SwaggerProcessor: Proxy swagger validation passed")
        print(f"   Original host: {target_swagger['host']} -> Proxy host: {proxy_swagger['host']}")
        print(f"   Original basePath: {target_swagger['basePath']} -> Proxy basePath: {proxy_swagger['basePath']}")
        print(f"   Added headers: {header_names}")
        
    except Exception as e:
        print(f"❌ SwaggerProcessor test failed: {e}")
        raise


def test_bruno_generator():
    """Test BrunoGenerator functionality."""
    print("\n🧪 Testing BrunoGenerator...")
    
    from app.services.bruno_generator import BrunoGenerator
    
    # Create test swagger content
    swagger_content = json.dumps({
        "swagger": "2.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0"
        },
        "host": "api.example.com",
        "basePath": "/v1",
        "schemes": ["https"],
        "paths": {
            "/users": {
                "get": {
                    "summary": "Get users",
                    "operationId": "getUsers",
                    "tags": ["Users"],
                    "parameters": [
                        {"name": "limit", "in": "query", "type": "integer", "required": False},
                        {"name": "x-api-key", "in": "header", "type": "string", "required": True}
                    ]
                },
                "post": {
                    "summary": "Create user",
                    "operationId": "createUser",
                    "tags": ["Users"],
                    "parameters": [
                        {"name": "x-api-key", "in": "header", "type": "string", "required": True}
                    ],
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "email": {"type": "string"}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }).encode('utf-8')
    
    generator = BrunoGenerator()
    
    try:
        bruno_zip = generator.generate_bruno_collection(
            swagger_content=swagger_content,
            collection_name="Test API Collection",
            base_url="https://api.example.com/v1"
        )
        
        print("✅ BrunoGenerator: Successfully generated Bruno collection")
        print(f"   ZIP size: {len(bruno_zip)} bytes")
        
        # Verify ZIP contents
        with zipfile.ZipFile(io.BytesIO(bruno_zip), 'r') as zip_file:
            file_list = zip_file.namelist()
            
            # Check for expected files
            assert any("bruno.json" in f for f in file_list), "Missing bruno.json"
            assert any("environments/default.bru" in f for f in file_list), "Missing environment file"
            assert any("requests/Users/<USER>" in f for f in file_list), "Missing GET request file"
            assert any("requests/Users/<USER>" in f for f in file_list), "Missing POST request file"
            
            print(f"✅ BrunoGenerator: ZIP validation passed")
            print(f"   Files in collection: {len(file_list)}")
            print(f"   Sample files: {file_list[:5]}")
            
            # Check content of a request file
            get_users_file = next(f for f in file_list if "getUsers.bru" in f)
            get_users_content = zip_file.read(get_users_file).decode('utf-8')
            
            assert "GET {{baseUrl}}/users" in get_users_content
            assert "x-api-key:" in get_users_content
            assert "limit:" in get_users_content
            
            print("✅ BrunoGenerator: Request file content validation passed")
        
    except Exception as e:
        print(f"❌ BrunoGenerator test failed: {e}")
        raise


def test_orchestrator_integration():
    """Test Orchestrator integration with new services."""
    print("\n🧪 Testing Orchestrator integration...")
    
    from app.services.orchestrator import Orchestrator, BootstrapRequest, GitOptions
    
    # Mock all dependencies
    mock_auth = Mock()
    mock_auth.exchange_passcode.return_value = Mock(token_type="Bearer", token="test-token")
    
    mock_apigee = Mock()
    mock_apigee.import_proxy_zip.return_value = Mock(revision=1)
    
    mock_policy_builder = Mock()
    mock_policy_builder.render_policies.return_value = ([], Mock())
    
    mock_flow_builder = Mock()
    mock_flow_builder.build_proxy_endpoint_xml.return_value = "<ProxyEndpoint>test</ProxyEndpoint>"
    
    mock_target_builder = Mock()
    mock_target_builder.build_target_endpoint_xml.return_value = "<TargetEndpoint>test</TargetEndpoint>"
    
    mock_resource_uploader = Mock()
    mock_exporter = Mock()
    mock_exporter.export.return_value = Mock(data=b"test-zip", sha256="test-hash", size=1000)
    
    mock_git_service = Mock()
    mock_git_service.create_project.return_value = {"project_id": "test-project"}
    mock_git_service.push_zip_source_code.return_value = ("commit-123", "https://git.example.com/commit/123")
    
    mock_swagger_fetcher = Mock()
    mock_swagger_fetcher.fetch_swagger_from_url.return_value = json.dumps({
        "swagger": "2.0",
        "info": {"title": "Test API", "version": "1.0.0"},
        "host": "api.example.com",
        "basePath": "/v1",
        "paths": {"/test": {"get": {"summary": "Test"}}}
    }).encode('utf-8')

    mock_swagger_processor = Mock()
    mock_swagger_processor.create_proxy_swagger.return_value = b'{"swagger": "2.0", "info": {"title": "Proxy API"}}'

    mock_bruno_generator = Mock()
    mock_bruno_generator.generate_bruno_collection.return_value = b"bruno-collection-zip"
    
    # Create orchestrator with mocked dependencies
    orchestrator = Orchestrator(
        auth=mock_auth,
        apigee=mock_apigee,
        policy_builder=mock_policy_builder,
        flow_builder=mock_flow_builder,
        target_builder=mock_target_builder,
        resource_uploader=mock_resource_uploader,
        exporter=mock_exporter,
        git_service=mock_git_service,
        swagger_fetcher=mock_swagger_fetcher,
        swagger_processor=mock_swagger_processor,
        bruno_generator=mock_bruno_generator
    )
    
    # Test swagger URL request
    request = BootstrapRequest(
        passcode="test-passcode",
        system_name="reflect",
        proxy_name="test-proxy",
        swagger_content=b"",  # Empty since we're using URL
        swagger_url="https://api.example.com/swagger.json",
        swagger_headers={"Authorization": "Bearer token"},
        apigee_host="api.company.com",
        apigee_schemes=["https"],
        git=GitOptions(commit_message="test commit")
    )
    
    with patch('app.services.orchestrator._load_system_yaml') as mock_load_system:
        mock_load_system.return_value = {
            "git": {"bundles_repo": "test-namespace", "default_branch": "main"}
        }
        
        try:
            result = orchestrator.bootstrap(request)
            
            print("✅ Orchestrator: Bootstrap completed successfully")
            print(f"   Proxy: {result.proxy_name}")
            print(f"   Revision: {result.revision}")
            print(f"   Git commit: {result.git_commit_id}")
            print(f"   Git URL: {result.git_web_url}")
            
            # Verify that new services were called
            mock_swagger_fetcher.fetch_swagger_from_url.assert_called_once()
            mock_swagger_processor.create_proxy_swagger.assert_called_once()
            mock_bruno_generator.generate_bruno_collection.assert_called_once()
            
            # Verify resource uploads
            assert mock_resource_uploader.upload_swagger.called
            assert mock_resource_uploader.upload_target_swagger.called
            
            print("✅ Orchestrator: All new services were properly integrated")
            
        except Exception as e:
            print(f"❌ Orchestrator integration test failed: {e}")
            raise


if __name__ == "__main__":
    print("🚀 Running swagger optimization tests...\n")
    
    try:
        test_swagger_fetcher()
        test_swagger_processor()
        test_bruno_generator()
        test_orchestrator_integration()
        
        print("\n🎉 All swagger optimization tests passed!")
        print("\n📋 Summary of new features:")
        print("   ✅ Swagger URL fetching with authentication")
        print("   ✅ Proxy swagger generation with Apigee-specific modifications")
        print("   ✅ Bruno API collection generation")
        print("   ✅ Combined Git repository with both bundle and collection")
        print("   ✅ Enhanced orchestrator integration")
        
    except Exception as e:
        print(f"\n💥 Tests failed: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
