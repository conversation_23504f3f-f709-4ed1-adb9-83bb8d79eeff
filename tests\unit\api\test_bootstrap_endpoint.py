"""
Unit tests for Bootstrap API endpoint
====================================
Comprehensive test coverage for the bootstrap endpoint including
form data handling, validation, and orchestration integration.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from fastapi import UploadFile
import json
import io

from app.main import create_app
from app.core.errors import ValidationError, AuthError, UpstreamError


class TestBootstrapEndpoint:
    """Test suite for Bootstrap API endpoint"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.app = create_app()
        self.client = TestClient(self.app)
        
        # Sample valid form data
        self.valid_form_data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "policyParams": json.dumps({
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "100pm"}
            }),
            "gitCommitMessage": "Test commit message"
        }
        
        # Sample swagger content
        self.swagger_content = json.dumps({
            "openapi": "3.0.0",
            "info": {"title": "Test API", "version": "1.0.0"},
            "paths": {
                "/test": {
                    "get": {
                        "summary": "Test endpoint",
                        "responses": {"200": {"description": "Success"}}
                    }
                }
            }
        })
        
        # Mock orchestrator result
        self.mock_orchestrator_result = Mock()
        self.mock_orchestrator_result.proxy_name = "test-proxy"
        self.mock_orchestrator_result.revision = 1
        self.mock_orchestrator_result.org = "test-org"
        self.mock_orchestrator_result.env = "test-env"
        self.mock_orchestrator_result.bundle_sha256 = "abc123"
        self.mock_orchestrator_result.export_size = 1024
        self.mock_orchestrator_result.git_commit_id = "commit123"
        self.mock_orchestrator_result.git_web_url = "https://git.example.com/commit/123"
        self.mock_orchestrator_result.feature_impact = None
        self.mock_orchestrator_result.policies_generated = ["VerifyApiKey", "SpikeArrest"]
        self.mock_orchestrator_result.environment_notes = "Test environment"
    
    def test_bootstrap_endpoint_exists(self):
        """Test that bootstrap endpoint exists and accepts POST"""
        # Test with minimal data to check endpoint existence
        files = {"swagger_file": ("test.json", io.BytesIO(b"{}"), "application/json")}
        data = {"passcode": "test", "systemName": "test", "proxyName": "test", "gitCommitMessage": "test commit"}
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        # Should not return 404 (endpoint exists)
        assert response.status_code != 404
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_success_minimal(self, mock_orchestrator_class):
        """Test successful bootstrap with minimal required parameters"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.return_value = self.mock_orchestrator_result
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test minimal commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 200
        
        response_data = response.json()
        assert response_data["proxyName"] == "test-proxy"
        assert response_data["revision"] == 1
        assert response_data["apigeeOrg"] == "test-org"
        assert response_data["apigeeEnv"] == "test-env"
        
        # Verify orchestrator was called
        mock_orchestrator.bootstrap.assert_called_once()
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_success_full_parameters(self, mock_orchestrator_class):
        """Test successful bootstrap with all parameters"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.return_value = self.mock_orchestrator_result
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=self.valid_form_data)
        
        assert response.status_code == 200
        
        # Verify orchestrator was called with correct parameters
        call_args = mock_orchestrator.bootstrap.call_args[0][0]
        assert call_args.passcode == "test-passcode"
        assert call_args.system_name == "test-system"
        assert call_args.proxy_name == "test-proxy"
        assert call_args.policy_params["VerifyApiKey"]["keyref"] == "request.header.x-api-key"
        assert call_args.features["advanced_security"] is True
        assert call_args.git.commit_message == "Test commit message"
    
    def test_bootstrap_missing_required_file(self):
        """Test bootstrap with missing swagger file"""
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", data=data)
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_bootstrap_missing_required_fields(self):
        """Test bootstrap with missing required form fields"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            # Missing systemName and proxyName
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 422  # Unprocessable Entity
    
    def test_bootstrap_empty_swagger_file(self):
        """Test bootstrap with empty swagger file"""
        files = {
            "swagger_file": ("empty.json", io.BytesIO(b""), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 400  # Bad Request
        response_data = response.json()
        assert "empty" in response_data["detail"].lower()
    
    def test_bootstrap_invalid_json_parameters(self):
        """Test bootstrap with invalid JSON in parameters"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "policyParams": "invalid json",  # Invalid JSON
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 400  # Bad Request
        response_data = response.json()
        assert "json" in response_data["detail"].lower()
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_validation_error(self, mock_orchestrator_class):
        """Test bootstrap with validation error from orchestrator"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.side_effect = ValidationError("Invalid system configuration")
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "invalid-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 400  # Bad Request
        response_data = response.json()
        assert "Invalid system configuration" in response_data["detail"]
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_auth_error(self, mock_orchestrator_class):
        """Test bootstrap with authentication error"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.side_effect = AuthError("Invalid passcode")
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "invalid-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 401  # Unauthorized
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_upstream_error(self, mock_orchestrator_class):
        """Test bootstrap with upstream service error"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.side_effect = UpstreamError("Apigee service unavailable")
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 502  # Bad Gateway
    
    @patch('app.api.v1.endpoints.bootstrap.Orchestrator')
    def test_bootstrap_unexpected_error(self, mock_orchestrator_class):
        """Test bootstrap with unexpected error"""
        mock_orchestrator = Mock()
        mock_orchestrator.bootstrap.side_effect = Exception("Unexpected error")
        mock_orchestrator_class.return_value = mock_orchestrator
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Test commit"
        }
        
        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
        
        assert response.status_code == 500  # Internal Server Error
    
    def test_bootstrap_policy_params_parsing(self):
        """Test parsing of policy parameters"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }

        policy_params = {
            "VerifyApiKey": {"keyref": "request.header.custom-key"},
            "CORS": {
                "allow_origins": ["https://example.com"],
                "allow_methods": ["GET", "POST"]
            }
        }

        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "policyParams": json.dumps(policy_params),
            "gitCommitMessage": "Test commit"
        }

    def test_bootstrap_multiple_policy_groups(self):
        """Test parsing of multiple comma-separated policy groups"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }

        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "policyGroup": "basic_security,cors_enabled,high_security",
            "gitCommitMessage": "Test commit with multiple policy groups"
        }

        # This test would need proper mocking of the policy catalog
        # For now, it demonstrates the expected input format
        
        with patch('app.api.v1.endpoints.bootstrap.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.bootstrap.return_value = self.mock_orchestrator_result
            mock_orchestrator_class.return_value = mock_orchestrator
            
            response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
            
            assert response.status_code == 200
            
            # Verify policy params were parsed correctly
            call_args = mock_orchestrator.bootstrap.call_args[0][0]
            assert call_args.policy_params["VerifyApiKey"]["keyref"] == "request.header.custom-key"
            assert call_args.policy_params["CORS"]["allow_origins"] == ["https://example.com"]
    


    
    def test_bootstrap_features_removed(self):
        """Test that features parameter is no longer accepted"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }

        # Try to pass features - should be ignored since it's no longer a valid parameter
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "features": json.dumps({"advanced_security": True}),  # This should be ignored
            "gitCommitMessage": "Test commit"
        }

        with patch('app.api.v1.endpoints.bootstrap.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.bootstrap.return_value = self.mock_orchestrator_result
            mock_orchestrator_class.return_value = mock_orchestrator

            response = self.client.post("/api/v1/bootstrap/", files=files, data=data)

            assert response.status_code == 200

            # Verify features are not passed to orchestrator (should be None)
            call_args = mock_orchestrator.bootstrap.call_args[0][0]
            assert call_args.features is None  # Features should not be passed from API
    
    def test_bootstrap_git_options_parsing(self):
        """Test parsing of git options"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }
        
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Custom commit message"
        }
        
        with patch('app.api.v1.endpoints.bootstrap.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_orchestrator.bootstrap.return_value = self.mock_orchestrator_result
            mock_orchestrator_class.return_value = mock_orchestrator
            
            response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
            
            assert response.status_code == 200
            
            # Verify git options were parsed correctly
            call_args = mock_orchestrator.bootstrap.call_args[0][0]
            assert call_args.git is not None
            assert call_args.git.commit_message == "Custom commit message"
    
    def test_bootstrap_missing_git_commit_message(self):
        """Test bootstrap without mandatory gitCommitMessage"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(self.swagger_content.encode()), "application/json")
        }

        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy"
            # No gitCommitMessage - should fail validation
        }

        response = self.client.post("/api/v1/bootstrap/", files=files, data=data)

        # Should return 422 (Unprocessable Entity) for missing required field
        assert response.status_code == 422
        response_data = response.json()
        assert "gitCommitMessage" in str(response_data)


class TestBootstrapEndpointIntegration:
    """Integration tests for bootstrap endpoint"""
    
    def setup_method(self):
        """Set up integration test fixtures"""
        self.app = create_app()
        self.client = TestClient(self.app)
    
    @pytest.mark.integration
    def test_bootstrap_endpoint_with_real_orchestrator(self):
        """Test bootstrap endpoint with real orchestrator (mocked external services)"""
        files = {
            "swagger_file": ("test.json", io.BytesIO(b'{"openapi": "3.0.0", "info": {"title": "Test", "version": "1.0.0"}, "paths": {}}'), "application/json")
        }
        
        data = {
            "passcode": "test-passcode",
            "systemName": "reflect",  # Use existing system config
            "proxyName": "test-integration-proxy",
            "gitCommitMessage": "Integration test commit"
        }
        
        # Mock external services but use real orchestrator
        with patch('app.services.auth_service.AuthService.exchange_passcode') as mock_auth:
            with patch('app.services.apigee_client.ApigeeClient') as mock_client:
                mock_auth.return_value = Mock(token="test-token", token_type="Bearer")
                
                mock_client_instance = Mock()
                mock_client_instance.import_proxy_zip.return_value = {"revision": 1}
                mock_client_instance.export_revision_zip.return_value = b"test-zip-content"
                mock_client.return_value = mock_client_instance
                
                response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
                
                # Should not crash and should return some response
                assert response.status_code in [200, 400, 401, 500]  # Various possible outcomes
    
    @pytest.mark.integration
    def test_bootstrap_endpoint_performance(self):
        """Test bootstrap endpoint performance"""
        import time
        
        files = {
            "swagger_file": ("test.json", io.BytesIO(b'{"openapi": "3.0.0"}'), "application/json")
        }
        
        data = {
            "passcode": "test-passcode",
            "systemName": "test-system",
            "proxyName": "test-proxy",
            "gitCommitMessage": "Performance test commit"
        }
        
        with patch('app.api.v1.endpoints.bootstrap.Orchestrator') as mock_orchestrator_class:
            mock_orchestrator = Mock()
            mock_result = Mock()
            mock_result.proxy_name = "test"
            mock_result.revision = 1
            mock_result.org = "test"
            mock_result.env = "test"
            mock_result.bundle_sha256 = "abc"
            mock_result.export_size = 1024
            mock_result.git_commit_id = None
            mock_result.git_web_url = None
            mock_result.feature_impact = None
            mock_result.policies_generated = []
            mock_result.environment_notes = None
            mock_orchestrator.bootstrap.return_value = mock_result
            mock_orchestrator_class.return_value = mock_orchestrator
            
            start_time = time.time()
            response = self.client.post("/api/v1/bootstrap/", files=files, data=data)
            end_time = time.time()
            
            elapsed = end_time - start_time
            
            # Should complete in reasonable time (< 1 second for mocked services)
            assert elapsed < 1.0, f"Bootstrap endpoint too slow: {elapsed:.3f}s"
            assert response.status_code == 200
