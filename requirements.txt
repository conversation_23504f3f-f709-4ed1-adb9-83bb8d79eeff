# Core Framework Dependencies
fastapi>=0.111.0
uvicorn[standard]>=0.30.0
eval-type-backport

# Pydantic v2 stack + settings
pydantic>=2.8.0
pydantic-settings>=2.3.0

# HTTP client + templating + XML + YAML processing
httpx>=0.27.0
jinja2>=3.1.0
lxml>=5.2.0
PyYAML>=6.0.0

# File uploads + logging + faster JSON
python-multipart>=0.0.9
structlog>=24.1.0
orjson>=3.10.0

# Phase 1-4 Enhancement Dependencies
# Template processing and validation
markupsafe>=2.1.0
xmlschema>=3.0.0

# Configuration management and validation
jsonschema>=4.20.0
cerberus>=1.3.5

# Feature flag and conditional logic
python-dotenv>=1.0.0
typing-extensions>=4.8.0

# Advanced data processing
pathlib2>=2.3.7; python_version < "3.4"
dataclasses>=0.8; python_version < "3.7"

# Core testing (minimal for basic functionality)
pytest>=7.4.0

# Essential Phase 1-4 Dependencies
# Configuration validation and processing
jsonschema>=4.20.0
cerberus>=1.3.5

# Template processing enhancements
markupsafe>=2.1.0

# Environment and feature management
python-dotenv>=1.0.0
cachetools>=5.3.0

# Enhanced data processing
typing-extensions>=4.8.0

# Security (core only)
cryptography>=41.0.0

# Async utilities
aiofiles>=23.2.0
