<XMLToJSON name="{{ name }}">
  {% if params.input %}<InputVariable>{{ params.input }}</InputVariable>{% endif %}
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.options %}
  <Options>
    {% if params.options.recognize_number %}<RecognizeNumber>{{ 'true' if params.options.recognize_number else 'false' }}</RecognizeNumber>{% endif %}
    {% if params.options.escape %}<Escape>{{ 'true' if params.options.escape else 'false' }}</Escape>{% endif %}
    {% if params.options.text_node_name %}<TextNodeName>{{ params.options.text_node_name }}</TextNodeName>{% endif %}
  </Options>
  {% endif %}
</XMLToJSON>