"""
Configuration Validation Tools
=============================
Comprehensive validation tools for system configurations, policy parameters,
and template validation to ensure configuration correctness and prevent runtime errors.
"""

import re
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import structlog
import yaml

from ..core.errors import ValidationError
from .template_helpers import TemplateHelpers

log = structlog.get_logger(__name__)


class ValidationResult:
    """Represents the result of a validation operation"""
    
    def __init__(self, is_valid: bool = True, errors: Optional[List[str]] = None, 
                 warnings: Optional[List[str]] = None, context: Optional[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
        self.context = context
    
    def add_error(self, error: str):
        """Add an error to the result"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add a warning to the result"""
        self.warnings.append(warning)
    
    def merge(self, other: 'ValidationResult'):
        """Merge another validation result into this one"""
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        if not other.is_valid:
            self.is_valid = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "is_valid": self.is_valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "context": self.context
        }


class SystemConfigValidator:
    """Validates system configuration files"""
    
    def __init__(self):
        self.helpers = TemplateHelpers()
    
    def validate_system_config(self, config: Dict[str, Any], system_name: str) -> ValidationResult:
        """Validate a complete system configuration"""
        result = ValidationResult(context=f"system:{system_name}")
        
        # Validate metadata section
        metadata_result = self._validate_metadata(config.get("metadata", {}))
        result.merge(metadata_result)
        
        # Validate policy defaults
        policy_defaults_result = self._validate_policy_defaults(config.get("policy_defaults", {}))
        result.merge(policy_defaults_result)
        
        # Validate flows configuration
        flows_result = self._validate_flows_config(config.get("flows", {}))
        result.merge(flows_result)
        
        # Validate target configuration
        target_result = self._validate_target_config(config.get("target", {}))
        result.merge(target_result)
        
        # Validate feature flags
        features_result = self._validate_features(config.get("features", {}))
        result.merge(features_result)
        
        return result
    
    def _validate_metadata(self, metadata: Dict[str, Any]) -> ValidationResult:
        """Validate metadata section"""
        result = ValidationResult(context="metadata")
        
        required_fields = ["system", "owner_team"]
        for field in required_fields:
            if not metadata.get(field):
                result.add_error(f"Missing required metadata field: {field}")
        
        # Validate system name format
        system_name = metadata.get("system", "")
        if system_name and not re.match(r'^[a-z][a-z0-9-]*$', system_name):
            result.add_error("System name must be lowercase, start with letter, and contain only letters, numbers, and hyphens")
        
        return result
    
    def _validate_policy_defaults(self, policy_defaults: Dict[str, Any]) -> ValidationResult:
        """Validate policy defaults section"""
        result = ValidationResult(context="policy_defaults")
        
        for policy_name, params in policy_defaults.items():
            if not isinstance(params, dict):
                result.add_error(f"Policy defaults for '{policy_name}' must be a dictionary")
                continue
            
            # Validate specific policy parameters
            policy_result = self._validate_policy_params(policy_name, params)
            result.merge(policy_result)
        
        return result
    
    def _validate_policy_params(self, policy_name: str, params: Dict[str, Any]) -> ValidationResult:
        """Validate parameters for a specific policy"""
        result = ValidationResult(context=f"policy:{policy_name}")
        
        if policy_name == "SpikeArrest":
            rate = params.get("rate")
            if rate and not self.helpers.is_valid_rate(rate):
                result.add_error(f"Invalid rate format for SpikeArrest: {rate}")
        
        elif policy_name == "Quota":
            allow = params.get("allow")
            if allow and not self.helpers.is_valid_rate(allow):
                result.add_error(f"Invalid quota format: {allow}")
            
            timeunit = params.get("timeunit")
            if timeunit and timeunit not in ["second", "minute", "hour", "day", "week", "month"]:
                result.add_error(f"Invalid timeunit for Quota: {timeunit}")
        
        elif policy_name == "VerifyApiKey":
            keyref = params.get("keyref")
            if keyref and not keyref.startswith("request."):
                result.add_warning(f"API key reference should typically start with 'request.': {keyref}")
        
        elif policy_name == "CORS":
            origins = params.get("allow_origins", [])
            if isinstance(origins, list):
                for origin in origins:
                    if origin != "*" and not self.helpers.is_valid_url(origin):
                        result.add_error(f"Invalid CORS origin URL: {origin}")
            
            headers = params.get("allow_headers", [])
            if isinstance(headers, list):
                for header in headers:
                    if not self.helpers.is_valid_header_name(header):
                        result.add_error(f"Invalid header name: {header}")
        
        return result
    
    def _validate_flows_config(self, flows: Dict[str, Any]) -> ValidationResult:
        """Validate flows configuration"""
        result = ValidationResult(context="flows")
        
        # Validate flow structure
        expected_flows = ["preflow", "per_operation"]
        for flow_name in expected_flows:
            if flow_name not in flows:
                result.add_warning(f"Missing flow configuration: {flow_name}")
                continue
            
            flow_config = flows[flow_name]
            if not isinstance(flow_config, dict):
                result.add_error(f"Flow '{flow_name}' must be a dictionary")
                continue
            
            # Validate request/response sections
            for section in ["request", "response"]:
                if section in flow_config:
                    policies = flow_config[section]
                    if not isinstance(policies, list):
                        result.add_error(f"Flow '{flow_name}.{section}' must be a list")
                    else:
                        for policy in policies:
                            if isinstance(policy, str):
                                # Simple policy name - validate it exists
                                if not self._is_valid_policy_name(policy):
                                    result.add_warning(f"Unknown policy name: {policy}")
                            elif isinstance(policy, dict):
                                # Complex policy definition
                                if "policy" not in policy:
                                    result.add_error(f"Policy definition missing 'policy' field in {flow_name}.{section}")
        
        # Validate CORS configuration
        cors_config = flows.get("cors", {})
        if cors_config:
            if not isinstance(cors_config.get("enabled"), bool):
                result.add_error("CORS 'enabled' field must be boolean")
            
            if cors_config.get("enabled") and not cors_config.get("policy"):
                result.add_error("CORS configuration missing 'policy' field when enabled")
        
        return result
    
    def _validate_target_config(self, target: Dict[str, Any]) -> ValidationResult:
        """Validate target endpoint configuration"""
        result = ValidationResult(context="target")
        
        baseurl = target.get("baseurl")
        if baseurl and not self.helpers.is_valid_url(baseurl):
            result.add_error(f"Invalid target baseurl: {baseurl}")
        
        # Validate timeout values
        for timeout_field in ["connect_timeout_ms", "read_timeout_ms"]:
            timeout_value = target.get(timeout_field)
            if timeout_value is not None:
                if not isinstance(timeout_value, int) or timeout_value <= 0:
                    result.add_error(f"Invalid {timeout_field}: must be positive integer")
                elif timeout_value > 300000:  # 5 minutes
                    result.add_warning(f"Very high {timeout_field}: {timeout_value}ms")
        
        return result
    
    def _validate_features(self, features: Dict[str, Any]) -> ValidationResult:
        """Validate features configuration"""
        result = ValidationResult(context="features")
        
        for feature_name, feature_value in features.items():
            if not isinstance(feature_value, bool):
                result.add_warning(f"Feature '{feature_name}' should be boolean, got {type(feature_value).__name__}")
        
        return result
    
    def _is_valid_policy_name(self, policy_name: str) -> bool:
        """Check if a policy name is valid/known"""
        known_policies = {
            "VerifyApiKey", "SpikeArrest", "Quota", "CORS", "AssignMessage",
            "ExtractVariables", "JSONThreatProtection", "XMLThreatProtection",
            "RegularExpressionProtection", "ResponseCache", "FlowCallout",
            "RaiseFault", "AccessControl", "KeyValueMapOperations",
            "DecodeJWT", "VerifyJWT", "GenerateJWT", "OAuthV2"
        }
        return policy_name in known_policies


class TemplateValidator:
    """Validates policy templates"""
    
    def validate_template(self, template_path: Path, test_params: Optional[Dict[str, Any]] = None) -> ValidationResult:
        """Validate a policy template"""
        result = ValidationResult(context=f"template:{template_path.name}")
        
        if not template_path.exists():
            result.add_error(f"Template file not found: {template_path}")
            return result
        
        try:
            # Read template content
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # Basic syntax validation
            syntax_result = self._validate_template_syntax(template_content)
            result.merge(syntax_result)
            
            # Try rendering with test parameters
            if test_params:
                render_result = self._validate_template_rendering(template_path, test_params)
                result.merge(render_result)
            
        except Exception as e:
            result.add_error(f"Error reading template: {str(e)}")
        
        return result
    
    def _validate_template_syntax(self, template_content: str) -> ValidationResult:
        """Validate Jinja2 template syntax"""
        result = ValidationResult(context="template_syntax")
        
        try:
            from jinja2 import Environment, meta
            
            env = Environment()
            ast = env.parse(template_content)
            
            # Check for undefined variables (basic check)
            undefined_vars = meta.find_undeclared_variables(ast)
            for var in undefined_vars:
                if var not in ['name', 'params', 'input', 'helpers']:
                    result.add_warning(f"Potentially undefined variable: {var}")
            
        except Exception as e:
            result.add_error(f"Template syntax error: {str(e)}")
        
        return result
    
    def _validate_template_rendering(self, template_path: Path, test_params: Dict[str, Any]) -> ValidationResult:
        """Validate template rendering with test parameters"""
        result = ValidationResult(context="template_rendering")
        
        try:
            from ..services.policy_builder import PolicyBuilder
            
            builder = PolicyBuilder()
            rendered_xml = builder._render_template(
                template_path.name,
                name="TEST-Policy",
                params=test_params,
                input={"methods": ["GET"], "headers": ["Authorization"]}
            )
            
            # Validate rendered XML
            xml_result = self._validate_rendered_xml(rendered_xml)
            result.merge(xml_result)
            
        except Exception as e:
            result.add_error(f"Template rendering failed: {str(e)}")
        
        return result
    
    def _validate_rendered_xml(self, xml_content: str) -> ValidationResult:
        """Validate rendered XML content"""
        result = ValidationResult(context="rendered_xml")
        
        try:
            # Parse XML to check for well-formedness
            ET.fromstring(xml_content)
            
            # Check for common issues
            if not xml_content.strip():
                result.add_error("Rendered XML is empty")
            
            if "{{" in xml_content or "}}" in xml_content:
                result.add_warning("Unresolved template variables found in rendered XML")
            
        except ET.ParseError as e:
            result.add_error(f"Invalid XML structure: {str(e)}")
        
        return result


class ConfigurationValidator:
    """Main configuration validator that orchestrates all validation"""
    
    def __init__(self):
        self.system_validator = SystemConfigValidator()
        self.template_validator = TemplateValidator()
    
    def validate_all(self, config_dir: Path) -> Dict[str, ValidationResult]:
        """Validate all configurations in a directory"""
        results = {}
        
        # Validate system configurations
        systems_dir = config_dir / "systems"
        if systems_dir.exists():
            for system_file in systems_dir.glob("*.yaml"):
                if system_file.name.endswith("_features.yaml"):
                    continue  # Skip feature flag files
                
                system_name = system_file.stem
                try:
                    with open(system_file, 'r') as f:
                        config = yaml.safe_load(f)
                    
                    result = self.system_validator.validate_system_config(config, system_name)
                    results[f"system:{system_name}"] = result
                    
                except Exception as e:
                    error_result = ValidationResult(is_valid=False)
                    error_result.add_error(f"Failed to load system config: {str(e)}")
                    results[f"system:{system_name}"] = error_result
        
        # Validate templates
        templates_dir = config_dir.parent / "templates" / "policies"
        if templates_dir.exists():
            for template_file in templates_dir.glob("*.xml.j2"):
                result = self.template_validator.validate_template(template_file)
                results[f"template:{template_file.name}"] = result
        
        return results
    
    def generate_validation_report(self, results: Dict[str, ValidationResult]) -> str:
        """Generate a comprehensive validation report"""
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("CONFIGURATION VALIDATION REPORT")
        report_lines.append("=" * 60)
        
        total_configs = len(results)
        valid_configs = sum(1 for r in results.values() if r.is_valid)
        invalid_configs = total_configs - valid_configs
        
        report_lines.append(f"Total Configurations: {total_configs}")
        report_lines.append(f"Valid: {valid_configs}")
        report_lines.append(f"Invalid: {invalid_configs}")
        report_lines.append(f"Success Rate: {(valid_configs/total_configs*100):.1f}%")
        report_lines.append("")
        
        # Detailed results
        for config_name, result in results.items():
            status = "✅ VALID" if result.is_valid else "❌ INVALID"
            report_lines.append(f"{status} {config_name}")
            
            if result.errors:
                for error in result.errors:
                    report_lines.append(f"  ERROR: {error}")
            
            if result.warnings:
                for warning in result.warnings:
                    report_lines.append(f"  WARNING: {warning}")
            
            if result.errors or result.warnings:
                report_lines.append("")
        
        return "\n".join(report_lines)
