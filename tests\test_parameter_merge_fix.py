#!/usr/bin/env python3
"""
Test parameter merge fix for dictionary update sequence error.
This test reproduces and verifies the fix for the ValueError that occurs
when trying to merge incompatible types in parameter resolution.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder


def test_parameter_merge_fix():
    """Test that parameter merging handles type mismatches correctly."""
    print("============================================================")
    print("TESTING PARAMETER MERGE FIX")
    print("============================================================")
    
    try:
        # Load the actual system configuration that's causing the issue
        system_config_path = "app/config/systems/REFLECT_FDID.yaml"
        with open(system_config_path, 'r') as f:
            system_cfg = yaml.safe_load(f)
        
        print(f"✅ Loaded system configuration: {len(system_cfg.get('policies', {}))} policies")
        
        # Test input variables that might cause the issue
        test_input_vars = {
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],
            "content_types": ["application/json"],
            "policy_params": {
                # This might cause type conflicts
                "VK-VerifyApiKey": {
                    "keyref": "request.header.x-api-key"
                }
            }
        }
        
        # Test policy builder with the actual configuration
        print("🏗️ Testing Policy Builder with REFLECT_FDID configuration...")
        policy_builder = PolicyBuilder()
        
        try:
            policies, attach_plan = policy_builder.render_policies(
                system_cfg=system_cfg,
                input_vars=test_input_vars,
                system_name="REFLECT_FDID"
            )
            
            print(f"   ✅ Policy Builder: Successfully rendered {len(policies)} policies")
            
            # Verify some key policies are rendered
            policy_names = [p.name for p in policies]
            expected_policies = ["VK-VerifyApiKey", "SA-SpikeArrest", "Q-ImposeQuota"]
            
            for expected in expected_policies:
                if expected in policy_names:
                    print(f"      ✅ {expected}: Found in rendered policies")
                else:
                    print(f"      ⚠️  {expected}: Not found in rendered policies")
            
            # Test attach plan
            total_policies = (
                len(attach_plan.preflow_request) + 
                len(attach_plan.preflow_response) +
                len(attach_plan.perflow_request) + 
                len(attach_plan.perflow_response) +
                len(attach_plan.postclientflow_response) +
                len(attach_plan.nomatchfound_request)
            )
            
            print(f"   ✅ Attach Plan: {total_policies} total policy attachments")
            print(f"      PreFlow Request: {len(attach_plan.preflow_request)}")
            print(f"      PreFlow Response: {len(attach_plan.preflow_response)}")
            print(f"      PerFlow Request: {len(attach_plan.perflow_request)}")
            print(f"      PerFlow Response: {len(attach_plan.perflow_response)}")
            
        except ValueError as e:
            if "dictionary update sequence element" in str(e):
                print(f"   ❌ Dictionary update sequence error still occurring: {e}")
                return False
            else:
                print(f"   ❌ Different ValueError: {e}")
                return False
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test with different input configurations that might cause issues
        print(f"\n🧪 Testing Edge Cases...")
        
        # Test case 1: Mixed parameter types
        edge_case_1 = {
            **test_input_vars,
            "policy_params": {
                "VK-VerifyApiKey": "simple_string_value",  # This might cause issues
                "SA-SpikeArrest": {"rate": "200pm"}
            }
        }
        
        try:
            policies, _ = policy_builder.render_policies(
                system_cfg=system_cfg,
                input_vars=edge_case_1,
                system_name="REFLECT_FDID"
            )
            print(f"   ✅ Edge Case 1: Handled mixed parameter types successfully")
        except Exception as e:
            print(f"   ❌ Edge Case 1 failed: {e}")
            return False
        
        # Test case 2: Empty policy params
        edge_case_2 = {
            **test_input_vars,
            "policy_params": {}
        }
        
        try:
            policies, _ = policy_builder.render_policies(
                system_cfg=system_cfg,
                input_vars=edge_case_2,
                system_name="REFLECT_FDID"
            )
            print(f"   ✅ Edge Case 2: Handled empty policy params successfully")
        except Exception as e:
            print(f"   ❌ Edge Case 2 failed: {e}")
            return False
        
        # Test case 3: Conflicting parameter types
        edge_case_3 = {
            **test_input_vars,
            "policy_params": {
                "VK-VerifyApiKey": ["list", "instead", "of", "dict"],  # Wrong type
                "SA-SpikeArrest": {"rate": "200pm"}
            }
        }
        
        try:
            policies, _ = policy_builder.render_policies(
                system_cfg=system_cfg,
                input_vars=edge_case_3,
                system_name="REFLECT_FDID"
            )
            print(f"   ✅ Edge Case 3: Handled conflicting parameter types successfully")
        except Exception as e:
            print(f"   ❌ Edge Case 3 failed: {e}")
            return False
        
        print("\n============================================================")
        print("✅ PARAMETER MERGE FIX TEST PASSED!")
        print("Dictionary update sequence error has been resolved.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_parameter_merge_fix()
    sys.exit(0 if success else 1)
