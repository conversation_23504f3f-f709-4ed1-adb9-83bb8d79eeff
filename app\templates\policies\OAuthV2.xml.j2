<!-- OAuthV2 VerifyAccessToken -->
<OAuthV2 name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  <Operation>VerifyAccessToken</Operation>

  {% if params.token_ref %}
  <!-- Expect "Bearer <token>" in header or raw token in variable -->
  <AccessToken ref="{{ params.token_ref }}"/>
  {% else %}
  <!-- Default: Authorization header -->
  <AccessToken ref="request.header.Authorization"/>
  {% endif %}

  <!-- Strip "Bearer " prefix if present -->
  <RemoveOAuthTokenPrefix>{{ params.remove_prefix | default('true') }}</RemoveOAuthTokenPrefix>

  {% if params.scope %}
  <!-- Optional scope check (space-delimited) -->
  <Scope>{{ params.scope }}</Scope>
  {% endif %}

  <!-- Optional: set to 'true' to populate token variables in message context -->
  {% if params.generate_response is defined %}
  <GenerateResponse>{{ params.generate_response }}</GenerateResponse>
  {% endif %}
</OAuthV2>
