# Simplified System Configuration for Apigee Proxy Framework
# Version 2.0 - Streamlined and intuitive structure

# Basic system metadata
metadata:
  system: "REFLECT_FDID"
  owner_team: "Reflect IT"
  description: "Reflect FDID Adaptor APIs"
  version: "1.0"

# Git repository configuration
git:
  bundles_repo: "api-and-gateway/api-deployment/digital-apps/reflect/edge/proxy"
  source_repo: "api-and-gateway/api-deployment/digital-apps/reflect/edge/proxy"
  default_branch: "develop"

# Security configuration
security:
  type: "apikey"  # apikey | oauth2 | jwt
  policies: ["VK-VerifyApiKey"]  # Primary security policies

# Policy definitions - template-based structure
policies:
  # Security Policies
  VK-VerifyApiKey:
    template: "VerifyApiKey.xml.j2"
    display_name: "VK-VerifyApiKey"
    keyref: "request.header.client_id"

  OA-OAuthV2:
    template: "OAuthV2.xml.j2"
    display_name: "OA-OAuthV2"
    operation: "VerifyAccessToken"
    access_token_ref: "request.header.Authorization"
    remove_prefix: true

  # Traffic Management
  SA-SpikeArrest:
    template: "SpikeArrest.xml.j2"
    display_name: "SA-SpikeArrest"
    rate: "100pm"

  Q-ImposeQuota:
    template: "Quota.xml.j2"
    display_name: "Q-ImposeQuota"
    allow: "100"
    interval: "1"
    timeunit: "minute"
    identifier_ref: "request.header.client_id"

  # CORS Configuration
  AM-AddCors:
    template: "AssignMessage.CORS.xml.j2"
    display_name: "AM-AddCors"
    allow_origins: ["*"]
    allow_methods: ["OPTIONS"]
    allow_headers: ["accept", "content-type", "client_id", "message-id"]
    expose_headers: ["x-request-id"]
    max_age: 86400
    allow_credentials: false

  # AssignMessage Policies - Multiple instances for different purposes
  AM-AddRespHeaders:
    template: "AssignMessage.xml.j2"
    display_name: "AM-AddRespHeaders"
    ignore_unresolved_variables: true
    add:
      headers:
        - name: "X-Response-Time"
          value: "{system.timestamp}"
        - name: "X-API-Version"
          value: "v1.0"

  AM-DisablePathCopy:
    template: "AssignMessage.xml.j2"
    display_name: "AM-DisablePathCopy"
    ignore_unresolved_variables: true
    copy:
      path: false

  AM-RemoveRequestHeaders:
    template: "AssignMessage.xml.j2"
    display_name: "AM-RemoveRequestHeaders"
    ignore_unresolved_variables: true
    remove:
      headers:
        - "X-Forwarded-For"
        - "X-Real-IP"

  AM-SetRequestData:
    template: "AssignMessage.xml.j2"
    display_name: "AM-SetRequestData"
    ignore_unresolved_variables: true
    set:
      headers:
        - name: "X-Request-ID"
          value: "{messageid}"
        - name: "X-Client-IP"
          value: "{client.ip}"

  AM-SetResponse:
    template: "AssignMessage.xml.j2"
    display_name: "AM-SetResponse"
    ignore_unresolved_variables: true
    assign_to:
      createNew: false
      type: "response"
      transport: "http"
    set:
      headers:
        - name: "Content-Type"
          value: "application/json"

  AM-SetTargetEndTime:
    template: "AssignMessage.xml.j2"
    display_name: "AM-SetTargetEndTime"
    ignore_unresolved_variables: true
    set:
      headers:
        - name: "X-Target-End-Time"
          value: "{system.timestamp}"

  AM-SetTargetStartTime:
    template: "AssignMessage.xml.j2"
    display_name: "AM-SetTargetStartTime"
    ignore_unresolved_variables: true
    set:
      headers:
        - name: "X-Target-Start-Time"
          value: "{system.timestamp}"

  # ExtractVariables Policies
  EV-ExtractRequestPayload:
    template: "ExtractVariables.xml.j2"
    display_name: "EV-ExtractRequestPayload"
    source: "request"
    ignore_unresolved: true
    json:
      - name: "request.payload.data"
        path: "$.data"
      - name: "request.payload.id"
        path: "$.id"

  # FlowCallout Policies - Multiple instances for different shared flows
  FC-CommonErrorHandler:
    template: "FlowCallout.xml.j2"
    display_name: "FC-CommonErrorHandler"
    shared_flow: "CommonErrorHandler"
    parameters:
      - name: "error.source"
        value: "REFLECT_FDID"
      - name: "error.context"
        value: "proxy"

  FC-CommonSecurityPreFlow:
    template: "FlowCallout.xml.j2"
    display_name: "FC-CommonSecurityPreFlow"
    shared_flow: "CommonSecurityPreFlow"
    parameters:
      - name: "security.level"
        value: "standard"
      - name: "security.context"
        value: "REFLECT_FDID"

  FC-CommonTargetErrorHandler:
    template: "FlowCallout.xml.j2"
    display_name: "FC-CommonTargetErrorHandler"
    shared_flow: "CommonTargetErrorHandler"
    parameters:
      - name: "target.error.source"
        value: "REFLECT_FDID"
      - name: "target.error.context"
        value: "target"

  FC-ExtractCustomLogs:
    template: "FlowCallout.xml.j2"
    display_name: "FC-ExtractCustomLogs"
    shared_flow: "ExtractCustomLogs"
    parameters:
      - name: "log.source"
        value: "REFLECT_FDID"
      - name: "log.level"
        value: "INFO"

  FC-PushLogsToPubSub:
    template: "FlowCallout.xml.j2"
    display_name: "FC-PushLogsToPubSub"
    shared_flow: "PushLogsToPubSub"
    parameters:
      - name: "pubsub.topic"
        value: "reflect-fdid-logs"
      - name: "pubsub.project"
        value: "reflect-project"

  # RaiseFault Policies - Multiple instances for different error scenarios
  RF-MethodNotAllowed:
    template: "RaiseFault.xml.j2"
    display_name: "RF-MethodNotAllowed"
    status_code: "405"
    reason_phrase: "Method Not Allowed"
    ignore_unresolved_variables: true
    payload: |
      {
        "error": {
          "code": "METHOD_NOT_ALLOWED",
          "message": "The requested HTTP method is not allowed for this resource",
          "timestamp": "{system.timestamp}"
        }
      }
    content_type: "application/json"

  RF-ResourceNotFound:
    template: "RaiseFault.xml.j2"
    display_name: "RF-ResourceNotFound"
    status_code: "404"
    reason_phrase: "Resource Not Found"
    ignore_unresolved_variables: true
    payload: |
      {
        "error": {
          "code": "RESOURCE_NOT_FOUND",
          "message": "The requested resource was not found",
          "timestamp": "{system.timestamp}"
        }
      }
    content_type: "application/json"
  
  # Caching
  RC-ResponseCache:
    template: "ResponseCache.xml.j2"
    display_name: "RC-ResponseCache"
    cache_resource: "response-cache"
    scope: "environment"
    expiry_seconds: 60

  # Threat Protection
  JTP-JSONThreatProtection:
    template: "JSONThreatProtection.xml.j2"
    display_name: "JTP-JSONThreatProtection"
    source: "request"
    max_container_depth: 32
    max_array_elements: 10000
    max_object_members: 1000
    max_string_length: 8192

  XTP-XMLThreatProtection:
    template: "XMLThreatProtection.xml.j2"
    display_name: "XTP-XMLThreatProtection"
    source: "request"
    max_element_count: 1000
    max_attribute_count: 100
    max_element_depth: 32
    max_text_length: 8192

  # Additional Security Policies
  BA-BasicAuthentication:
    template: "BasicAuthentication.xml.j2"
    display_name: "BA-BasicAuthentication"
    operation: "Decode"
    source: "request.header.authorization"
    ignore_unresolved_variables: true

  # Service Integration
  SC-ServiceCallout:
    template: "ServiceCallout.xml.j2"
    display_name: "SC-ServiceCallout"
    url: "https://api.example.com/service"
    timeout: 30000
    ignore_unresolved_variables: true
  

# Flow attachment configuration - optimized with direct policy references
flows:
  # Proxy endpoint flows
  proxy:
    # Policies that run before all API flows
    preflow:
      request:
        - VK-VerifyApiKey
        - FC-CommonSecurityPreFlow
        - SA-SpikeArrest
        - AM-SetRequestData
      response:
        - AM-AddRespHeaders

    # Policies that run for each API operation
    per_operation:
      request:
        - Q-ImposeQuota
        - EV-ExtractRequestPayload
      response:
        - FC-ExtractCustomLogs

    # PostFlow policies (run after all flows)
    postflow:
      request: []
      response: []

    # PostClientFlow policies (run after response is sent to client)
    postclientflow:
      request: []
      response:
        - FC-PushLogsToPubSub

    # NoMatchFound flow (when no resource matches)
    nomatchfound:
      request:
        - RF-ResourceNotFound
      response: []

  # Error handling flows
  error_flows:
    method_not_allowed:
      - RF-MethodNotAllowed
    resource_not_found:
      - RF-ResourceNotFound

  # CORS handling
  cors:
    enabled: true
    policy: AM-AddCors

# Target endpoints configuration - supports multiple targets
targets:
  default:
    name: "default"
    baseurl: "https://payments.internal.example.com"
    connect_timeout_ms: 4000
    read_timeout_ms: 30000
    ssl_profile: "SSLInfo"
    flows:
      preflow:
        request:
          - AM-SetTargetStartTime
          - FC-CommonTargetErrorHandler
        response:
          - AM-SetTargetEndTime
      postflow:
        request: []
        response:
          - FC-ExtractCustomLogs

  default-jo:
    name: "default-jo"
    baseurl: "https://payments-jo.internal.example.com"
    connect_timeout_ms: 5000
    read_timeout_ms: 45000
    ssl_profile: "SSLInfo"
    flows:
      preflow:
        request:
          - AM-SetTargetStartTime
        response:
          - AM-SetTargetEndTime
      postflow:
        request: []
        response: []

# Flow generation settings
flow_settings:
  include_verb_in_condition: true
  regex_required: false

# Resource files to include
resources:
  - path: "xsl/transform/normalize.xsl"
    type: "resource"

# Feature flags
features:
  advanced_security: false
  caching_enabled: true
  threat_protection: false
