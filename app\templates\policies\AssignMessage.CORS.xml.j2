<!-- Smart CORS AssignMessage with validation and environment awareness -->
<AssignMessage name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  <IgnoreUnresolvedVariables>true</IgnoreUnresolvedVariables>

  <Set>
    <Headers>
      <!-- Smart origin handling with validation -->
      {% set origins = normalize_list(params.get('allow_origins', []), ',') %}
      {% if not origins %}
        {% if is_production() %}
          {% set origins = ['https://app.example.com'] %}  <!-- Secure default for prod -->
        {% else %}
          {% set origins = ['*'] %}  <!-- Permissive for dev/test -->
        {% endif %}
      {% endif %}
      <Header name="Access-Control-Allow-Origin">
        {{ helpers.format_origins_list(origins) }}
      </Header>

      <!-- Smart methods with environment-specific defaults -->
      {% set methods = normalize_list(params.get('allow_methods', []), ',') %}
      {% if not methods %}
        {% set methods = ['GET','POST','PUT','PATCH','DELETE','OPTIONS'] %}
      {% endif %}
      {% if is_production() and not feature_enabled('allow_delete_in_prod') %}
        {% set methods = methods | reject('equalto', 'DELETE') | list %}
      {% endif %}
      <Header name="Access-Control-Allow-Methods">
        {{ methods | join(',') }}
      </Header>

      <!-- Smart headers with validation and OAS integration -->
      {% set headers = normalize_list(params.get('allow_headers', []), ',') %}
      {% if not headers %}
        {% set headers = ['Authorization','Content-Type','x-api-key','x-request-id'] %}
      {% endif %}
      {% if input.get('headers') %}
        {% set headers = headers + normalize_list(input.headers, ',') %}
      {% endif %}
      <Header name="Access-Control-Allow-Headers">
        {{ helpers.format_headers_list(headers) }}
      </Header>

      <!-- Smart expose headers -->
      {% set expose_headers = normalize_list(params.get('expose_headers', []), ',') %}
      {% if not expose_headers %}
        {% set expose_headers = ['x-request-id'] %}
      {% endif %}
      {% if feature_enabled('expose_custom_headers') and params.get('custom_expose_headers') %}
        {% set expose_headers = expose_headers + normalize_list(params.custom_expose_headers, ',') %}
      {% endif %}
      <Header name="Access-Control-Expose-Headers">
        {{ helpers.format_headers_list(expose_headers) }}
      </Header>

      <!-- Use resolved parameter value or apply smart defaults only if not provided -->
      {% if params.get('max_age') is not none %}
        <!-- Use the resolved parameter value (from system/env/input) -->
        {% set max_age = params.max_age %}
      {% else %}
        <!-- Apply smart defaults only when no value is configured -->
        {% if is_production() %}
          {% set max_age = 3600 %}  <!-- Shorter cache in prod -->
        {% else %}
          {% set max_age = 86400 %}
        {% endif %}
      {% endif %}
      <Header name="Access-Control-Max-Age">{{ max_age }}</Header>

      <Header name="Vary">Origin</Header>

      <!-- Conditional credentials support -->
      {% if 'allow_credentials' in params %}
        <Header name="Access-Control-Allow-Credentials">{{ 'true' if params.allow_credentials else 'false' }}</Header>
      {% elif feature_enabled('cors_credentials') %}
        <Header name="Access-Control-Allow-Credentials">true</Header>
      {% endif %}

      <!-- Security headers for production -->
      {% if is_production() and feature_enabled('security_headers') %}
        <Header name="X-Content-Type-Options">nosniff</Header>
        <Header name="X-Frame-Options">DENY</Header>
      {% endif %}
    </Headers>

    <!-- Preflight response -->
    <StatusCode>200</StatusCode>
    <ReasonPhrase>OK</ReasonPhrase>
  </Set>

  <!-- Attach to response or used as a dedicated OPTIONS flow -->
  <AssignTo createNew="false" type="response" transport="http"/>
</AssignMessage>
