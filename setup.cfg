[metadata]
name = apigee-proxy-factory
version = 1.0.0
description = Comprehensive Python framework for generating Apigee API proxy configurations with intelligent policy management
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/your-org/apigee-proxy-factory
author = Apigee Proxy Factory Team
author_email = <EMAIL>
license = MIT
license_file = LICENSE
classifiers =
    Development Status :: 5 - Production/Stable
    Intended Audience :: Developers
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Internet :: WWW/HTTP :: HTTP Servers
    Topic :: System :: Systems Administration
    License :: OSI Approved :: MIT License
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Operating System :: OS Independent
    Framework :: FastAPI
    Environment :: Web Environment

[options]
packages = find:
python_requires = >=3.8
include_package_data = True
zip_safe = False
install_requires =
    fastapi>=0.111.0
    uvicorn[standard]>=0.30.0
    pydantic>=2.8.0
    pydantic-settings>=2.3.0
    httpx>=0.27.0
    jinja2>=3.1.0
    lxml>=5.2.0
    PyYAML>=6.0.0
    python-multipart>=0.0.9
    structlog>=24.1.0
    orjson>=3.10.0
    markupsafe>=2.1.0
    jsonschema>=4.20.0
    cerberus>=1.3.5
    python-dotenv>=1.0.0
    cachetools>=5.3.0
    typing-extensions>=4.8.0
    cryptography>=41.0.0
    aiofiles>=23.2.0
    pytest>=7.4.0

[options.packages.find]
exclude =
    tests*
    docs*

[options.package_data]
app =
    templates/**/*.j2
    config/**/*.yaml
    config/**/*.yml
    docs/**/*.md

[options.entry_points]
console_scripts =
    apigee-proxy-factory = app.main:main
    apf-test = run_tests:main

[options.extras_require]
dev =
    pytest-cov>=4.1.0
    pytest-html>=4.1.0
    pytest-xdist>=3.3.0
    pytest-mock>=3.12.0
    pytest-asyncio>=0.21.0
    pytest-timeout>=2.2.0
    pytest-benchmark>=4.0.0
    responses>=0.24.0
    factory-boy>=3.3.0
    faker>=20.0.0
    freezegun>=1.2.0
    flake8>=6.1.0
    black>=23.9.0
    isort>=5.12.0
    mypy>=1.6.0
    bandit>=1.7.5
    coverage>=7.3.0
    pre-commit>=3.5.0
test =
    pytest>=7.4.0
    pytest-cov>=4.1.0
    pytest-html>=4.1.0
    pytest-mock>=3.12.0
    responses>=0.24.0
    faker>=20.0.0
    coverage>=7.3.0
optional =
    sqlalchemy>=2.0.0
    redis>=5.0.0
    celery>=5.3.0
    prometheus-client>=0.18.0

# Flake8 configuration
[flake8]
max-line-length = 120
extend-ignore = E203, E266, E501, W503, F403, F401
max-complexity = 18
select = B,C,E,F,W,T4,B9
exclude =
    .git,
    __pycache__,
    docs/source/conf.py,
    old,
    build,
    dist,
    .venv,
    venv,
    .tox,
    .eggs,
    *.egg

# Pylint configuration
[pylint]
load-plugins = pylint.extensions.docparams
disable = C0330, C0326

[pylint.messages_control]
disable = R0903, R0913, C0103, W0613

[pylint.reports]
output-format = text
files-output = no
reports = no
evaluation = 10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[pylint.refactoring]
max-nested-blocks = 5

[pylint.basic]
good-names = i, j, k, ex, Run, _

[pylint.format]
max-line-length = 120

[pylint.variables]
dummy-variables-rgx = _+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy|^ignored_|^unused_

[pylint.similarities]
min-similarity-lines = 7
ignore-comments = yes
ignore-docstrings = yes
ignore-imports = no

[pylint.typecheck]
ignored-modules = distutils

[pylint.logging]
logging-modules = logging

# Wheel configuration
[bdist_wheel]
universal = 0

# Aliases for setup.py commands
[aliases]
test = pytest
