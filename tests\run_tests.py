#!/usr/bin/env python3
"""
Test Runner for Apigee Proxy Factory
====================================
Comprehensive test runner with different test categories and reporting.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} - PASSED")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - FAILED (exit code: {e.returncode})")
        return False
    except FileNotFoundError:
        print(f"❌ {description} - FAILED (command not found)")
        return False


def run_unit_tests():
    """Run unit tests"""
    cmd = ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short"]
    return run_command(cmd, "Unit Tests")


def run_integration_tests():
    """Run integration tests"""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "integration", "-v", "--tb=short"]
    return run_command(cmd, "Integration Tests")


def run_api_tests():
    """Run API tests"""
    cmd = ["python", "-m", "pytest", "tests/unit/api/", "-v", "--tb=short"]
    return run_command(cmd, "API Tests")


def run_service_tests():
    """Run service tests"""
    cmd = ["python", "-m", "pytest", "tests/unit/services/", "-v", "--tb=short"]
    return run_command(cmd, "Service Tests")


def run_coverage_tests():
    """Run tests with coverage reporting"""
    cmd = [
        "python", "-m", "pytest", 
        "tests/", 
        "--cov=app", 
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "--cov-fail-under=80",
        "-v"
    ]
    return run_command(cmd, "Coverage Tests")


def run_performance_tests():
    """Run performance tests"""
    cmd = ["python", "-m", "pytest", "tests/", "-m", "performance", "-v", "--tb=short"]
    return run_command(cmd, "Performance Tests")


def run_phase_tests():
    """Run phase-specific tests"""
    phase_tests = [
        ("test_phase1_foundation.py", "Phase 1: Foundation"),
        ("test_phase2_enhanced_config.py", "Phase 2: Enhanced Configuration"),
        ("test_phase3_smart_templates.py", "Phase 3: Smart Templates"),
        ("test_phase4_advanced_features.py", "Phase 4: Advanced Features")
    ]
    
    results = []
    for test_file, description in phase_tests:
        if Path(test_file).exists():
            cmd = ["python", test_file]
            success = run_command(cmd, description)
            results.append((description, success))
        else:
            print(f"⚠️  {description} - SKIPPED (file not found: {test_file})")
            results.append((description, None))
    
    return results


def run_lint_checks():
    """Run code quality checks"""
    checks = [
        (["python", "-m", "flake8", "app/", "--max-line-length=120"], "Flake8 Linting"),
        (["python", "-m", "black", "--check", "app/"], "Black Code Formatting"),
        (["python", "-m", "isort", "--check-only", "app/"], "Import Sorting"),
        (["python", "-m", "mypy", "app/"], "Type Checking")
    ]
    
    results = []
    for cmd, description in checks:
        try:
            success = run_command(cmd, description)
            results.append((description, success))
        except:
            print(f"⚠️  {description} - SKIPPED (tool not installed)")
            results.append((description, None))
    
    return results


def generate_test_report():
    """Generate comprehensive test report"""
    print(f"\n{'='*60}")
    print("GENERATING COMPREHENSIVE TEST REPORT")
    print(f"{'='*60}")
    
    # Run all tests with detailed reporting
    cmd = [
        "python", "-m", "pytest",
        "tests/",
        "--cov=app",
        "--cov-report=html:htmlcov",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "--junit-xml=test-results.xml",
        "--html=test-report.html",
        "--self-contained-html",
        "-v",
        "--tb=short",
        "--durations=10"
    ]
    
    success = run_command(cmd, "Comprehensive Test Report Generation")
    
    if success:
        print(f"\n📊 Test reports generated:")
        print(f"  - HTML Coverage: htmlcov/index.html")
        print(f"  - XML Coverage: coverage.xml")
        print(f"  - JUnit XML: test-results.xml")
        print(f"  - HTML Report: test-report.html")
    
    return success


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description="Apigee Proxy Factory Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests")
    parser.add_argument("--integration", action="store_true", help="Run integration tests")
    parser.add_argument("--api", action="store_true", help="Run API tests")
    parser.add_argument("--services", action="store_true", help="Run service tests")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--phases", action="store_true", help="Run phase tests")
    parser.add_argument("--lint", action="store_true", help="Run code quality checks")
    parser.add_argument("--report", action="store_true", help="Generate comprehensive report")
    parser.add_argument("--all", action="store_true", help="Run all tests and checks")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        # No arguments provided, show help
        parser.print_help()
        return 1
    
    results = []
    
    print("🚀 Apigee Proxy Factory Test Runner")
    print(f"Python: {sys.version}")
    print(f"Working Directory: {Path.cwd()}")
    
    if args.all or args.unit:
        success = run_unit_tests()
        results.append(("Unit Tests", success))
    
    if args.all or args.services:
        success = run_service_tests()
        results.append(("Service Tests", success))
    
    if args.all or args.api:
        success = run_api_tests()
        results.append(("API Tests", success))
    
    if args.all or args.integration:
        success = run_integration_tests()
        results.append(("Integration Tests", success))
    
    if args.all or args.performance:
        success = run_performance_tests()
        results.append(("Performance Tests", success))
    
    if args.all or args.phases:
        phase_results = run_phase_tests()
        results.extend(phase_results)
    
    if args.all or args.lint:
        lint_results = run_lint_checks()
        results.extend(lint_results)
    
    if args.all or args.coverage:
        success = run_coverage_tests()
        results.append(("Coverage Tests", success))
    
    if args.all or args.report:
        success = generate_test_report()
        results.append(("Test Report Generation", success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST EXECUTION SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    failed = 0
    skipped = 0
    
    for test_name, result in results:
        if result is True:
            print(f"✅ {test_name}")
            passed += 1
        elif result is False:
            print(f"❌ {test_name}")
            failed += 1
        else:
            print(f"⚠️  {test_name} (skipped)")
            skipped += 1
    
    total = passed + failed + skipped
    print(f"\nTotal: {total}, Passed: {passed}, Failed: {failed}, Skipped: {skipped}")
    
    if failed > 0:
        print(f"\n❌ {failed} test suite(s) failed!")
        return 1
    elif passed > 0:
        print(f"\n🎉 All {passed} test suite(s) passed!")
        return 0
    else:
        print(f"\n⚠️  No tests were executed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
