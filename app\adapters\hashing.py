"""
Hashing helpers
- SHA-256 for bytes and files
- Deterministic UUIDv5 generator from arbitrary parts (useful for idempotency keys)
"""

from __future__ import annotations

import hashlib
import uuid
from pathlib import Path
from typing import Iterable


def sha256_bytes(data: bytes) -> str:
    return hashlib.sha256(data).hexdigest()


def sha256_file(path: str | Path, *, chunk_size: int = 1024 * 1024) -> str:
    h = hashlib.sha256()
    with open(path, "rb") as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            h.update(chunk)
    return h.hexdigest()


def uuid5_from_parts(*parts: str, namespace: uuid.UUID = uuid.NAMESPACE_URL) -> str:
    """
    Deterministic UUIDv5 from multiple string parts.
    Example:
        uuid5_from_parts("apiproxy", "payments-orders-v1", "rev1")
    """
    name = "|".join(p.strip() for p in parts if p is not None)
    return str(uuid.uuid5(namespace, name))
