from __future__ import annotations

import time
from typing import Dict, Optional
from urllib.parse import urlparse

import httpx
from fastapi import APIRouter
from pydantic import BaseModel

from ....core.settings import settings

router = APIRouter()

# ---------- Models ----------

class CheckResult(BaseModel):
    status: str                  # "ok" | "fail" | "skip"
    latency_ms: Optional[int] = None
    error: Optional[str] = None

class ReadyResponse(BaseModel):
    status: str                  # "ready" | "degraded" | "fail"
    checks: Dict[str, CheckResult]
    version: str = "0.1.0"       # update if you bump app version


# ---------- Helpers ----------

async def _check_apigee_mgmt(timeout: float = 2.0) -> CheckResult:
    """
    Reachability check to Apigee Edge Management base.
    Treat 2xx/3xx/401/403/404 as 'ok' (service reachable).
    Only '5xx' or network errors => 'fail'.
    """
    url = settings.mgmt.base_url.rstrip("/")  # e.g., https://api.enterprise.apigee.com/v1
    start = time.perf_counter()
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url)
        latency = int((time.perf_counter() - start) * 1000)
        if resp.status_code >= 500:
            return CheckResult(status="fail", latency_ms=latency, error=f"status {resp.status_code}")
        return CheckResult(status="ok", latency_ms=latency)
    except Exception as e:
        latency = int((time.perf_counter() - start) * 1000)
        return CheckResult(status="fail", latency_ms=latency, error=str(e))


async def _check_gitlab(timeout: float = 2.0) -> CheckResult:
    """
    If a GitLab host can be derived from settings.git.default_repo, hit /api/v4/version.
    If not configured, we 'skip' (not a hard failure).
    """
    repo = str(settings.git.default_repo or "").strip()
    if not repo:
        return CheckResult(status="skip", error="git.default_repo not set")

    try:
        parsed = urlparse(repo)
        host = parsed.netloc
        if not host:
            return CheckResult(status="skip", error="git host not found in repo URL")
        url = f"https://{host}/api/v4/version"
    except Exception as e:
        return CheckResult(status="skip", error=f"parse error: {e}")

    start = time.perf_counter()
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            resp = await client.get(url)
        latency = int((time.perf_counter() - start) * 1000)
        if resp.status_code >= 500:
            return CheckResult(status="fail", latency_ms=latency, error=f"status {resp.status_code}")
        return CheckResult(status="ok", latency_ms=latency)
    except Exception as e:
        latency = int((time.perf_counter() - start) * 1000)
        return CheckResult(status="fail", latency_ms=latency, error=str(e))


def _overall_status(checks: Dict[str, CheckResult]) -> str:
    if any(c.status == "fail" for c in checks.values()):
        return "fail"
    if all(c.status == "ok" for c in checks.values() if c.status != "skip"):
        return "ready"
    # Any mix of ok/skip without fail
    return "degraded"


# ---------- Route ----------

@router.get("", response_model=ReadyResponse, summary="Readiness probe")
async def readiness():
    apigee = await _check_apigee_mgmt()
    gitlab = await _check_gitlab()

    checks = {"apigee_mgmt": apigee, "gitlab": gitlab}
    return ReadyResponse(status=_overall_status(checks), checks=checks)
