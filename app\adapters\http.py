"""
HTTP adapter utilities
- Factory helpers for configured httpx clients (sync/async)
- Retry wrappers with exponential backoff (for transient 429/5xx)
- Small header utilities

Note: Services like ApigeeClient already implement their own retry loops.
These helpers are provided for reuse in other outbound calls.
"""

from __future__ import annotations

import time
from typing import Any, Dict, Iterable, Mapping, Optional, Tuple

import httpx

from ..core.settings import settings


DEFAULT_TIMEOUT_S = settings.http_timeout_s
RETRY_TOTAL = settings.mgmt.retries.total
RETRY_BACKOFF = settings.mgmt.retries.backoff_factor
TRANSIENT_CODES = {429, 500, 502, 503, 504}


def new_client(
    *,
    timeout_s: Optional[float] = None,
    base_headers: Optional[Mapping[str, str]] = None,
    verify: bool = True,
) -> httpx.Client:
    """
    Build a configured synchronous client.
    """
    return httpx.Client(
        timeout=timeout_s or DEFAULT_TIMEOUT_S,
        headers=dict(base_headers or {}),
        verify=verify,
    )


def new_async_client(
    *,
    timeout_s: Optional[float] = None,
    base_headers: Optional[Mapping[str, str]] = None,
    verify: bool = True,
) -> httpx.AsyncClient:
    """
    Build a configured asynchronous client.
    """
    return httpx.AsyncClient(
        timeout=timeout_s or DEFAULT_TIMEOUT_S,
        headers=dict(base_headers or {}),
        verify=verify,
    )


def merge_headers(*parts: Optional[Mapping[str, str]]) -> Dict[str, str]:
    """
    Merge multiple header dicts, skipping None.
    Later dicts override earlier ones.
    """
    out: Dict[str, str] = {}
    for p in parts:
        if not p:
            continue
        for k, v in p.items():
            if v is None:
                continue
            out[k] = v
    return out


def request_with_retries(
    client: httpx.Client,
    method: str,
    url: str,
    *,
    params: Optional[Mapping[str, Any]] = None,
    headers: Optional[Mapping[str, str]] = None,
    content: Any = None,
    json: Any = None,
    files: Any = None,
    allowed_status: Iterable[int] = (),
    retry_total: int = RETRY_TOTAL,
    backoff_factor: float = RETRY_BACKOFF,
) -> httpx.Response:
    """
    Sync request with simple retry policy.
    Retries on network errors and TRANSIENT_CODES unless status is in allowed_status.
    """
    allowed = set(allowed_status or [])
    for attempt in range(0, retry_total + 1):
        try:
            resp = client.request(
                method,
                url,
                params=params,
                headers=headers,
                content=content,
                json=json,
                files=files,
            )
        except httpx.RequestError:
            if attempt >= retry_total:
                raise
            _sleep_with_backoff(backoff_factor, attempt)
            continue

        if resp.status_code in allowed:
            return resp

        if resp.status_code in TRANSIENT_CODES and attempt < retry_total:
            _sleep_with_backoff(backoff_factor, attempt)
            continue

        return resp
    # Unreachable
    return resp  # type: ignore[UnboundLocalVariable]


async def arequest_with_retries(
    client: httpx.AsyncClient,
    method: str,
    url: str,
    *,
    params: Optional[Mapping[str, Any]] = None,
    headers: Optional[Mapping[str, str]] = None,
    content: Any = None,
    json: Any = None,
    files: Any = None,
    allowed_status: Iterable[int] = (),
    retry_total: int = RETRY_TOTAL,
    backoff_factor: float = RETRY_BACKOFF,
) -> httpx.Response:
    """
    Async request with simple retry policy.
    """
    allowed = set(allowed_status or [])
    for attempt in range(0, retry_total + 1):
        try:
            resp = await client.request(
                method,
                url,
                params=params,
                headers=headers,
                content=content,
                json=json,
                files=files,
            )
        except httpx.RequestError:
            if attempt >= retry_total:
                raise
            _sleep_with_backoff(backoff_factor, attempt)
            continue

        if resp.status_code in allowed:
            return resp

        if resp.status_code in TRANSIENT_CODES and attempt < retry_total:
            _sleep_with_backoff(backoff_factor, attempt)
            continue

        return resp
    # Unreachable
    return resp  # type: ignore[UnboundLocalVariable]


def _sleep_with_backoff(backoff_factor: float, attempt: int) -> None:
    delay = (backoff_factor * (2**attempt)) + (0.001 * attempt)
    time.sleep(min(delay, 5.0))

def sleep_with_backoff(backoff_factor: float, attempt: int) -> None:
    """
    Public wrapper so other modules can reuse standard backoff sleeping.
    """
    _sleep_with_backoff(backoff_factor, attempt)