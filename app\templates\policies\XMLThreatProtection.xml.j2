<XMLThreatProtection name="{{ name }}">
  {% if params.get('display_name') %}<DisplayName>{{ params.display_name }}</DisplayName>{% endif %}
  {% if params.get('source') %}<Source>{{ params.source }}</Source>{% endif %}

  {# Map old parameter names to new ones for backward compatibility #}
  {% set node_depth = params.get('node_depth') or params.get('max_depth') %}
  {% set attribute_count = params.get('attribute_count_per_element') or params.get('max_attributes') %}
  {% set child_count = params.get('child_count') or params.get('max_children') %}
  {% set namespace_count = params.get('namespace_count_per_element') %}

  {% if node_depth is not none or attribute_count is not none or namespace_count is not none or child_count is not none %}
  <StructureLimits>
    {% if node_depth is not none %}<NodeDepth>{{ node_depth }}</NodeDepth>{% endif %}
    {% if attribute_count is not none %}<AttributeCountPerElement>{{ attribute_count }}</AttributeCountPerElement>{% endif %}
    {% if namespace_count is not none %}<NamespaceCountPerElement>{{ namespace_count }}</NamespaceCountPerElement>{% endif %}
    {% if child_count is not none %}<ChildCount>{{ child_count }}</ChildCount>{% endif %}
  </StructureLimits>
  {% endif %}

  {# Map old parameter names for value limits #}
  {% set text_length = params.get('text_value_length') or params.get('max_text_length') %}
  {% set attr_value_length = params.get('attribute_value_length') or params.get('max_attribute_length') %}
  {% set namespace_uri_length = params.get('namespace_uri_length') %}
  {% set comment_length = params.get('comment_length') %}
  {% set pi_data_length = params.get('processing_instruction_data_length') %}

  {% if text_length is not none or attr_value_length is not none or namespace_uri_length is not none or comment_length is not none or pi_data_length is not none %}
  <ValueLimits>
    {% if text_length is not none %}<Text>{{ text_length }}</Text>{% endif %}
    {% if attr_value_length is not none %}<Attribute>{{ attr_value_length }}</Attribute>{% endif %}
    {% if namespace_uri_length is not none %}<NamespaceURI>{{ namespace_uri_length }}</NamespaceURI>{% endif %}
    {% if comment_length is not none %}<Comment>{{ comment_length }}</Comment>{% endif %}
    {% if pi_data_length is not none %}<ProcessingInstructionData>{{ pi_data_length }}</ProcessingInstructionData>{% endif %}
  </ValueLimits>
  {% endif %}

  {# Name limits - these don't have old equivalents #}
  {% set element_name_length = params.get('element_name_length') %}
  {% set attr_name_length = params.get('attribute_name_length') %}
  {% set namespace_prefix_length = params.get('namespace_prefix_length') %}
  {% set pi_target_length = params.get('processing_instruction_target_length') %}

  {% if element_name_length is not none or attr_name_length is not none or namespace_prefix_length is not none or pi_target_length is not none %}
  <NameLimits>
    {% if element_name_length is not none %}<Element>{{ element_name_length }}</Element>{% endif %}
    {% if attr_name_length is not none %}<Attribute>{{ attr_name_length }}</Attribute>{% endif %}
    {% if namespace_prefix_length is not none %}<NamespacePrefix>{{ namespace_prefix_length }}</NamespacePrefix>{% endif %}
    {% if pi_target_length is not none %}<ProcessingInstructionTarget>{{ pi_target_length }}</ProcessingInstructionTarget>{% endif %}
  </NameLimits>
  {% endif %}
</XMLThreatProtection>