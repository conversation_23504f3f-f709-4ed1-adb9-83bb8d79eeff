# Testing-Only Dependencies
# Minimal set for CI/CD and testing environments

# Core testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=4.1.0
pytest-xdist>=3.3.0
pytest-mock>=3.12.0
pytest-asyncio>=0.21.0
pytest-timeout>=2.2.0

# Test utilities
responses>=0.24.0
faker>=20.0.0
freezegun>=1.2.0
httpx-mock>=0.10.0

# Coverage reporting
coverage>=7.3.0

# Mocking and fixtures
factory-boy>=3.3.0

# Performance testing (lightweight)
pytest-benchmark>=4.0.0

# JSON reporting for CI/CD
pytest-json-report>=1.5.0

# Security testing
bandit>=1.7.5
safety>=2.3.0
