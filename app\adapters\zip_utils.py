"""
ZIP utilities (in-memory)
- Create ZIP from a mapping of {path: bytes/str}
- List entries in a ZIP
- Add/replace a single file inside an existing ZIP (returns new bytes)
- Extract to a directory (for debugging/local inspection)
"""

from __future__ import annotations

import io
import os
import zipfile
from pathlib import Path
from typing import Dict, Iterable, List, Tuple, Union


BytesLike = Union[bytes, bytearray, memoryview]


def create_zip(files: Dict[str, Union[str, BytesLike]]) -> bytes:
    """
    Build a ZIP archive in-memory. Paths use forward slashes.
    """
    buf = io.BytesIO()
    with zipfile.ZipFile(buf, "w", compression=zipfile.ZIP_DEFLATED) as z:
        for path, content in files.items():
            norm = _normalize_zip_path(path)
            data = content.encode("utf-8") if isinstance(content, str) else bytes(content)
            z.writestr(norm, data)
    return buf.getvalue()


def list_entries(zip_bytes: BytesLike) -> List[str]:
    with zipfile.ZipFile(io.BytesIO(bytes(zip_bytes)), "r") as z:
        return z.namelist()


def add_or_replace(zip_bytes: BytesLike, path: str, content: Union[str, BytesLike]) -> bytes:
    """
    Return a new ZIP with the given path added or replaced.
    """
    path = _normalize_zip_path(path)
    data = content.encode("utf-8") if isinstance(content, str) else bytes(content)

    src = io.BytesIO(bytes(zip_bytes))
    out = io.BytesIO()

    with zipfile.ZipFile(src, "r") as zin, zipfile.ZipFile(out, "w", compression=zipfile.ZIP_DEFLATED) as zout:
        # copy all except target path
        for item in zin.infolist():
            if item.filename == path:
                continue
            zout.writestr(item, zin.read(item.filename))
        # write replacement
        zout.writestr(path, data)

    return out.getvalue()


def extract_to_dir(zip_bytes: BytesLike, target_dir: str | Path) -> None:
    """
    Extract all contents to target_dir (created if missing).
    """
    target = Path(target_dir)
    target.mkdir(parents=True, exist_ok=True)
    with zipfile.ZipFile(io.BytesIO(bytes(zip_bytes)), "r") as z:
        z.extractall(target)


def _normalize_zip_path(p: str) -> str:
    return "/".join(Path(p).as_posix().split("/"))
