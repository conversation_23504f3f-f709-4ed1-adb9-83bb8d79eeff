"""
Exporter
--------
Exports a proxy revision as ZIP.

Usage:
    exp = Exporter(apigee_client)
    result = exp.export(api, rev, bearer)

Returns ExportResult with bytes, sha256, and size.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Optional

import structlog

from .apigee_client import ApigeeClient
from ..adapters.hashing import sha256_bytes

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class ExportResult:
    data: bytes
    sha256: str
    size: int


class Exporter:
    def __init__(self, apigee: ApigeeClient) -> None:
        self._apigee = apigee

    def export(
        self,
        api: str,
        rev: int,
        bearer: str,
    ) -> ExportResult:
        """
        Exports revision ZIP and computes sha256.
        """
        blob = self._apigee.export_revision_zip(api, rev, bearer=bearer)
        sha = sha256_bytes(blob)

        return ExportResult(data=blob, sha256=sha, size=len(blob))

