"""
FlowBuilder - Enhanced ProxyEndpoint XML Generator
================================================

Builds complete ProxyEndpoint XML for Apigee proxies with enhanced flow support.

Architecture:
- Implements clean code architecture with single responsibility principle
- Uses OpenAPI/Swagger specification for flow generation
- Integrates with PolicyBuilder's AttachPlan for policy attachment
- Supports enhanced flows: PreFlow, PostFlow, PostClientFlow, NoMatchFound
- Implements comprehensive error handling with structured logging

Key Features:
- Derives BasePath from OAS servers/basePath (fallback '/')
- Generates Flow per (path, method) with operationId or METHOD_normalizedPath
- Condition generation: proxy.pathsuffix MatchesPath "<pattern>" [AND verb]
- Enhanced flow support: PostFlow, PostClientFlow, DefaultFaultRule
- CORS preflight flow generation for OPTIONS requests
- Feature-driven flow configuration with system-level settings

Configuration Structure (New Optimized Format):
    flows:
      proxy:
        preflow:
          request: ["VK-VerifyApiKey", "SA-SpikeArrest"]
          response: ["AM-AddRespHeaders"]
        postclientflow:
          response: ["FC-PushLogsToPubSub"]
        nomatchfound:
          request: ["RF-ResourceNotFound"]

    flow_settings:
      include_verb_in_condition: true
      regex_required: false

Returns:
    str: Complete ProxyEndpoint XML ready for Apigee deployment

Raises:
    ValidationError: For configuration or OAS validation failures
"""

from __future__ import annotations

import json
import re
from dataclasses import dataclass
from typing import Any, Dict, Iterable, List, Optional, Tuple
from urllib.parse import urlparse

import yaml
from lxml import etree as ET
import structlog

from ..core.errors import ValidationError
from ..core.settings import settings
from .policy_builder import AttachPlan

log = structlog.get_logger(__name__)

# Supported HTTP methods in OAS we care about
_HTTP_METHODS = {"get", "post", "put", "delete", "patch", "head"}


@dataclass(frozen=True)
class OASModel:
    version: str  # "2.0" for Swagger, "3.x" for OAS3
    base_path: str
    paths: Dict[str, Dict[str, Any]]  # path -> method -> operation object


class FlowBuilder:
    def __init__(self) -> None:
        pass

    # ---------- Public API ----------

    def build_proxy_endpoint_xml(
        self,
        oas_content: str | bytes | Dict[str, Any],
        attach_plan: AttachPlan,
        *,
        system_cfg: Dict[str, Any],
        proxy_endpoint_name: str = "default",
        target_endpoint_name: str = "default",
        virtual_host: str = "default",
    ) -> str:
        """
        Returns a complete ProxyEndpoint XML text, ready to PUT to Apigee.

        :param oas_content: JSON/YAML content (str/bytes) or already-parsed dict
        :param attach_plan: AttachPlan from PolicyBuilder (policy names to attach)
        :param system_cfg: system YAML dict (flows options, CORS, etc.)
        """
        """
        Parse OAS content and extract flow configuration using the new optimized structure.
        Expected structure: flows.proxy.* and flow_settings.*
        """
        oas = self._parse_oas(oas_content)
        flows_cfg = system_cfg.get("flows", {}) if isinstance(system_cfg, dict) else {}

        # Expect new structure: flows.proxy.*
        if "proxy" not in flows_cfg:
            raise ValidationError(
                "Invalid system configuration: expected 'flows.proxy' structure",
                details={"available_keys": list(flows_cfg.keys())}
            )

        proxy_flows = flows_cfg.get("proxy", {})
        cors_config = flows_cfg.get("cors", {})

        # Get flow settings from dedicated section
        flow_settings = system_cfg.get("flow_settings", {})
        include_verb = bool(flow_settings.get("include_verb_in_condition", True))
        regex_required = _coalesce_bool(flow_settings.get("regex_required", False))

        cors_enabled = bool(cors_config.get("enabled", False))
        cors_policy_name = attach_plan.cors_policy if cors_enabled else None

        # Root
        root = ET.Element("ProxyEndpoint", name=proxy_endpoint_name)

        # HTTP connection
        http_conn = ET.SubElement(root, "HTTPProxyConnection")
        base_path_el = ET.SubElement(http_conn, "BasePath")
        base_path_el.text = oas.base_path
        vh = ET.SubElement(http_conn, "VirtualHost")
        vh.text = virtual_host

        # Route rule
        route = ET.SubElement(root, "RouteRule", name="default")
        target = ET.SubElement(route, "TargetEndpoint")
        target.text = target_endpoint_name

        # PreFlow
        pre = ET.SubElement(root, "PreFlow", name="PreFlow")
        pre_req = ET.SubElement(pre, "Request")
        for pname in attach_plan.preflow_request:
            _append_step(pre_req, pname)
        pre_resp = ET.SubElement(pre, "Response")
        for pname in attach_plan.preflow_response:
            _append_step(pre_resp, pname)

        # PostFlow with policies from attach plan
        post = ET.SubElement(root, "PostFlow", name="PostFlow")
        post_req = ET.SubElement(post, "Request")
        for pname in (attach_plan.postflow_request or []):
            _append_step(post_req, pname)
        post_resp = ET.SubElement(post, "Response")
        for pname in (attach_plan.postflow_response or []):
            _append_step(post_resp, pname)

        # PostClientFlow with policies from attach plan
        postclient = ET.SubElement(root, "PostClientFlow", name="PostClientFlow")
        postclient_req = ET.SubElement(postclient, "Request")
        for pname in (attach_plan.postclientflow_request or []):
            _append_step(postclient_req, pname)
        postclient_resp = ET.SubElement(postclient, "Response")
        for pname in (attach_plan.postclientflow_response or []):
            _append_step(postclient_resp, pname)

        # Flows
        flows_el = ET.SubElement(root, "Flows")
        num_flows = 0

        for path, methods in oas.paths.items():
            for m, op in methods.items():
                lm = m.lower()
                if lm not in _HTTP_METHODS:
                    continue

                flow_name = _flow_name(op, lm, path)
                cond = self._build_condition(path, method=lm, include_verb=include_verb, regex=regex_required)
                flow_el = ET.SubElement(flows_el, "Flow", name=flow_name)
                cond_el = ET.SubElement(flow_el, "Condition")
                cond_el.text = cond

                # Per-flow attachments
                req_el = ET.SubElement(flow_el, "Request")
                for pname in attach_plan.perflow_request:
                    _append_step(req_el, pname)

                resp_el = ET.SubElement(flow_el, "Response")
                for pname in attach_plan.perflow_response:
                    _append_step(resp_el, pname)

                num_flows += 1

        # OPTIONS flow for CORS (one catch-all under this basePath)
        if cors_policy_name:
            flow_el = ET.SubElement(flows_el, "Flow", name="CORS-Preflight")
            cond_el = ET.SubElement(flow_el, "Condition")
            # proxy.pathsuffix excludes BasePath; match anything under it
            cond_el.text = '(request.verb = "OPTIONS")'
            req_el = ET.SubElement(flow_el, "Request")
            _append_step(req_el, cors_policy_name)
            ET.SubElement(flow_el, "Response")

        # Add NoMatchFound flow if configured
        nomatch_policies = (attach_plan.nomatchfound_request or []) + (attach_plan.nomatchfound_response or [])
        if nomatch_policies:
            default_fault = ET.SubElement(root, "DefaultFaultRule", name="NoMatchFound")
            for pname in (attach_plan.nomatchfound_request or []):
                step = ET.SubElement(default_fault, "Step")
                name_el = ET.SubElement(step, "Name")
                name_el.text = pname
                # Add condition to exclude OPTIONS requests
                if pname == "RF-ResourceNotFound":
                    cond_el = ET.SubElement(step, "Condition")
                    cond_el.text = 'request.verb != "OPTIONS"'

        if num_flows == 0 and not cors_policy_name:
            raise ValidationError("OpenAPI contains no usable paths/methods")

        xml_bytes = ET.tostring(root, encoding="utf-8", xml_declaration=False, pretty_print=True)
        xml = xml_bytes.decode("utf-8").strip()
        log.info(
            "proxy_endpoint_built",
            base_path=oas.base_path,
            flows=num_flows,
            includeVerb=include_verb,
            regexRequired=regex_required,
            cors=bool(cors_policy_name),
            postflow_policies=len((attach_plan.postflow_request or []) + (attach_plan.postflow_response or [])),
            postclientflow_policies=len((attach_plan.postclientflow_request or []) + (attach_plan.postclientflow_response or [])),
            nomatchfound_policies=len(nomatch_policies),
        )
        return xml

    # ---------- Internals ----------

    def _parse_oas(self, content: str | bytes | Dict[str, Any]) -> OASModel:
        if isinstance(content, dict):
            data = content
        else:
            text = content.decode("utf-8") if isinstance(content, (bytes, bytearray)) else str(content)
            # Try JSON first, fallback to YAML
            try:
                data = json.loads(text)
            except json.JSONDecodeError:
                data = yaml.safe_load(text)

        if not isinstance(data, dict):
            raise ValidationError("Invalid OpenAPI input — expected object")

        if "openapi" in data:
            version = str(data.get("openapi"))
            base_path = _basepath_from_servers(data.get("servers"))
            paths = data.get("paths") or {}
        elif "swagger" in data:
            version = str(data.get("swagger"))
            base_path = str(data.get("basePath") or "/")
            paths = data.get("paths") or {}
        else:
            raise ValidationError("Unrecognized OpenAPI/Swagger document")

        if not base_path.startswith("/"):
            # Extract path part if someone passed a full URL
            try:
                base_path = urlparse(base_path).path or "/"
            except Exception:
                base_path = "/"

        # Normalize paths dict: ensure dict of dicts
        if not isinstance(paths, dict):
            raise ValidationError("OpenAPI 'paths' must be an object")

        return OASModel(version=version, base_path=_normalize_basepath(base_path), paths=paths)

    def _build_condition(self, path: str, *, method: str, include_verb: bool, regex: Optional[bool]) -> str:
        """
        Build an Apigee Flow condition string.

        Strategy:
        - Always express path condition using MatchesPath on proxy.pathsuffix
          since suffix excludes BasePath.
        - If regex=True we still use MatchesPath with wildcards (safe/default).
        - Include verb clause if requested.
        """
        pattern = _path_to_match_pattern(path)
        pieces = [f'(proxy.pathsuffix MatchesPath "{pattern}")']
        if include_verb:
            pieces.append(f'(request.verb = "{method.upper()}")')
        return " and ".join(pieces)


# -------------- helpers --------------

def _normalize_basepath(base_path: str) -> str:
    if not base_path:
        return "/"
    base = base_path.strip()
    if not base.startswith("/"):
        base = "/" + base
    # No trailing slash unless root
    if base != "/" and base.endswith("/"):
        base = base[:-1]
    return base


def _basepath_from_servers(servers: Any) -> str:
    """
    OAS3: servers: [ { url: "https://api.example.com/v1" }, ... ]
    We extract the path portion (/v1). Fallback '/'.
    """
    if not servers or not isinstance(servers, list):
        return "/"
    url = servers[0].get("url") if isinstance(servers[0], dict) else None
    if not url:
        return "/"
    try:
        path = urlparse(url).path
        return path or "/"
    except Exception:
        return "/"


def _flow_name(op: Dict[str, Any], method: str, path: str) -> str:
    op_id = (op or {}).get("operationId")
    if op_id and isinstance(op_id, str) and op_id.strip():
        return op_id.strip()
    # Fallback: METHOD_normalizedPath
    norm = re.sub(r"[^a-zA-Z0-9]+", "_", path.strip("/")) or "root"
    return f"{method.upper()}_{norm}"


def _path_to_match_pattern(path: str) -> str:
    """
    Convert OAS templated path to Apigee MatchesPath pattern.
    Examples:
      /orders/{id}          -> /orders/*
      /users/{uid}/roles    -> /users/*/roles
      /reports/{year}/{m?}  -> /reports/*/*  (best-effort)
      /                      -> /*
    """
    if not path or path.strip() == "/":
        return "/*"
    segs = [s for s in path.split("/") if s]
    out: List[str] = []
    for s in segs:
        if s.startswith("{") and s.endswith("}"):
            out.append("*")
        else:
            out.append(s)
    return "/" + "/".join(out)


def _append_step(parent: ET._Element, policy_name: str) -> None:
    step = ET.SubElement(parent, "Step")
    name = ET.SubElement(step, "Name")
    name.text = policy_name


def _coalesce_bool(val: Any) -> Optional[bool]:
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        v = val.strip().lower()
        if v in {"true", "yes", "1"}:
            return True
        if v in {"false", "no", "0"}:
            return False
    return None
