"""
AuthService
-----------
Exchanges an Apigee Hybrid Proxy passcode for a Management API access token.

Notes
- The Apigee Proxy endpoint is derived from Settings.mgmt.apigee_token_url.
- If client_id/client_secret are configured (recommended), they are sent as HTTP headers.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Optional
import uuid

import httpx
import structlog

from ..core.errors import AuthError, UpstreamError, ValidationError
from ..core.settings import settings
from ..adapters.http import sleep_with_backoff 

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class AccessToken:
    token: str
    token_type: str
    expires_in: int
    refresh_token: Optional[str] = None


class AuthService:
    def __init__(
        self,
    
    ) -> None:
        self._token_url = settings.mgmt.apigee_token_url
        self._timeout =  settings.mgmt.timeout_s

        # Client-specific configuration variables
        self._client_id =  settings.mgmt.client_id
        self._uuid = str(uuid.uuid4())
        self._x_channel_identifier = settings.mgmt.x_channel_identifier
       

        # Ephemeral client (service lifetime). Callers may pass a custom client for testing.
        self._http = httpx.Client(timeout=self._timeout)

    def close(self) -> None:
        try:
            self._http.close()
        except Exception:  # pragma: no cover
            pass

    def exchange_passcode(self, passcode: str) -> AccessToken:
        """
        Exchange one-time passcode for bearer token.

        Raises:
            ValidationError: blank/invalid passcode
            AuthError: unauthorized/forbidden
            UpstreamError: network/server errors
        """
        # Validate passcode input
        code = (passcode or "").strip()
        if not code:
            raise ValidationError("Passcode cannot be blank")

        # Prepare headers and payload for the Apigee Proxy call
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {settings.mgmt.basic_auth}",
            "client_id": self._client_id,
            "uuid": self._uuid,
            "x-channel-identifier": self._x_channel_identifier,
            "x-fapi-interaction-id": self._uuid,  # Using UUID as interaction ID
        }
        log.info(f"client_headers: {headers}")

        data = {
            "grant_type": "password",
            "passcode": code,
            "response_type": "token",
        }

        # Minimal, explicit retry loop for transient 429/5xx
        max_retries = settings.mgmt.retries.total
        backoff = settings.mgmt.retries.backoff_factor

        for attempt in range(0, max_retries + 1):
            try:
                resp = self._http.post(self._token_url, headers=headers, data=data)
            except httpx.RequestError as e:
                if attempt >= max_retries:
                    raise UpstreamError(
                        "Failed to reach Apigee token endpoint",
                        details={"url": self._token_url, "error": str(e)},
                    ) from e
                sleep_with_backoff(backoff, attempt)
                continue

            # Handle various HTTP response statuses
            if resp.status_code in (401, 403):
                detail = _safe_json(resp)
                raise AuthError(
                    "Unauthorized exchanging passcode for token",
                    details={"status": resp.status_code, "body": detail},
                )

            if resp.status_code >= 500:
                if attempt >= max_retries:
                    raise UpstreamError(
                        "Apigee token service error",
                        details={
                            "status": resp.status_code,
                            "body": _safe_json(resp),
                        },
                    )
                sleep_with_backoff(backoff, attempt)
                continue

            if resp.status_code >= 400:
                raise ValidationError(
                    "Token exchange failed",
                    details={
                        "status": resp.status_code,
                        "body": _safe_json(resp),
                    },
                )

            # Successfully parse the response payload
            body = resp.json()
            token = AccessToken(
                token=body.get("access_token", ""),
                token_type=body.get("token_type", "Bearer").capitalize(),
                expires_in=int(body.get("expires_in", 1800)),
                refresh_token=body.get("refresh_token"),
            )
            if not token.token:
                raise UpstreamError(
                    "Apigee token endpoint returned no access_token",
                    details={"body": body},
                )

            log.info(
                "auth_token_obtained",
                token_type=token.token_type,
                expires_in=token.expires_in,
            )
            return token

        # Should be unreachable
        raise UpstreamError("Exhausted retries while exchanging passcode")


def _safe_json(resp: httpx.Response):
    try:
        return resp.json()
    except Exception:
        return {"text": resp.text[:1000]}