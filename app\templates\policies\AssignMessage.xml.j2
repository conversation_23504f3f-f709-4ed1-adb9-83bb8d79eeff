<AssignMessage name="{{ name }}"
{% if params.enabled is defined %} enabled="{{ 'true' if params.enabled else 'false' }}"{% endif %}
{% if params.continueOnError is defined %} continueOnError="{{ 'true' if params.continueOnError else 'false' }}"{% endif %}
{% if params.async is defined %} async="{{ 'true' if params.async else 'false' }}"{% endif %}>
  <DisplayName>{{ params.get('display_name', name) }}</DisplayName>
  {% if params.get('ignore_unresolved_variables') is not none %}
  <IgnoreUnresolvedVariables>{{ 'true' if params.get('ignore_unresolved_variables') else 'false' }}</IgnoreUnresolvedVariables>
  {% endif %}

  {% if params.get('assign_to') %}
  <AssignTo createNew="{{ 'true' if params.assign_to.get('createNew', false) else 'false' }}" transport="{{ params.assign_to.get('transport', 'http') }}" type="{{ params.assign_to.get('type', 'request') }}">{{ params.assign_to.get('variable', '') }}</AssignTo>
  {% endif %}

  {% if params.get('add') %}
  <Add>
    {% if params.add.get('headers') %}
    <Headers>
      {% for h in params.add.headers %}
      <Header name="{{ h.name }}">{{ h.value }}</Header>
      {% endfor %}
    </Headers>
    {% endif %}
    {% if params.add.get('query_params') %}
    <QueryParams>
      {% for qp in params.add.query_params %}
      <QueryParam name="{{ qp.name }}">{{ qp.value }}</QueryParam>
      {% endfor %}
    </QueryParams>
    {% endif %}
    {% if params.add.get('form_params') %}
    <FormParams>
      {% for fp in params.add.form_params %}
      <FormParam name="{{ fp.name }}">{{ fp.value }}</FormParam>
      {% endfor %}
    </FormParams>
    {% endif %}
  </Add>
  {% endif %}

  {% if params.get('copy') %}
  <Copy source="{{ params.copy.get('source', 'request') }}">
    {% if params.copy.get('headers') %}
    <Headers>
      {% for h in params.copy.headers %}
      <Header name="{{ h.name }}">{{ h.get('from', h.name) }}</Header>
      {% endfor %}
    </Headers>
    {% endif %}
    {% if params.copy.get('query_params') %}
    <QueryParams>
      {% for qp in params.copy.query_params %}
      <QueryParam name="{{ qp.name }}">{{ qp.get('from', qp.name) }}</QueryParam>
      {% endfor %}
    </QueryParams>
    {% endif %}
    {% if params.copy.get('form_params') %}
    <FormParams>
      {% for fp in params.copy.form_params %}
      <FormParam name="{{ fp.name }}">{{ fp.get('from', fp.name) }}</FormParam>
      {% endfor %}
    </FormParams>
    {% endif %}
    {% if params.copy.get('payload') is not none %}
    <Payload>{{ 'true' if params.copy.payload else 'false' }}</Payload>
    {% endif %}
    {% if params.copy.get('path') is not none %}
    <Path>{{ 'true' if params.copy.path else 'false' }}</Path>
    {% endif %}
    {% if params.copy.get('verb') is not none %}
    <Verb>{{ 'true' if params.copy.verb else 'false' }}</Verb>
    {% endif %}
    {% if params.copy.get('version') is not none %}
    <Version>{{ 'true' if params.copy.version else 'false' }}</Version>
    {% endif %}
  </Copy>
  {% endif %}

  {% if params.get('remove') %}
  <Remove>
    {% if params.remove.get('headers') %}
    <Headers>
      {% for h in params.remove.headers %}
      <Header name="{{ h.name if h.name is defined else h }}"></Header>
      {% endfor %}
    </Headers>
    {% endif %}
    {% if params.remove.get('query_params') %}
    <QueryParams>
      {% for qp in params.remove.query_params %}
      <QueryParam name="{{ qp.name if qp.name is defined else qp }}"></QueryParam>
      {% endfor %}
    </QueryParams>
    {% endif %}
    {% if params.remove.get('form_params') %}
    <FormParams>
      {% for fp in params.remove.form_params %}
      <FormParam name="{{ fp.name if fp.name is defined else fp }}"></FormParam>
      {% endfor %}
    </FormParams>
    {% endif %}
    {% if params.remove.get('payload') is not none %}
    <Payload>{{ 'true' if params.remove.payload else 'false' }}</Payload>
    {% endif %}
  </Remove>
  {% endif %}

  {% if params.get('set') %}
  <Set>
    {% if params.set.get('headers') %}
    <Headers>
      {% for h in params.set.headers %}
      <Header name="{{ h.name }}">{{ h.value }}</Header>
      {% endfor %}
    </Headers>
    {% endif %}
    {% if params.set.get('query_params') %}
    <QueryParams>
      {% for qp in params.set.query_params %}
      <QueryParam name="{{ qp.name }}">{{ qp.value }}</QueryParam>
      {% endfor %}
    </QueryParams>
    {% endif %}
    {% if params.set.get('form_params') %}
    <FormParams>
      {% for fp in params.set.form_params %}
      <FormParam name="{{ fp.name }}">{{ fp.value }}</FormParam>
      {% endfor %}
    </FormParams>
    {% endif %}
    {% if params.set.get('payload') is not none %}
    <Payload{% if params.set.get('content_type') %} contentType="{{ params.set.content_type }}"{% endif %}>{{ params.set.payload }}</Payload>
    {% endif %}
    {% if params.set.get('path') is not none %}
    <Path>{{ params.set.path }}</Path>
    {% endif %}
    {% if params.set.get('verb') is not none %}
    <Verb>{{ params.set.verb }}</Verb>
    {% endif %}
    {% if params.set.get('version') is not none %}
    <Version>{{ params.set.version }}</Version>
    {% endif %}
    {% if params.set.get('status_code') is not none %}
    <StatusCode>{{ params.set.status_code }}</StatusCode>
    {% endif %}
    {% if params.set.get('reason_phrase') is not none %}
    <ReasonPhrase>{{ params.set.reason_phrase }}</ReasonPhrase>
    {% endif %}
  </Set>
  {% endif %}
</AssignMessage>
