<XSL name="{{ name }}">
  {% if params.resource_url %}<ResourceURL>{{ params.resource_url }}</ResourceURL>{% endif %} <!-- e.g., xsl://transform/my.xsl -->
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}                   <!-- variable or content -->
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.parameters %}
  <Parameters>
    {% for p in params.parameters %}
    <Parameter name="{{ p.name }}">{{ p.value }}</Parameter>
    {% endfor %}
  </Parameters>
  {% endif %}
</XSL>