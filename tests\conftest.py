"""
Pytest configuration and fixtures
=================================
Global test configuration, fixtures, and utilities for the test suite.
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch
import yaml
import json

# Test markers
pytest_plugins = []


def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "external: mark test as requiring external services"
    )


@pytest.fixture(scope="session")
def test_config_dir():
    """Create a temporary directory with test configuration files"""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_dir = Path(temp_dir)
        
        # Create directory structure
        (config_dir / "systems").mkdir()
        (config_dir / "env" / "test").mkdir(parents=True)
        (config_dir / "env" / "prod").mkdir(parents=True)
        
        # Create test system configuration
        test_system_config = {
            "metadata": {
                "system": "test-system",
                "owner_team": "test-team",
                "description": "Test system configuration"
            },
            "policy_defaults": {
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "100pm"},
                "Quota": {"allow": "1000pm", "timeunit": "minute"}
            },
            "flows": {
                "preflow": {
                    "request": ["VerifyApiKey", "SpikeArrest"],
                    "response": []
                },
                "per_operation": {
                    "request": ["Quota"],
                    "response": []
                },
                "cors": {
                    "enabled": True,
                    "policy": "CORS"
                }
            },
            "target": {
                "baseurl": "https://api.test.com",
                "connect_timeout_ms": 5000,
                "read_timeout_ms": 30000
            },
            "features": {
                "advanced_security": True,
                "caching_enabled": False
            }
        }
        
        with open(config_dir / "systems" / "test-system.yaml", 'w') as f:
            yaml.dump(test_system_config, f)
        
        # Create environment overrides
        prod_overrides = {
            "policy_defaults": {
                "SpikeArrest": {"rate": "50pm"},
                "Quota": {"allow": "500pm"}
            },
            "target": {
                "baseurl": "https://api.prod.com"
            }
        }
        
        with open(config_dir / "env" / "prod" / "test-system.yaml", 'w') as f:
            yaml.dump(prod_overrides, f)
        
        # Create feature flags
        feature_flags = {
            "advanced_security": False,
            "json_threat_protection": {
                "enabled": True,
                "conditions": [
                    {
                        "type": "context",
                        "key": "content_type",
                        "operator": "in",
                        "value": ["application/json"]
                    }
                ]
            },
            "smart_caching": {
                "enabled": True,
                "percentage": 50,
                "environments": {
                    "dev": True,
                    "test": True,
                    "prod": False
                }
            }
        }
        
        with open(config_dir / "feature_flags.yaml", 'w') as f:
            yaml.dump(feature_flags, f)
        
        yield config_dir


@pytest.fixture
def sample_system_config():
    """Sample system configuration for testing"""
    return {
        "metadata": {
            "system": "test-system",
            "owner_team": "test-team",
            "description": "Test system"
        },
        "policy_defaults": {
            "VerifyApiKey": {"keyref": "request.header.x-api-key"},
            "SpikeArrest": {"rate": "100pm"},
            "Quota": {"allow": "1000pm", "timeunit": "minute"}
        },
        "flows": {
            "preflow": {
                "request": ["VerifyApiKey", "SpikeArrest"],
                "response": []
            },
            "per_operation": {
                "request": ["Quota"],
                "response": []
            }
        },
        "target": {
            "baseurl": "https://api.example.com"
        },
        "features": {
            "advanced_security": True
        }
    }


@pytest.fixture
def sample_swagger_spec():
    """Sample OpenAPI/Swagger specification for testing"""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Test API",
            "version": "1.0.0",
            "description": "Test API specification"
        },
        "servers": [
            {"url": "https://api.example.com/v1"}
        ],
        "paths": {
            "/users": {
                "get": {
                    "summary": "Get users",
                    "responses": {
                        "200": {
                            "description": "Success",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {"type": "object"}
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "summary": "Create user",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "schema": {"type": "object"}
                            }
                        }
                    },
                    "responses": {
                        "201": {"description": "Created"}
                    }
                }
            },
            "/users/{id}": {
                "get": {
                    "summary": "Get user by ID",
                    "parameters": [
                        {
                            "name": "id",
                            "in": "path",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {"description": "Success"}
                    }
                }
            }
        }
    }


@pytest.fixture
def mock_apigee_client():
    """Mock Apigee client for testing"""
    mock_client = Mock()
    
    # Mock common methods
    mock_client.import_proxy_zip.return_value = {"revision": 1}
    mock_client.export_revision_zip.return_value = b"mock-zip-content"
    mock_client.get_proxy.return_value = {"name": "test-proxy"}
    mock_client.list_proxies.return_value = ["proxy1", "proxy2"]
    
    return mock_client


@pytest.fixture
def mock_auth_service():
    """Mock authentication service for testing"""
    mock_service = Mock()
    
    # Mock token exchange
    mock_token = Mock()
    mock_token.token = "mock-access-token"
    mock_token.token_type = "Bearer"
    mock_token.expires_in = 3600
    
    mock_service.exchange_passcode.return_value = mock_token
    
    return mock_service


@pytest.fixture
def mock_git_service():
    """Mock Git service for testing"""
    mock_service = Mock()
    
    mock_service.push_to_gitlab.return_value = {
        "commit_id": "abc123",
        "web_url": "https://git.example.com/commit/abc123"
    }
    
    return mock_service


@pytest.fixture
def sample_policy_params():
    """Sample policy parameters for testing"""
    return {
        "VerifyApiKey": {
            "keyref": "request.header.x-api-key"
        },
        "SpikeArrest": {
            "rate": "100pm"
        },
        "Quota": {
            "allow": "1000pm",
            "timeunit": "minute"
        },
        "CORS": {
            "allow_origins": ["https://example.com"],
            "allow_methods": ["GET", "POST", "PUT"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    }


@pytest.fixture
def sample_features():
    """Sample feature flags for testing"""
    return {
        "advanced_security": True,
        "json_threat_protection": True,
        "xml_threat_protection": False,
        "smart_caching": True,
        "conditional_policies": True
    }


@pytest.fixture
def mock_template_helpers():
    """Mock template helpers for testing"""
    mock_helpers = Mock()
    
    # Mock validation methods
    mock_helpers.is_valid_rate.side_effect = lambda rate: rate.endswith(('pm', 'ps', 'ph', 'pd'))
    mock_helpers.is_valid_url.side_effect = lambda url: url.startswith(('http://', 'https://'))
    mock_helpers.is_valid_header_name.side_effect = lambda name: '-' not in name or name.replace('-', '').replace('_', '').isalnum()
    mock_helpers.is_valid_json_path.side_effect = lambda path: path.startswith('$.')
    
    # Mock utility methods
    mock_helpers.safe_default.side_effect = lambda value, default, validator=None: value if not validator or validator(value) else default
    mock_helpers.normalize_list.side_effect = lambda value, sep=',': value.split(sep) if isinstance(value, str) else value
    mock_helpers.xml_escape.side_effect = lambda text: text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
    
    # Mock environment methods
    mock_helpers.is_production.return_value = False
    mock_helpers.is_development.return_value = True
    mock_helpers.feature_enabled.return_value = True
    
    return mock_helpers


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state between tests"""
    # Reset any global caches or singletons
    with patch('app.services.feature_flags._global_evaluator', None):
        with patch('app.services.conditional_policies._global_attacher', None):
            yield


@pytest.fixture
def temp_template_dir():
    """Create temporary directory with test templates"""
    with tempfile.TemporaryDirectory() as temp_dir:
        template_dir = Path(temp_dir)
        
        # Create sample templates
        spike_arrest_template = '''
<SpikeArrest name="{{ name }}">
    <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
    <Rate>{{ params.rate | default('100pm') }}</Rate>
</SpikeArrest>
        '''.strip()
        
        quota_template = '''
<Quota name="{{ name }}">
    <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
    <Allow>{{ params.allow | default('1000pm') }}</Allow>
    <Interval>{{ params.interval | default(1) }}</Interval>
    <TimeUnit>{{ params.timeunit | default('minute') }}</TimeUnit>
</Quota>
        '''.strip()
        
        (template_dir / "SpikeArrest.xml.j2").write_text(spike_arrest_template)
        (template_dir / "Quota.xml.j2").write_text(quota_template)
        
        yield template_dir


@pytest.fixture
def mock_orchestrator_result():
    """Mock orchestrator result for testing"""
    result = Mock()
    result.proxy_name = "test-proxy"
    result.revision = 1
    result.org = "test-org"
    result.env = "test-env"
    result.bundle_sha256 = "abc123def456"
    result.export_size = 2048
    result.git_commit_id = "commit123"
    result.git_web_url = "https://git.example.com/commit/123"
    
    return result


# Test utilities
class TestUtils:
    """Utility functions for tests"""
    
    @staticmethod
    def create_temp_file(content: str, suffix: str = ".tmp") -> Path:
        """Create a temporary file with given content"""
        with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
            f.write(content)
            return Path(f.name)
    
    @staticmethod
    def create_temp_yaml_file(data: dict) -> Path:
        """Create a temporary YAML file with given data"""
        content = yaml.dump(data)
        return TestUtils.create_temp_file(content, ".yaml")
    
    @staticmethod
    def create_temp_json_file(data: dict) -> Path:
        """Create a temporary JSON file with given data"""
        content = json.dumps(data, indent=2)
        return TestUtils.create_temp_file(content, ".json")


@pytest.fixture
def test_utils():
    """Test utilities fixture"""
    return TestUtils


# Performance testing utilities
@pytest.fixture
def performance_threshold():
    """Performance thresholds for tests"""
    return {
        "policy_rendering": 0.1,  # 100ms
        "config_validation": 0.05,  # 50ms
        "feature_flag_evaluation": 0.01,  # 10ms
        "template_rendering": 0.02,  # 20ms
    }


# Environment setup
@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables"""
    test_env_vars = {
        "APIGEE_ENV": "test",
        "LOG_LEVEL": "DEBUG",
        "TESTING": "true"
    }
    
    original_env = {}
    for key, value in test_env_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original environment
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value
