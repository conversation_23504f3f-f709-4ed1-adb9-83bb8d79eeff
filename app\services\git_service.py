"""
GitService (GitLab)
-------------------
Commits the exported ZIP into a GitLab repository using the GitLab REST API
(Create Commit with actions). No git binaries required.

- Derives API base as: https://<gitlab-host>/api/v4
- Project ID is the URL-encoded repo path (strip .git), e.g. "group/subgroup/repo"

Default file path logic:
- Attempts to parse `commit_message` like "feat: bootstrap <proxyName>@rev<rev>"
  and uses "bundles/<proxyName>/rev-<rev>.zip".
- If parsing fails, falls back to "bundles/export-<sha256>.zip".

Usage:
    gs = GitService()
    commit_id, web_url = gs.push_zip(zip_bytes, repo_url, branch, message, file_path=None)
"""

from __future__ import annotations

import base64
import re
from dataclasses import dataclass
from typing import Optional, Tuple
from urllib.parse import urlparse, quote_plus

import zipfile
import io

import httpx
import structlog

from ..core.errors import UpstreamError, ValidationError
from ..core.settings import settings
from ..adapters.hashing import sha256_bytes

log = structlog.get_logger(__name__)


@dataclass(frozen=True)
class GitPushResult:
    commit_id: str
    web_url: Optional[str]


class GitService:
    def __init__(
        self,
        *,
        http: Optional[httpx.Client] = None,
        timeout_s: Optional[int] = None,
    ) -> None:
        self._base_url = settings.git.base_url
        self._pat = settings.git.pat.get_secret_value() if settings.git.pat else None
        if not self._pat:
            log.warning("git_pat_missing", msg="Proceeding without PAT may fail for private repos.")
        self._http = http or httpx.Client(timeout=timeout_s or 30)

        # Cache for repository files to avoid repeated API calls
        self._repo_files_cache = {}

    def push_zip_source_code(
            self,
            zip_bytes: bytes,
            project_id: str,
            branch: str,
            commit_message: str,
            *,
            file_path: Optional[str] = None,
        ) -> Tuple[str, Optional[str]]:
        """
        Creates a commit that adds/updates:
        1) The actual content of the ZIP file (files and folders) directly into the repository.
        2) The ZIP file itself inside the /bundles folder.

        Returns (commit_id, web_url).
        """
        if not project_id or not branch or not commit_message:
            raise ValidationError("project_id, branch, and commit_message are required")

        base_url = self._base_url
        headers = _auth_headers(self._pat)

        # Resolve path for ZIP file
        if not file_path:
            guessed = _guess_path_from_message(commit_message)
            file_path = guessed or f"bundles/export-{sha256_bytes(zip_bytes)[:12]}.zip"

        # Prepare list of actions for the commit
        actions = []

        # 1. Add the ZIP file to the /bundles folder
        encoded_zip = base64.b64encode(zip_bytes).decode("ascii")
        actions.append({
            "action": "create",  # Will be optimized later based on file existence
            "file_path": file_path,
            "content": encoded_zip,
            "encoding": "base64",
        })

        log.info("zip_file_action_created",
                file_path=file_path,
                zip_size=len(zip_bytes),
                encoded_size=len(encoded_zip))

        # 2. Extract the ZIP file and add its contents to the repository
        try:
            with zipfile.ZipFile(io.BytesIO(zip_bytes), "r") as zip_ref:
                for file_name in zip_ref.namelist():
                    # Skip directories (GitLab API handles files only)
                    if file_name.endswith("/"):
                        continue

                    # Read file content from the ZIP
                    try:
                        raw_content = zip_ref.read(file_name)

                        # Determine if file is binary or text
                        is_binary = self._is_binary_content(raw_content, file_name)

                        if is_binary:
                            # For binary files, use base64 encoding
                            
                            file_content = base64.b64encode(raw_content).decode("ascii")
                            encoding = "base64"
                            log.debug("binary_file_encoded",
                                     file=file_name,
                                     original_size=len(raw_content),
                                     encoded_size=len(file_content))
                        else:
                            # For text files, decode as UTF-8
                            file_content = raw_content.decode("utf-8", errors="replace")
                            encoding = "text"
                            log.debug("text_file_decoded",
                                     file=file_name,
                                     size=len(file_content))

                    except Exception as e:
                        log.warning("failed_to_read_zip_file", file=file_name, error=str(e))
                        continue

                    # Add action for each file (action will be optimized later)
                    action = {
                        "action": "create",  # Will be optimized based on file existence
                        "file_path": file_name,  # File path relative to the repository root
                        "content": file_content,
                    }

                    # Add encoding for binary files
                    if is_binary:
                        action["encoding"] = encoding

                    actions.append(action)
        except zipfile.BadZipFile:
            raise ValidationError("Invalid ZIP file provided")

        # Build commit body
        body = {
            "branch": branch,
            "commit_message": commit_message,
            "actions": actions,
        }

        # Optimize actions by checking file existence before committing
        actions = self._optimize_commit_actions(project_id, branch, actions, headers)
        body["actions"] = actions

        # Validate commit data before sending
        validation_errors = self._validate_commit_data(body)
        if validation_errors:
            log.error("gitlab_commit_validation_failed", errors=validation_errors)
            raise ValidationError(f"Invalid commit data: {'; '.join(validation_errors)}")

        # Log commit details for debugging
        log.info("gitlab_commit_attempt",
                project_id=project_id,
                branch=branch,
                actions_count=len(actions),
                commit_message=commit_message[:100] + "..." if len(commit_message) > 100 else commit_message)

        # Log action details for debugging
        for i, action in enumerate(actions[:3]):  # Log first 3 actions
            log.debug("gitlab_commit_action",
                     action_index=i,
                     action_type=action.get("action"),
                     file_path=action.get("file_path"),
                     content_length=len(action.get("content", "")),
                     encoding=action.get("encoding"))

        # Create commit via GitLab API
        url = f"{base_url}/projects/{project_id}/repository/commits"
        resp = self._http.post(url, headers=headers, json=body)

        if resp.status_code >= 400:
            error_body = _safe_json(resp)
            error_text = resp.text

            # Enhanced logging for debugging
            log.error("gitlab_commit_failed_detailed",
                     status_code=resp.status_code,
                     response_text=error_text[:1000],  # First 1000 chars
                     response_headers=dict(resp.headers),
                     project_id=project_id,
                     branch=branch,
                     actions_count=len(actions),
                     request_url=url)

            error_msg = "GitLab commit failed"

            # Provide more specific error messages
            if resp.status_code == 400:
                # Check for common GitLab commit errors
                if "A file with this name already exists" in error_text:
                    error_msg = "GitLab commit failed: Some files already exist (optimization failed)"
                elif "A file with this name doesn't exist" in error_text:
                    error_msg = "GitLab commit failed: Some files don't exist (optimization failed)"
                elif "empty repository" in error_text.lower():
                    error_msg = "GitLab commit failed: Cannot commit to empty repository without initial commit"
                elif "invalid" in error_text.lower() and "branch" in error_text.lower():
                    error_msg = f"GitLab commit failed: Invalid branch '{branch}'"
                elif "message" in error_text.lower() and ("empty" in error_text.lower() or "blank" in error_text.lower()):
                    error_msg = "GitLab commit failed: Commit message cannot be empty"
                elif "file" in error_text.lower() and "content" in error_text.lower():
                    error_msg = "GitLab commit failed: Invalid file content or encoding"
                elif "path" in error_text.lower() and "invalid" in error_text.lower():
                    error_msg = "GitLab commit failed: Invalid file path"
                else:
                    # Include actual error message from GitLab
                    error_msg = f"GitLab commit failed: {error_text[:200]}"
            elif resp.status_code == 403:
                error_msg = "GitLab commit failed: Access denied (check permissions and PAT)"
            elif resp.status_code == 404:
                error_msg = "GitLab commit failed: Project or branch not found"
            elif resp.status_code == 422:
                error_msg = f"GitLab commit failed: Validation error - {error_text[:200]}"

            raise UpstreamError(
                error_msg,
                details={
                    "status": resp.status_code,
                    "body": error_body,
                    "response_text": error_text[:500],
                    "project_id": project_id,
                    "branch": branch,
                    "actions_count": len(actions),
                    "url": url
                },
            )

        payload = resp.json()
        commit_id = payload.get("id") or payload.get("short_id")
        web_url = payload.get("web_url")
        log.info("git_commit_created", id=commit_id, url=web_url, file=file_path, branch=branch)
        return commit_id, web_url

    def create_project(
            self,
            project_name: str,
            *,
            namespace_path: str,
            visibility: str = "private",
            description: Optional[str] = None,
        ) -> dict:
        """
        Creates a new GitLab project or returns the existing project details if it already exists.
        
        Args:
            project_name (str): Name of the new project.
            namespace_path (str): GitLab namespace path (e.g., "group/subgroup").
            visibility (str): Project visibility ("private", "internal", "public").
            description (Optional[str]): Optional project description.
        
        Returns:
            dict: Project details containing the project ID and other metadata.
        """
        if not namespace_path or not project_name:
            raise ValidationError("namespace_path and project_name are required")

        log.info(f"namespace path : {namespace_path}")
        namespace_id = self.get_namespace_id_by_full_path(namespace_path)

        base_url = self._base_url
        headers = _auth_headers(self._pat)

        # Check if project already exists
        search_url = f"{base_url}/projects"
        search_params = {"search": project_name}
        search_resp = self._http.get(search_url, headers=headers, params=search_params)

        if search_resp.status_code >= 400:
            raise UpstreamError(
                "Failed to search for existing projects",
                details={"status": search_resp.status_code, "body": _safe_json(search_resp)},
            )

        existing_projects = search_resp.json()
        for project in existing_projects:
            if project.get("name") == project_name and project.get("namespace", {}).get("id") == namespace_id:
                log.info("git_project_exists", id=project.get("id"), name=project_name, url=project.get("web_url"))
                return {
                    "project_id": project.get("id"),
                    "name": project.get("name"),
                    "web_url": project.get("web_url"),
                    "namespace_path": namespace_path,
                }  # Return existing project details

        # Create project if it doesn't exist
        body = {
            "name": project_name,
            "visibility": visibility,
            "namespace_id": namespace_id
        }

        if description:
            body["description"] = description

        create_url = f"{base_url}/projects"
        create_resp = self._http.post(create_url, headers=headers, json=body)

        if create_resp.status_code >= 400:
            raise UpstreamError(
                "GitLab project creation failed",
                details={"status": create_resp.status_code, "body": _safe_json(create_resp)},
            )

        payload = create_resp.json()
        log.info("git_project_created", id=payload.get("id"), name=project_name, url=payload.get("web_url"))
        return {
            "project_id": payload.get("id"),
            "name": payload.get("name"),
            "web_url": payload.get("web_url"),
            "namespace_path": namespace_path,
        }  # Return newly created project details


    def get_namespace_id_by_full_path(self, namespace_path: str) -> int:
        """
        Searches for a namespace by its full path using the GitLab API and returns its ID.

        Args:
            host (str): GitLab host (e.g., "gitlab.example.com").
            namespace_path (str): Full namespace path (e.g., "group/subgroup/reflect").
            pat (str): Personal Access Token for authentication.

        Returns:
            int: Namespace ID if found.

        Raises:
            ValidationError: If the namespace is not found.
            UpstreamError: If the API call fails.
        """
        base_url = self._base_url
        headers = _auth_headers(self._pat)
        url = f"{base_url}/namespaces"
        params = {"search": namespace_path.split("/")[-1]}  # Use the last part of the path for fuzzy search

        resp = httpx.get(url, headers=headers, params=params)

        if resp.status_code >= 400:
            raise UpstreamError(
                "Failed to retrieve namespace ID",
                details={"status": resp.status_code, "body": _safe_json(resp)},
            )

        namespaces = resp.json()
        for namespace in namespaces:
            if namespace.get("full_path") == namespace_path:
                log.info("git_namespace_found", id=namespace.get("id"), path=namespace_path)
                return namespace.get("id")

        raise ValidationError(f"Namespace '{namespace_path}' not found on GitLab")

    def _optimize_commit_actions(self, project_id: str, branch: str, actions: list, headers: dict) -> list:
        """
        Optimizes commit actions by checking file existence and setting correct action type.

        For each file:
        - If file exists: use "update" action
        - If file doesn't exist: use "create" action

        This prevents the "file already exists" and "file doesn't exist" errors.
        """
        if not actions:
            return actions

        # Check if we should skip optimization
        if self._should_skip_optimization(actions):
            # For large commits or when all actions are updates, use "update" for all
            for action in actions:
                action["action"] = "update"
            return actions

        # Get list of files in the repository for the given branch
        existing_files = self._get_repository_files(project_id, branch, headers)

        optimized_actions = []
        for action in actions:
            file_path = action.get("file_path", "")

            # Determine correct action based on file existence
            if file_path in existing_files:
                # File exists, use update
                action["action"] = "update"
                log.debug("file_action_optimized", file=file_path, action="update", reason="file_exists")
            else:
                # File doesn't exist, use create
                action["action"] = "create"
                log.debug("file_action_optimized", file=file_path, action="create", reason="file_new")

            optimized_actions.append(action)

        log.info("commit_actions_optimized",
                total_files=len(actions),
                create_actions=sum(1 for a in optimized_actions if a["action"] == "create"),
                update_actions=sum(1 for a in optimized_actions if a["action"] == "update"))

        return optimized_actions

    def _get_repository_files(self, project_id: str, branch: str, headers: dict) -> set:
        """
        Gets a set of all file paths in the repository for the given branch.
        Uses GitLab Repository Tree API with recursive=True to get all files.
        Results are cached to avoid repeated API calls within the same session.
        """
        # Check cache first
        cache_key = f"{project_id}:{branch}"
        if cache_key in self._repo_files_cache:
            log.debug("repository_files_cache_hit", project_id=project_id, branch=branch)
            return self._repo_files_cache[cache_key]

        base_url = self._base_url
        url = f"{base_url}/projects/{project_id}/repository/tree"

        params = {
            "ref": branch,
            "recursive": True,
            "per_page": 100  # GitLab default max per page
        }

        existing_files = set()
        page = 1

        try:
            while True:
                params["page"] = page
                resp = self._http.get(url, headers=headers, params=params)

                if resp.status_code == 404:
                    # Branch doesn't exist yet, return empty set
                    log.info("branch_not_found", branch=branch, project_id=project_id)
                    existing_files = set()
                    break
                elif resp.status_code >= 400:
                    # Other error, log warning but continue with empty set
                    log.warning("failed_to_get_repository_files",
                              status=resp.status_code,
                              project_id=project_id,
                              branch=branch,
                              body=_safe_json(resp))
                    existing_files = set()
                    break

                files = resp.json()
                if not files:
                    break

                # Add file paths to set (exclude directories)
                for file_info in files:
                    if file_info.get("type") == "blob":  # blob = file, tree = directory
                        existing_files.add(file_info.get("path", ""))

                # Check if there are more pages
                if len(files) < params["per_page"]:
                    break
                page += 1

        except Exception as e:
            log.warning("error_getting_repository_files",
                       error=str(e),
                       project_id=project_id,
                       branch=branch)
            existing_files = set()

        # Cache the result
        self._repo_files_cache[cache_key] = existing_files

        log.debug("repository_files_retrieved",
                 project_id=project_id,
                 branch=branch,
                 file_count=len(existing_files))

        return existing_files

    def _validate_commit_data(self, body: dict) -> list:
        """
        Validate commit data before sending to GitLab.
        Returns list of validation errors.
        """
        errors = []

        # Check required fields
        if not body.get("branch"):
            errors.append("Branch name is required")

        if not body.get("commit_message"):
            errors.append("Commit message is required")

        actions = body.get("actions", [])
        if not actions:
            errors.append("At least one action is required")

        # Validate each action
        for i, action in enumerate(actions):
            if not isinstance(action, dict):
                errors.append(f"Action {i}: must be a dictionary")
                continue

            # Check required action fields
            if not action.get("action"):
                errors.append(f"Action {i}: 'action' field is required")
            elif action["action"] not in ["create", "update", "move", "delete"]:
                errors.append(f"Action {i}: invalid action type '{action['action']}'")

            if not action.get("file_path"):
                errors.append(f"Action {i}: 'file_path' field is required")

            # For create/update actions, content is required
            if action.get("action") in ["create", "update"]:
                if "content" not in action:
                    errors.append(f"Action {i}: 'content' field is required for {action['action']} action")
                elif not isinstance(action["content"], (str, bytes)):
                    errors.append(f"Action {i}: 'content' must be string or bytes")

            # Validate file path
            file_path = action.get("file_path", "")
            if file_path:
                # Check for invalid characters in file path
                invalid_chars = ["<", ">", ":", '"', "|", "?", "*"]
                if any(char in file_path for char in invalid_chars):
                    errors.append(f"Action {i}: file path contains invalid characters")

                # Check for absolute paths (should be relative)
                if file_path.startswith("/") or (len(file_path) > 1 and file_path[1] == ":"):
                    errors.append(f"Action {i}: file path should be relative, not absolute")

                # Check for empty path components
                if "//" in file_path or file_path.endswith("/"):
                    errors.append(f"Action {i}: invalid file path format")

            # Validate encoding if specified
            encoding = action.get("encoding")
            if encoding and encoding not in ["text", "base64"]:
                errors.append(f"Action {i}: invalid encoding '{encoding}', must be 'text' or 'base64'")

        # Check for duplicate file paths
        file_paths = [action.get("file_path") for action in actions if action.get("file_path")]
        duplicate_paths = set([path for path in file_paths if file_paths.count(path) > 1])
        if duplicate_paths:
            errors.append(f"Duplicate file paths found: {', '.join(duplicate_paths)}")

        return errors

    def _is_binary_content(self, content: bytes, file_name: str) -> bool:
        """
        Determine if content is binary based on file extension and content analysis.
        """
        # Check file extension first
        binary_extensions = {
            '.zip', '.jar', '.war', '.ear', '.tar', '.gz', '.bz2', '.7z',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg',
            '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',
            '.exe', '.dll', '.so', '.dylib', '.bin', '.dat'
        }

        file_ext = '.' + file_name.split('.')[-1].lower() if '.' in file_name else ''
        if file_ext in binary_extensions:
            return True

        # Check for null bytes (common in binary files)
        if b'\x00' in content[:1024]:  # Check first 1KB
            return True

        # Check for high ratio of non-printable characters
        if len(content) > 0:
            printable_chars = sum(1 for byte in content[:1024] if 32 <= byte <= 126 or byte in [9, 10, 13])
            ratio = printable_chars / min(len(content), 1024)
            if ratio < 0.7:  # Less than 70% printable characters
                return True

        return False

    def clear_cache(self) -> None:
        """Clear the repository files cache. Useful for testing or when repository state changes."""
        self._repo_files_cache.clear()
        log.debug("repository_files_cache_cleared")

    def _should_skip_optimization(self, actions: list) -> bool:
        """
        Determine if we should skip the optimization step.
        Skip if there are too many files (performance) or if all actions are the same type.
        """
        if len(actions) > 1000:  # Skip optimization for very large commits
            log.info("skipping_optimization_large_commit", file_count=len(actions))
            return True

        # If all actions are already "update", no need to optimize
        action_types = set(action.get("action") for action in actions)
        if len(action_types) == 1 and "update" in action_types:
            log.debug("skipping_optimization_all_updates")
            return True

        return False


# ---------------- helpers ----------------

def _parse_gitlab_repo(repo_url: str) -> Tuple[str, str]:
    """
    Accepts repo URLs like:
      https://gitlab.example.com/group/subgroup/repo.git
    Returns:
      host -> "gitlab.example.com"
      project_id -> URL-encoded "group/subgroup/repo"
    """
    try:
        u = urlparse(repo_url)
        host = u.netloc
        path = (u.path or "").strip("/")
        if path.endswith(".git"):
            path = path[:-4]
        if not host or not path:
            raise ValueError("missing host/path")
        project_id = quote_plus(path)  # encode slashes as %2F
        return host, project_id
    except Exception as e:
        raise ValidationError("Invalid GitLab repo URL", details={"repo_url": repo_url, "error": str(e)})


def _auth_headers(pat: Optional[str]) -> dict:
    # Prefer PRIVATE-TOKEN header; Authorization: Bearer works on recent GitLab, too.
    h = {"Accept": "application/json"}
    if pat:
        h["PRIVATE-TOKEN"] = pat
    return h


def _guess_path_from_message(message: str) -> Optional[str]:
    """
    Parses "bootstrap <proxy>@rev<rev>" pattern to produce bundles/<proxy>/rev-<rev>.zip.
    """
    m = re.search(r"\bbootstrap\s+([a-zA-Z0-9\-_.]+)@rev(\d+)\b", message)
    if not m:
        return None
    proxy, rev = m.group(1), m.group(2)
    return f"bundles/{proxy}/rev-{rev}.zip"


def _safe_json(resp: httpx.Response):
    try:
        return resp.json()
    except Exception:
        return {"text": resp.text[:1000]}