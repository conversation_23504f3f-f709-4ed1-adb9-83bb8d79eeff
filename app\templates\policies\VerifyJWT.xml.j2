<VerifyJWT name="{{ name }}">
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}  <!-- variable containing JWT -->
  {% if params.algorithm %}<Algorithm>{{ params.algorithm }}</Algorithm>{% endif %}
  {% if params.public_key %}
  <PublicKey>
    {% if params.public_key.ref %}<Value ref="{{ params.public_key.ref }}"/>{% elif params.public_key.inline %}<Value>{{ params.public_key.inline }}</Value>{% endif %}
  </PublicKey>
  {% endif %}
  {% if params.secret_key %}
  <SecretKey>
    {% if params.secret_key.ref %}<Value ref="{{ params.secret_key.ref }}"/>{% elif params.secret_key.inline %}<Value>{{ params.secret_key.inline }}</Value>{% endif %}
  </SecretKey>
  {% endif %}
  {% if params.issuer %}<Issuer>{{ params.issuer }}</Issuer>{% endif %}
  {% if params.audience %}<Audience>{{ params.audience }}</Audience>{% endif %}
  {% if params.clock_skew is defined %}<ClockSkew>{{ params.clock_skew }}</ClockSkew>{% endif %}
  {% if params.output is defined %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
</VerifyJWT>