{"swagger": "2.0", "info": {"description": "NeoBanking BNPL Experience APIs Documentation", "version": "1.0.0", "title": "ab-neo-bnpl-experience-java-service", "termsOfService": "https://developer.arabbank.com/api-catalogue", "contact": {"name": "Arab-Bank", "url": "http://www.arabbank.jo", "email": "<EMAIL>"}, "license": {"name": "Arab-Bank API license 1.0", "url": "https://developer.arabbank.com/api-catalogue"}}, "host": "*************", "tags": [{"name": "NeoBank BNPL Experience Services", "description": "Experience Controller"}, {"name": "NeoBank Repayment Services", "description": "Repayment Controller"}], "paths": {"/neobanking/bnpl-experience/v1/dashboard": {"get": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Dashboard", "description": "Dashboard", "operationId": "dashboardUsingGET", "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "Country Code ISO 2", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "Customer language preference default value EN", "required": false, "type": "string", "default": "EN", "maxLength": 2, "minLength": 2, "enum": ["ar", "en", "fr"]}, {"name": "uuid", "in": "header", "description": "UUID transaction ID", "required": false, "type": "string", "default": "a92b27b6-3746-4f43-a0e8-664665754f4f", "x-example": "39028d1f-2917-4ad7-a4b2-d527da9d1c99"}, {"name": "x-channel-identifier", "in": "header", "description": "Channel reference", "required": true, "type": "string", "default": "NEO", "x-example": "MB", "enum": ["NEO"]}, {"name": "x-fapi-interaction-id", "in": "header", "description": "Interaction Transaction ID", "required": true, "type": "string", "default": "f8764743-6eb3-4214-813c-b3158b6afa08", "x-example": "AA97B177-**************-0F91A7A02836"}, {"name": "x-jws-signature", "in": "header", "description": "Header containing a JWS signature of the body of the payload", "required": false, "type": "string"}, {"name": "x-service-identifier", "in": "header", "description": "Service Code for internal use", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/DashboardResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/merchants": {"get": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Merchant List", "description": "Merchant List", "operationId": "merchantListUsingGET", "produces": ["application/json"], "parameters": [{"name": "category", "in": "query", "description": "Merchant Category ", "required": false, "type": "string"}, {"name": "country", "in": "header", "description": "Country Code ISO 2", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "Customer language preference default value EN", "required": false, "type": "string", "default": "EN", "maxLength": 2, "minLength": 2, "enum": ["ar", "en", "fr"]}, {"name": "uuid", "in": "header", "description": "UUID transaction ID", "required": false, "type": "string", "default": "1a1c7444-9fd4-407d-b791-edbc97b0ee7d", "x-example": "39028d1f-2917-4ad7-a4b2-d527da9d1c99"}, {"name": "x-channel-identifier", "in": "header", "description": "Channel reference", "required": true, "type": "string", "default": "NEO", "x-example": "MB", "enum": ["NEO"]}, {"name": "x-fapi-interaction-id", "in": "header", "description": "Interaction Transaction ID", "required": true, "type": "string", "default": "83b393b1-cb64-4a5c-8113-40522b60df47", "x-example": "AA97B177-**************-0F91A7A02836"}, {"name": "x-jws-signature", "in": "header", "description": "Header containing a JWS signature of the body of the payload", "required": false, "type": "string"}, {"name": "x-service-identifier", "in": "header", "description": "Service Code for internal use", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/MerchantDtoWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/analysis": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Registration Analysis", "description": "Registration Analysis", "operationId": "analysisUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "8e885a2b-0ab9-4700-91fd-eeeb5a601496"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "15be065e-2674-4ccd-bad8-ea0171ef7c8b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplRegistrationAnalysisResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/analysis/v2": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Registration Analysis V2", "description": "Registration Analysis V2", "operationId": "analysisV2UsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "7a90ca26-e9ce-4dc8-a5bf-129d6125bb06"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "f6ff2ef5-5cc1-4b47-a5bc-617eff748cf5"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplRegistrationAnalysisResponseV2Wrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/cancel": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Registration Cancellation", "description": "Registration Cancellation", "operationId": "cancelUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "fa32ff5f-f1fe-4741-aa0a-dee040c67683"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "baf8504e-88ec-4c7a-9c42-58e8f30d2daf"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplRegistrationCancelResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/declaration/income-expense": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Update income Details", "description": "Update income Details", "operationId": "updateIncomeDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/IncomeAndExpenseRequest"}}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "f3805742-0043-41f4-a731-7e9331afc0fc"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9dd12a0d-642f-42c6-99e0-9ff1a4dc7383"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/IncomeAndExpenseResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/initiation": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Registration Initiation", "description": "Registration Initiation", "operationId": "initiationUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ConfirmationTokenRequest"}}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "974f23d7-fdf8-41d3-abdb-741dd189d2eb"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "ca654377-2f66-45b9-9f88-1bbbdb35196b"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplRegistrationInitiationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/registration/initiation/v2": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "Registration Initiation V2", "description": "Registration Initiation V2", "operationId": "initiationV2UsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ConfirmationTokenRequest"}}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "054e94f0-2b18-4886-af7f-77250a29908b"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "9b526f92-485e-4e02-b520-0f51a0cc93a9"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplRegistrationInitiationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/repayment/analysis": {"post": {"tags": ["NeoBank Repayment Services"], "summary": "Repayment Analysis", "description": "Repayment Analysis", "operationId": "analysisUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "Country Code ISO 2", "required": true, "type": "string", "enum": ["AE", "EG", "JO", "PS"]}, {"name": "lang", "in": "header", "description": "Customer language preference default value EN", "required": false, "type": "string", "default": "EN", "maxLength": 2, "minLength": 2}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/RepaymentAnalysisWrapper"}}, {"name": "uuid", "in": "header", "description": "UUID transaction ID", "required": false, "type": "string", "x-example": "39028d1f-2917-4ad7-a4b2-d527da9d1c99"}, {"name": "x-channel-identifier", "in": "header", "description": "Channel reference", "required": true, "type": "string", "default": "NEO", "enum": ["NEO"]}, {"name": "x-fapi-interaction-id", "in": "header", "description": "Interaction Transaction ID", "required": true, "type": "string", "x-example": "AA97B177-**************-0F91A7A02836"}, {"name": "x-jws-signature", "in": "header", "description": "Header containing a JWS signature of the body of the payload", "required": false, "type": "string"}, {"name": "x-service-identifier", "in": "header", "description": "Service Code for internal use", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/RepaymentAnalysisResponseDTOWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/repayment/initiation": {"post": {"tags": ["NeoBank Repayment Services"], "summary": "Repayment Initiation", "description": "Repayment Initiation", "operationId": "initiationUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "Country Code ISO 2", "required": true, "type": "string", "enum": ["AE", "EG", "JO", "PS"]}, {"name": "lang", "in": "header", "description": "Customer language preference default value EN", "required": false, "type": "string", "default": "EN", "maxLength": 2, "minLength": 2}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/ConfirmationTokenRequest"}}, {"name": "uuid", "in": "header", "description": "UUID transaction ID", "required": false, "type": "string", "x-example": "39028d1f-2917-4ad7-a4b2-d527da9d1c99"}, {"name": "x-channel-identifier", "in": "header", "description": "Channel reference", "required": true, "type": "string", "default": "NEO", "enum": ["NEO"]}, {"name": "x-fapi-interaction-id", "in": "header", "description": "Interaction Transaction ID", "required": true, "type": "string", "x-example": "AA97B177-**************-0F91A7A02836"}, {"name": "x-jws-signature", "in": "header", "description": "Header containing a JWS signature of the body of the payload", "required": false, "type": "string"}, {"name": "x-service-identifier", "in": "header", "description": "Service Code for internal use", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/RepaymentInitiationResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/repayment/transaction": {"get": {"tags": ["NeoBank Repayment Services"], "summary": "Payment history", "description": "Payment history", "operationId": "paymentHistoryUsingGET", "produces": ["application/json"], "parameters": [{"name": "country", "in": "header", "description": "Country Code ISO 2", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "Customer language preference default value EN", "required": false, "type": "string", "default": "EN", "maxLength": 2, "minLength": 2, "enum": ["ar", "en", "fr"]}, {"name": "payment-type", "in": "header", "description": "Payment Type", "required": false, "type": "string"}, {"name": "uuid", "in": "header", "description": "UUID transaction ID", "required": false, "type": "string", "default": "40d3989e-3828-4b90-9122-83aa0be34e30", "x-example": "39028d1f-2917-4ad7-a4b2-d527da9d1c99"}, {"name": "x-channel-identifier", "in": "header", "description": "Channel reference", "required": true, "type": "string", "default": "NEO", "x-example": "MB", "enum": ["NEO"]}, {"name": "x-fapi-interaction-id", "in": "header", "description": "Interaction Transaction ID", "required": true, "type": "string", "default": "c9b5781d-2ede-4424-835f-abeffd4f4353", "x-example": "AA97B177-**************-0F91A7A02836"}, {"name": "x-jws-signature", "in": "header", "description": "Header containing a JWS signature of the body of the payload", "required": false, "type": "string"}, {"name": "x-service-identifier", "in": "header", "description": "Service Code for internal use", "required": false, "type": "string"}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/PaymentHistoryResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/social-security/send-otp": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "SSC send otp code", "description": "SSC send otp code", "operationId": "sscSendOtpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/BnplSscSendOtpRequest"}}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "6bf2a5e9-d48e-4414-ad21-f906f1fb8406"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "122ca357-9252-4630-88ca-ec3cab0c4c62"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplSscSendOtpResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}, "/neobanking/bnpl-experience/v1/social-security/validate-otp": {"post": {"tags": ["NeoBank BNPL Experience Services"], "summary": "SSC validate otp code", "description": "SSC validate otp code", "operationId": "sscValidateOtpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "country", "in": "header", "description": "country code in iso 3166-1 alpha-2 format", "required": true, "type": "string", "x-example": "JO", "enum": ["AE", "BH", "EG", "JO", "LB", "PS", "QA"]}, {"name": "lang", "in": "header", "description": "language code in iso 639-1 format", "required": false, "type": "string", "default": "en", "enum": ["ar", "en", "fr"]}, {"in": "body", "name": "request", "description": "request", "required": true, "schema": {"$ref": "#/definitions/BnplSscValidateOtpRequest"}}, {"name": "uuid", "in": "header", "description": "Request unique identifier", "required": false, "type": "string", "default": "b2efdcff-f502-4bf8-b328-b9fc6021e0f0"}, {"name": "x-channel-identifier", "in": "header", "description": "channel name like MB, IB, BPM, ATM, FINN1", "required": true, "type": "string", "default": "MB", "x-example": "MB"}, {"name": "x-fapi-interaction-id", "in": "header", "description": "session id, should be the same when calling a list of apis in the same journey", "required": true, "type": "string", "default": "594268e5-29e4-4f28-91c1-b03a6bf5a069"}, {"name": "x-jws-signature", "in": "header", "description": "data to check whether the message has not been changed on way from the sender to the recipient", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/BnplSscSendOtpResponseWrapper"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/ErrorResponseDTO"}}}}}}, "definitions": {"AnalysisApplication": {"type": "object", "properties": {"adminFees": {"$ref": "#/definitions/InstructedAmount"}, "preApprovedLimit": {"$ref": "#/definitions/InstructedAmount"}}, "title": "AnalysisApplication"}, "AnalysisApplicationV2": {"type": "object", "properties": {"adminFees": {"$ref": "#/definitions/InstructedAmount"}, "averageBalance": {"$ref": "#/definitions/InstructedAmount"}, "description": {"type": "string"}, "preApprovedLimit": {"$ref": "#/definitions/InstructedAmount"}, "status": {"type": "string"}, "statusCode": {"type": "string"}, "walletOpenedDate": {"type": "string"}}, "title": "AnalysisApplicationV2"}, "BnplRegistrationAnalysisResponse": {"type": "object", "properties": {"application": {"$ref": "#/definitions/AnalysisApplication"}, "confirmationToken": {"type": "string"}, "documents": {"$ref": "#/definitions/Documents"}}, "title": "BnplRegistrationAnalysisResponse"}, "BnplRegistrationAnalysisResponseV2": {"type": "object", "properties": {"application": {"$ref": "#/definitions/AnalysisApplicationV2"}, "confirmationToken": {"type": "string"}, "documents": {"$ref": "#/definitions/Documents"}, "incomeExpenseDetails": {"$ref": "#/definitions/UserIncomeDetails"}}, "title": "BnplRegistrationAnalysisResponseV2"}, "BnplRegistrationAnalysisResponseV2Wrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationAnalysisResponseV2"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BnplRegistrationAnalysisResponseV2Wrapper"}, "BnplRegistrationAnalysisResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationAnalysisResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BnplRegistrationAnalysisResponseWrapper"}, "BnplRegistrationCancelResponse": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/CancelResponse"}}}, "title": "BnplRegistrationCancelResponse"}, "BnplRegistrationCancelResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/BnplRegistrationCancelResponse"}}}, "title": "BnplRegistrationCancelResponseDataHolder"}, "BnplRegistrationCancelResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationCancelResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BnplRegistrationCancelResponseWrapper"}, "BnplRegistrationInitiationResponse": {"type": "object", "properties": {"application": {"$ref": "#/definitions/InitiationApplication"}}, "title": "BnplRegistrationInitiationResponse"}, "BnplRegistrationInitiationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationInitiationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BnplRegistrationInitiationResponseWrapper"}, "BnplSscSendOtpRequest": {"type": "object", "properties": {"status": {"type": "string"}}, "title": "BnplSscSendOtpRequest"}, "BnplSscSendOtpResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ACCEPT", "APPROVED", "CANCELLED", "CONTRACT_ACCEPT", "CONTRACT_DECLINE", "CUSTOMER_CREATED", "DECLINE", "DISCLOSURE_ACCEPT", "DISCLOSURE_DECLINE", "FAILED", "FUNDING_ACCOUNT_CREATED", "INITIATED", "L1_INSUFFICIENT_BALANCE", "L1_REJECTED", "L1_SUCCESS", "L2_FAILED", "L2_INELIGIBLE_AMOUNT", "L2_INSUFFICIENT_BALANCE", "L2_REJECTED", "L2_SUBMITTED", "L2_SUCCESS", "LIFE_INSURANCE_ACCEPT", "LIFE_INSURANCE_DECLINE", "LOAN_APPLICATION_ACCEPT", "LOAN_APPLICATION_DECLINE", "LOAN_INITIATED", "LOAN_PRE_CLOSED", "LOAN_RETRY_INITIATED", "LOAN_SANCTIONED", "LOAN_SETTLED", "LOAN_SUBMITTED", "NOT_INITIATED", "PRE_CLOSED", "REJECTED", "SETTLED", "SSC_SEND_OTP_ACCEPT", "SSC_SEND_OTP_DECLINE", "SSC_SEND_OTP_FAILED", "SSC_SEND_OTP_SUCCESS", "SSC_VALIDATE_OTP_FAILED", "SSC_VALIDATE_OTP_SUCCESS", "SUBMITTED", "SUCCESS", "TERMINATED", "TNC_ACCEPT", "TNC_DECLINE"]}}, "title": "BnplSscSendOtpResponse"}, "BnplSscSendOtpResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplSscSendOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "BnplSscSendOtpResponseWrapper"}, "BnplSscValidateOtpRequest": {"type": "object", "properties": {"otpCode": {"type": "string"}}, "title": "BnplSscValidateOtpRequest"}, "CancelResponse": {"type": "object", "properties": {"result": {"type": "string"}}, "title": "CancelResponse"}, "ConfirmationTokenRequest": {"type": "object", "properties": {"confirmationToken": {"type": "string"}}, "title": "ConfirmationTokenRequest"}, "CurrentCommitments": {"type": "object", "properties": {"electronicDeviceObligations": {"$ref": "#/definitions/InstructedAmount"}, "furnitureObligations": {"$ref": "#/definitions/InstructedAmount"}, "loanGuarantees": {"$ref": "#/definitions/InstructedAmount"}, "otherCommitments": {"$ref": "#/definitions/InstructedAmount"}}, "title": "CurrentCommitments"}, "DashboardApplication": {"type": "object", "properties": {"applicationDate": {"type": "string"}, "applicationId": {"type": "string"}, "applicationStatus": {"type": "string"}, "approvedLimit": {"$ref": "#/definitions/InstructedAmount"}, "availableLimit": {"$ref": "#/definitions/InstructedAmount"}, "businessErrorCode": {"type": "string"}, "limitExpiryDate": {"type": "string"}, "limitIssueDate": {"type": "string"}, "limitValidityPeriodInDays": {"type": "integer", "format": "int32"}, "profileReApplyDays": {"type": "integer", "format": "int32"}, "reApplicationDate": {"type": "string"}, "spentAmount": {"$ref": "#/definitions/InstructedAmount"}, "statusDescription": {"type": "string"}}, "title": "DashboardApplication"}, "DashboardResponse": {"type": "object", "properties": {"application": {"$ref": "#/definitions/DashboardApplication"}, "documents": {"$ref": "#/definitions/Documents"}, "merchantList": {"type": "array", "items": {"$ref": "#/definitions/MerchantDto"}}, "upcomingPaymentsSummary": {"type": "array", "items": {"$ref": "#/definitions/PaymentSummaryDto"}}}, "title": "DashboardResponse"}, "DashboardResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/DashboardResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "DashboardResponseWrapper"}, "Documents": {"type": "object", "properties": {"currentVersion": {"type": "string"}, "href": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}}, "title": "Documents"}, "ErrorOfobject": {"type": "object", "properties": {"errorCode": {"type": "string"}, "errorDetails": {"type": "object"}, "message": {"type": "string"}, "path": {"type": "string"}, "url": {"type": "string"}}, "title": "ErrorOfobject"}, "ErrorResponseDTO": {"type": "object", "properties": {"code": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorOfobject"}}, "id": {"type": "string"}, "message": {"type": "string"}, "url": {"type": "string"}}, "title": "ErrorResponseDTO"}, "FixedMonthlyIncome": {"type": "object", "properties": {"educationExpense": {"$ref": "#/definitions/InstructedAmount"}, "housingExpense": {"$ref": "#/definitions/InstructedAmount"}, "monthlyBills": {"$ref": "#/definitions/InstructedAmount"}, "otherExpense": {"$ref": "#/definitions/InstructedAmount"}}, "title": "FixedMonthlyIncome"}, "HateoasDTO": {"type": "object", "properties": {"href": {"type": "string"}, "method": {"type": "string"}, "rel": {"type": "string"}}, "title": "HateoasDTO"}, "IncomeAndExpenseRequest": {"type": "object", "properties": {"confirmationToken": {"type": "string"}, "incomeExpenseDetails": {"$ref": "#/definitions/UserIncomeDetails"}, "status": {"type": "string"}}, "title": "IncomeAndExpenseRequest"}, "IncomeAndExpenseResponse": {"type": "object", "properties": {"application": {"$ref": "#/definitions/AnalysisApplicationV2"}, "confirmationToken": {"type": "string"}, "documents": {"$ref": "#/definitions/Documents"}}, "title": "IncomeAndExpenseResponse"}, "IncomeAndExpenseResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/IncomeAndExpenseResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "IncomeAndExpenseResponseWrapper"}, "IncomeDeclaration": {"type": "object", "properties": {"monthlySalary": {"$ref": "#/definitions/InstructedAmount"}}, "title": "IncomeDeclaration"}, "InitiationApplication": {"type": "object", "properties": {"applicationDate": {"type": "string"}, "applicationId": {"type": "string"}, "applicationStatus": {"type": "string"}}, "title": "InitiationApplication"}, "InstructedAmount": {"type": "object", "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}, "title": "InstructedAmount"}, "MerchantDto": {"type": "object", "properties": {"merchantIcon": {"type": "string"}, "merchantId": {"type": "string"}, "merchantName": {"type": "string"}}, "title": "MerchantDto"}, "MerchantDtoDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/MerchantDto"}}}, "title": "MerchantDtoDataHolder"}, "MerchantDtoWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/MerchantDtoDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "MerchantDtoWrapper"}, "MetadataDTO": {"type": "object", "properties": {"pagination": {"$ref": "#/definitions/PaginationDTO"}, "queryParameters": {"type": "object"}}, "title": "MetadataDTO"}, "PaginationDTO": {"type": "object", "properties": {"hasNext": {"type": "boolean"}, "totalPages": {"type": "integer", "format": "int32"}, "totalRecords": {"type": "integer", "format": "int64"}}, "title": "PaginationDTO"}, "PaymentHistoryResponse": {"type": "object", "properties": {"documents": {"$ref": "#/definitions/Documents"}, "paymentsSummary": {"type": "array", "items": {"$ref": "#/definitions/PaymentSummaryDto"}}}, "title": "PaymentHistoryResponse"}, "PaymentHistoryResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/PaymentHistoryResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "PaymentHistoryResponseWrapper"}, "PaymentSummaryDto": {"type": "object", "properties": {"daysLeftToDueDate": {"type": "integer", "format": "int32"}, "documents": {"$ref": "#/definitions/Documents"}, "dueAmount": {"$ref": "#/definitions/InstructedAmount"}, "merchantIcon": {"type": "string"}, "merchantId": {"type": "string"}, "merchantName": {"type": "string"}, "numberOfRepaymentsPaid": {"type": "integer", "format": "int32"}, "numberOfRepaymentsPending": {"type": "integer", "format": "int32"}, "purchaseId": {"type": "string"}, "repaymentPlan": {"type": "array", "items": {"$ref": "#/definitions/RepaymentPlan"}}, "repaymentStatus": {"type": "string"}, "totalNumberOfRepayments": {"type": "integer", "format": "int32"}}, "title": "PaymentSummaryDto"}, "RepaymentAnalysisRequest2": {"type": "object", "properties": {"planId": {"type": "string"}, "purchaseId": {"type": "string"}, "rePaymentId": {"type": "string"}}, "title": "RepaymentAnalysisRequest2"}, "RepaymentAnalysisResponse": {"type": "object", "properties": {"dueDate": {"type": "string"}, "dueDays": {"type": "integer", "format": "int32"}, "lateFees": {"$ref": "#/definitions/InstructedAmount"}, "paymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentDate": {"type": "string"}, "planId": {"type": "string"}, "purchaseId": {"type": "string"}, "rePaymentId": {"type": "string"}, "rePaymentNo": {"type": "string"}, "rePaymentType": {"type": "string"}, "status": {"type": "string"}, "totalAmount": {"$ref": "#/definitions/InstructedAmount"}}, "title": "RepaymentAnalysisResponse"}, "RepaymentAnalysisResponseDTO": {"type": "object", "properties": {"confirmationToken": {"type": "string"}, "results": {"type": "array", "items": {"$ref": "#/definitions/RepaymentAnalysisResponse"}}}, "title": "RepaymentAnalysisResponseDTO"}, "RepaymentAnalysisResponseDTOWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/RepaymentAnalysisResponseDTO"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "RepaymentAnalysisResponseDTOWrapper"}, "RepaymentAnalysisWrapper": {"type": "object", "properties": {"payments": {"type": "array", "items": {"$ref": "#/definitions/RepaymentAnalysisRequest2"}}}, "title": "RepaymentAnalysisWrapper"}, "RepaymentInitiationResponse": {"type": "object", "properties": {"dueDate": {"type": "string"}, "dueDays": {"type": "integer", "format": "int32"}, "lateFees": {"$ref": "#/definitions/InstructedAmount"}, "paymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentDate": {"type": "string"}, "planId": {"type": "string"}, "purchaseId": {"type": "string"}, "rePaymentId": {"type": "string"}, "rePaymentNo": {"type": "string"}, "rePaymentType": {"type": "string"}, "status": {"type": "string"}, "totalAmount": {"$ref": "#/definitions/InstructedAmount"}, "transaction": {"$ref": "#/definitions/RepaymentTransactionInfo"}}, "title": "RepaymentInitiationResponse"}, "RepaymentInitiationResponseDataHolder": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/definitions/RepaymentInitiationResponse"}}}, "title": "RepaymentInitiationResponseDataHolder"}, "RepaymentInitiationResponseWrapper": {"type": "object", "properties": {"data": {"$ref": "#/definitions/RepaymentInitiationResponseDataHolder"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "RepaymentInitiationResponseWrapper"}, "RepaymentPlan": {"type": "object", "properties": {"dueAmount": {"$ref": "#/definitions/InstructedAmount"}, "dueDate": {"type": "string"}, "dueDays": {"type": "integer", "format": "int32"}, "lateFees": {"$ref": "#/definitions/InstructedAmount"}, "paidAmount": {"$ref": "#/definitions/InstructedAmount"}, "paymentDate": {"type": "string"}, "planId": {"type": "string"}, "purchaseId": {"type": "string"}, "repaymentAmount": {"$ref": "#/definitions/InstructedAmount"}, "repaymentId": {"type": "string"}, "repaymentNo": {"type": "string"}, "repaymentType": {"type": "string"}, "status": {"type": "string"}}, "title": "RepaymentPlan"}, "RepaymentTransactionInfo": {"type": "object", "properties": {"externalReference": {"type": "string"}, "purchaseId": {"type": "string"}, "status": {"type": "string"}, "walletPaymentReference": {"type": "string"}}, "title": "RepaymentTransactionInfo"}, "ResponseBodyTemplateDTOOfBnplRegistrationCancelResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/BnplRegistrationCancelResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfBnplRegistrationCancelResponse"}, "ResponseBodyTemplateDTOOfMerchantDto": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/MerchantDto"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfMerchantDto"}, "ResponseBodyTemplateDTOOfRepaymentInitiationResponse": {"type": "object", "properties": {"data": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/definitions/RepaymentInitiationResponse"}}}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "ResponseBodyTemplateDTOOfRepaymentInitiationResponse"}, "ResponseTemplateDTOOfBnplRegistrationCancelResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfBnplRegistrationCancelResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfBnplRegistrationCancelResponse"}, "ResponseTemplateDTOOfMerchantDto": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfMerchantDto"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfMerchantDto"}, "ResponseTemplateDTOOfRepaymentInitiationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/ResponseBodyTemplateDTOOfRepaymentInitiationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "ResponseTemplateDTOOfRepaymentInitiationResponse"}, "SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationAnalysisResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponse"}, "SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponseV2": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationAnalysisResponseV2"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponseV2"}, "SingularResponseBodyTemplateDTOOfBnplRegistrationInitiationResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplRegistrationInitiationResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfBnplRegistrationInitiationResponse"}, "SingularResponseBodyTemplateDTOOfBnplSscSendOtpResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/BnplSscSendOtpResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfBnplSscSendOtpResponse"}, "SingularResponseBodyTemplateDTOOfDashboardResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/DashboardResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfDashboardResponse"}, "SingularResponseBodyTemplateDTOOfIncomeAndExpenseResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/IncomeAndExpenseResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfIncomeAndExpenseResponse"}, "SingularResponseBodyTemplateDTOOfPaymentHistoryResponse": {"type": "object", "properties": {"data": {"$ref": "#/definitions/PaymentHistoryResponse"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfPaymentHistoryResponse"}, "SingularResponseBodyTemplateDTOOfRepaymentAnalysisResponseDTO": {"type": "object", "properties": {"data": {"$ref": "#/definitions/RepaymentAnalysisResponseDTO"}, "links": {"type": "array", "items": {"$ref": "#/definitions/HateoasDTO"}}, "metadata": {"$ref": "#/definitions/MetadataDTO"}}, "title": "SingularResponseBodyTemplateDTOOfRepaymentAnalysisResponseDTO"}, "SingularResponseTemplateDTOOfBnplRegistrationAnalysisResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfBnplRegistrationAnalysisResponse"}, "SingularResponseTemplateDTOOfBnplRegistrationAnalysisResponseV2": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfBnplRegistrationAnalysisResponseV2"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfBnplRegistrationAnalysisResponseV2"}, "SingularResponseTemplateDTOOfBnplRegistrationInitiationResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfBnplRegistrationInitiationResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfBnplRegistrationInitiationResponse"}, "SingularResponseTemplateDTOOfBnplSscSendOtpResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfBnplSscSendOtpResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfBnplSscSendOtpResponse"}, "SingularResponseTemplateDTOOfDashboardResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfDashboardResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfDashboardResponse"}, "SingularResponseTemplateDTOOfIncomeAndExpenseResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfIncomeAndExpenseResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfIncomeAndExpenseResponse"}, "SingularResponseTemplateDTOOfPaymentHistoryResponse": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfPaymentHistoryResponse"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfPaymentHistoryResponse"}, "SingularResponseTemplateDTOOfRepaymentAnalysisResponseDTO": {"type": "object", "properties": {"body": {"$ref": "#/definitions/SingularResponseBodyTemplateDTOOfRepaymentAnalysisResponseDTO"}, "statusCode": {"type": "string", "enum": ["ACCEPTED", "ALREADY_REPORTED", "BAD_GATEWAY", "BAD_REQUEST", "BANDWIDTH_LIMIT_EXCEEDED", "CHECKPOINT", "CONFLICT", "CONTINUE", "CREATED", "DESTINATION_LOCKED", "EXPECTATION_FAILED", "FAILED_DEPENDENCY", "FORBIDDEN", "FOUND", "GATEWAY_TIMEOUT", "GONE", "HTTP_VERSION_NOT_SUPPORTED", "IM_USED", "INSUFFICIENT_SPACE_ON_RESOURCE", "INSUFFICIENT_STORAGE", "INTERNAL_SERVER_ERROR", "I_AM_A_TEAPOT", "LENGTH_REQUIRED", "LOCKED", "LOOP_DETECTED", "METHOD_FAILURE", "METHOD_NOT_ALLOWED", "MOVED_PERMANENTLY", "MOVED_TEMPORARILY", "MULTIPLE_CHOICES", "MULTI_STATUS", "NETWORK_AUTHENTICATION_REQUIRED", "NON_AUTHORITATIVE_INFORMATION", "NOT_ACCEPTABLE", "NOT_EXTENDED", "NOT_FOUND", "NOT_IMPLEMENTED", "NOT_MODIFIED", "NO_CONTENT", "OK", "PARTIAL_CONTENT", "PAYLOAD_TOO_LARGE", "PAYMENT_REQUIRED", "PERMANENT_REDIRECT", "PRECONDITION_FAILED", "PRECONDITION_REQUIRED", "PROCESSING", "PROXY_AUTHENTICATION_REQUIRED", "REQUESTED_RANGE_NOT_SATISFIABLE", "REQUEST_ENTITY_TOO_LARGE", "REQUEST_HEADER_FIELDS_TOO_LARGE", "REQUEST_TIMEOUT", "REQUEST_URI_TOO_LONG", "RESET_CONTENT", "SEE_OTHER", "SERVICE_UNAVAILABLE", "SWITCHING_PROTOCOLS", "TEMPORARY_REDIRECT", "TOO_EARLY", "TOO_MANY_REQUESTS", "UNAUTHORIZED", "UNAVAILABLE_FOR_LEGAL_REASONS", "UNPROCESSABLE_ENTITY", "UNSUPPORTED_MEDIA_TYPE", "UPGRADE_REQUIRED", "URI_TOO_LONG", "USE_PROXY", "VARIANT_ALSO_NEGOTIATES"]}, "statusCodeValue": {"type": "integer", "format": "int32"}}, "title": "SingularResponseTemplateDTOOfRepaymentAnalysisResponseDTO"}, "UserIncomeDetails": {"type": "object", "properties": {"currentCommitments": {"$ref": "#/definitions/CurrentCommitments"}, "fixedMonthlyIncome": {"$ref": "#/definitions/FixedMonthlyIncome"}, "incomeDeclaration": {"$ref": "#/definitions/IncomeDeclaration"}}, "title": "UserIncomeDetails"}}}