#!/usr/bin/env python3
"""
Test script for Phase 3: Smart Policy Template System
- Template helper functions validation
- Conditional rendering logic
- Template testing framework
- Enhanced policy templates
"""

import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.services.template_helpers import TemplateHelpers
from app.services.policy_builder import PolicyBuilder
from app.config.loader import ConfigLoader
from app.testing.template_tester import create_standard_test_suite


def test_template_helpers():
    """Test template helper functions"""
    print("Testing Template Helper Functions...")
    
    try:
        helpers = TemplateHelpers(env_name="test", features={"advanced_security": True})
        
        # Test validation helpers
        test_cases = [
            (helpers.is_valid_rate, "100pm", True),
            (helpers.is_valid_rate, "invalid", False),
            (helpers.is_valid_rate, "50ps", True),
            (helpers.is_valid_url, "https://example.com", True),
            (helpers.is_valid_url, "invalid-url", False),
            (helpers.is_valid_header_name, "Content-Type", True),
            (helpers.is_valid_header_name, "Invalid Header!", False),
            (helpers.is_valid_json_path, "$.user.id", True),
            (helpers.is_valid_json_path, "invalid", False),
        ]
        
        for validator, value, expected in test_cases:
            result = validator(value)
            if result == expected:
                print(f"  ✓ {validator.__name__}('{value}') = {result}")
            else:
                print(f"  ❌ {validator.__name__}('{value}') = {result}, expected {expected}")
                return False
        
        # Test utility helpers
        if helpers.safe_default("invalid", "default", helpers.is_valid_rate) == "default":
            print("  ✓ safe_default with validation works")
        else:
            print("  ❌ safe_default with validation failed")
            return False
        
        # Test list normalization
        normalized = helpers.normalize_list("a,b,c")
        if normalized == ["a", "b", "c"]:
            print("  ✓ normalize_list works")
        else:
            print(f"  ❌ normalize_list failed: {normalized}")
            return False
        
        # Test environment helpers
        if helpers.feature_enabled("advanced_security"):
            print("  ✓ feature_enabled works")
        else:
            print("  ❌ feature_enabled failed")
            return False
        
        print("✅ Template Helper Functions test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Template Helper Functions test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_conditional_rendering():
    """Test conditional rendering logic in templates"""
    print("\nTesting Conditional Rendering Logic...")
    
    try:
        builder = PolicyBuilder()
        loader = ConfigLoader()
        cfgs = loader.load(system_name="reflect")
        
        # Test with different environments and features
        test_scenarios = [
            {
                "name": "Development Environment",
                "input_vars": {
                    "policy_params": {"SpikeArrest": {"rate": "200pm"}},
                    "features": {"advanced_traffic_management": False}
                },
                "env_name": "dev"
            },
            {
                "name": "Production Environment",
                "input_vars": {
                    "policy_params": {"SpikeArrest": {"rate": "200pm"}},
                    "features": {"advanced_traffic_management": True}
                },
                "env_name": "prod"
            },
            {
                "name": "Feature Flags Enabled",
                "input_vars": {
                    "policy_params": {
                        "CORS": {"allow_origins": ["https://example.com"]},
                        "SpikeArrest": {"rate": "100pm", "identifier_ref": "request.header.client_id"}
                    },
                    "features": {
                        "advanced_traffic_management": True,
                        "security_headers": True
                    }
                },
                "env_name": "prod"
            }
        ]
        
        for scenario in test_scenarios:
            print(f"  Testing: {scenario['name']}")
            
            # Temporarily set environment for testing
            import os
            original_env = os.environ.get('APIGEE_ENV', 'test')
            os.environ['APIGEE_ENV'] = scenario['env_name']
            
            try:
                policies, attach_plan = builder.render_policies(
                    cfgs.system_cfg,
                    scenario['input_vars'],
                    system_name="reflect"
                )
                
                print(f"    ✓ Rendered {len(policies)} policies successfully")
                
                # Check for environment-specific behavior
                for policy in policies:
                    if scenario['env_name'] == 'prod' and 'SpikeArrest' in policy.name:
                        if '50pm' in policy.xml or '200pm' in policy.xml:
                            print("    ✓ Production-specific rate limiting detected")
                    
                    if scenario.get('features', {}).get('security_headers') and 'CORS' in policy.name:
                        if 'X-Content-Type-Options' in policy.xml:
                            print("    ✓ Security headers added with feature flag")

                    if scenario.get('features', {}).get('advanced_traffic_management') and 'SpikeArrest' in policy.name:
                        if 'Identifier' in policy.xml:
                            print("    ✓ Advanced traffic management features enabled")
                
            finally:
                os.environ['APIGEE_ENV'] = original_env
        
        print("✅ Conditional Rendering Logic test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Conditional Rendering Logic test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_template_testing_framework():
    """Test the template testing framework itself"""
    print("\nTesting Template Testing Framework...")
    
    try:
        # Create and run standard test suite
        tester = create_standard_test_suite()
        results = tester.run_all_tests()
        
        print(f"  Executed {len(results)} template tests")
        
        # Check results
        passed = sum(1 for r in results if r.success)
        failed = len(results) - passed
        
        print(f"  ✓ Passed: {passed}")
        if failed > 0:
            print(f"  ❌ Failed: {failed}")
            
            # Show failed test details
            for result in results:
                if not result.success:
                    print(f"    Failed: {result.test_case.name}")
                    if result.error:
                        print(f"      Error: {result.error}")
                    if result.validation_errors:
                        for error in result.validation_errors:
                            print(f"      Validation: {error}")
        
        # Generate report
        report = tester.generate_test_report(results)
        print("\n" + "="*40)
        print("TEMPLATE TEST REPORT PREVIEW:")
        print("="*40)
        print(report[:500] + "..." if len(report) > 500 else report)
        
        # Consider it successful if most tests pass
        success_rate = passed / len(results) if results else 0
        if success_rate >= 0.8:  # 80% success rate threshold
            print("✅ Template Testing Framework test PASSED!")
            return True
        else:
            print(f"❌ Template Testing Framework test FAILED: {success_rate:.1%} success rate")
            return False
        
    except Exception as e:
        print(f"❌ Template Testing Framework test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_enhanced_policy_templates():
    """Test that enhanced policy templates work correctly"""
    print("\nTesting Enhanced Policy Templates...")
    
    try:
        builder = PolicyBuilder()
        
        # Test enhanced SpikeArrest template
        helpers = TemplateHelpers(env_name="prod", features={"advanced_traffic_management": True})
        builder._template_helpers = helpers
        
        spike_arrest_xml = builder._render_template(
            "SpikeArrest.xml.j2",
            name="SA-TestSpikeArrest",
            params={"rate": "invalid_rate", "identifier_ref": "request.header.client_id"},
            input={"methods": ["GET"], "headers": ["Authorization"]},
            env={"name": "prod"}
        )
        
        if "50pm" in spike_arrest_xml:  # Production default
            print("  ✓ SpikeArrest uses production default for invalid rate")
        else:
            print("  ❌ SpikeArrest should use production default")
            return False
        
        if "Identifier" in spike_arrest_xml:  # Advanced feature
            print("  ✓ SpikeArrest includes advanced features when enabled")
        else:
            print("  ❌ SpikeArrest should include advanced features")
            return False
        
        # Test enhanced Quota template
        quota_xml = builder._render_template(
            "Quota.xml.j2",
            name="Q-TestQuota",
            params={"allow": "2000pm", "timeunit": "invalid_unit"},
            input={"methods": ["GET"], "headers": ["Authorization"]},
            env={"name": "prod"}
        )
        
        if "2000pm" in quota_xml:
            print("  ✓ Quota uses provided valid rate")
        else:
            print("  ❌ Quota should use provided rate")
            return False
        
        if "<TimeUnit>minute</TimeUnit>" in quota_xml:
            print("  ✓ Quota falls back to default for invalid timeunit")
        else:
            print("  ❌ Quota should fallback to default timeunit")
            return False
        
        # Test enhanced CORS template
        helpers = TemplateHelpers(env_name="prod", features={"security_headers": True})
        builder._template_helpers = helpers
        
        cors_xml = builder._render_template(
            "AssignMessage.CORS.xml.j2",
            name="AM-TestCORS",
            params={"allow_origins": ["https://example.com"]},
            input={"methods": ["GET"], "headers": ["Authorization"]},
            env={"name": "prod"}
        )
        
        if "https://example.com" in cors_xml:
            print("  ✓ CORS uses provided origins")
        else:
            print("  ❌ CORS should use provided origins")
            return False
        
        if "X-Content-Type-Options" in cors_xml:
            print("  ✓ CORS includes security headers in production")
        else:
            print("  ❌ CORS should include security headers")
            return False
        
        print("✅ Enhanced Policy Templates test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced Policy Templates test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all Phase 3 tests"""
    print("=" * 60)
    print("PHASE 3: SMART POLICY TEMPLATE SYSTEM TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_template_helpers,
        test_conditional_rendering,
        test_enhanced_policy_templates,
        test_template_testing_framework,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PHASE 3 TESTS PASSED!")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
