#!/usr/bin/env python3
"""
Test policy groups integration with policy builder.
This test verifies that policy groups from the catalog can be used
to generate policies correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder


def test_policy_groups_integration():
    """Test that policy groups from catalog work with policy builder."""
    print("============================================================")
    print("TESTING POLICY GROUPS INTEGRATION")
    print("============================================================")
    
    try:
        # Load system configuration
        system_config_path = "app/config/systems/REFLECT_FDID.yaml"
        with open(system_config_path, 'r') as f:
            system_cfg = yaml.safe_load(f)
        
        # Load policy catalog
        catalog_path = "app/config/policy_catalog.yaml"
        with open(catalog_path, 'r') as f:
            catalog = yaml.safe_load(f)
        
        policy_builder = PolicyBuilder()
        
        # Test different policy groups
        groups_to_test = [
            "basic_security",
            "high_security", 
            "cors_enabled",
            "reflect_fdid_standard"
        ]
        
        for group_name in groups_to_test:
            print(f"\n🧪 Testing '{group_name}' group...")
            
            group_config = catalog['groups'].get(group_name, {})
            group_policies = group_config.get('policies', [])
            
            if not group_policies:
                print(f"   ❌ No policies found in group '{group_name}'")
                continue
            
            print(f"   Group policies: {group_policies}")
            
            # Create test configuration using group policies
            test_cfg = {
                "flows": {
                    "proxy": {
                        "preflow": {
                            "request": group_policies[:3],  # Use first 3 policies
                            "response": group_policies[3:4] if len(group_policies) > 3 else []
                        }
                    }
                },
                "policies": {
                    policy: system_cfg['policies'][policy] 
                    for policy in group_policies 
                    if policy in system_cfg['policies']
                }
            }
            
            # Test policy rendering
            try:
                policies, attach_plan = policy_builder.render_policies(
                    system_cfg=test_cfg,
                    input_vars={"policy_params": {}},
                    system_name="test"
                )
                
                print(f"   ✅ Successfully rendered {len(policies)} policies")
                
                # Verify all policies have templates
                template_success = True
                for policy in policies:
                    if policy.xml and len(policy.xml) > 50:  # Basic XML validation
                        print(f"      ✅ {policy.name}: Template rendered ({len(policy.xml)} chars)")
                    else:
                        print(f"      ❌ {policy.name}: Template rendering failed")
                        template_success = False
                
                if not template_success:
                    return False
                
                # Verify attach plan
                total_attached = (
                    len(attach_plan.preflow_request) + 
                    len(attach_plan.preflow_response) +
                    len(attach_plan.perflow_request) + 
                    len(attach_plan.perflow_response)
                )
                print(f"   ✅ Attach plan created with {total_attached} policy attachments")
                
            except Exception as e:
                print(f"   ❌ Policy rendering failed: {e}")
                return False
        
        # Test specific high-security scenario
        print(f"\n🔒 Testing High Security Scenario...")
        high_security_policies = catalog['groups']['high_security']['policies']
        
        # Create a comprehensive test configuration
        comprehensive_cfg = {
            "flows": {
                "proxy": {
                    "preflow": {
                        "request": high_security_policies[:3],
                        "response": ["AM-AddRespHeaders"]
                    },
                    "per_operation": {
                        "request": high_security_policies[3:],
                        "response": ["FC-ExtractCustomLogs"]
                    },
                    "postclientflow": {
                        "response": ["FC-PushLogsToPubSub"]
                    },
                    "nomatchfound": {
                        "request": ["RF-ResourceNotFound"]
                    }
                }
            },
            "policies": system_cfg['policies']
        }
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=comprehensive_cfg,
            input_vars={"policy_params": {}},
            system_name="REFLECT_FDID"
        )
        
        print(f"✅ Comprehensive test: Rendered {len(policies)} policies")
        print(f"   PreFlow Request: {len(attach_plan.preflow_request)} policies")
        print(f"   PreFlow Response: {len(attach_plan.preflow_response)} policies")
        print(f"   PerFlow Request: {len(attach_plan.perflow_request)} policies")
        print(f"   PerFlow Response: {len(attach_plan.perflow_response)} policies")
        print(f"   PostClientFlow Response: {len(attach_plan.postclientflow_response)} policies")
        print(f"   NoMatchFound Request: {len(attach_plan.nomatchfound_request)} policies")
        
        # Verify specific high-security policies are included
        all_policy_names = [p.name for p in policies]
        high_security_found = [p for p in high_security_policies if p in all_policy_names]
        
        print(f"✅ High security policies found: {len(high_security_found)}/{len(high_security_policies)}")
        for policy in high_security_found:
            print(f"   ✅ {policy}")
        
        print("\n============================================================")
        print("✅ POLICY GROUPS INTEGRATION TEST PASSED!")
        print("Policy catalog groups work correctly with policy builder.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_policy_groups_integration()
    sys.exit(0 if success else 1)
