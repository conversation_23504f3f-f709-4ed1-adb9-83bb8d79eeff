#!/usr/bin/env python3
"""
Test template-based policy builder functionality.
This test verifies that the policy builder works correctly with the new
template-based structure and actual policy names from system configuration.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder


def test_template_based_policy_builder():
    """Test that policy builder works with template-based structure and actual policy names."""
    print("============================================================")
    print("TESTING TEMPLATE-BASED POLICY BUILDER")
    print("============================================================")
    
    try:
        # Create test system configuration with actual policy names and templates
        test_system_cfg = {
            "flows": {
                "proxy": {
                    "preflow": {
                        "request": ["VK-VerifyApiKey", "JTP-JSONThreatProtection", "SA-SpikeArrest"],
                        "response": ["AM-AddRespHeaders"]
                    },
                    "per_operation": {
                        "request": ["Q-ImposeQuota"],
                        "response": ["FC-ExtractCustomLogs"]
                    },
                    "postclientflow": {
                        "response": ["FC-PushLogsToPubSub"]
                    },
                    "nomatchfound": {
                        "request": ["RF-ResourceNotFound"]
                    }
                }
            },
            "policies": {
                # Security policies
                "VK-VerifyApiKey": {
                    "template": "VerifyApiKey.xml.j2",
                    "display_name": "VK-VerifyApiKey",
                    "keyref": "request.header.x-api-key"
                },
                
                # Threat protection policies
                "JTP-JSONThreatProtection": {
                    "template": "JSONThreatProtection.xml.j2",
                    "display_name": "JTP-JSONThreatProtection",
                    "source": "request",
                    "container_depth": 32,
                    "object_entry_count": 100
                },
                
                "XTP-XMLThreatProtection": {
                    "template": "XMLThreatProtection.xml.j2",
                    "display_name": "XTP-XMLThreatProtection",
                    "source": "request",
                    "element_depth": 32,
                    "attribute_count_per_element": 100
                },
                
                # Traffic management policies
                "SA-SpikeArrest": {
                    "template": "SpikeArrest.xml.j2",
                    "display_name": "SA-SpikeArrest",
                    "rate": "100pm"
                },
                
                "Q-ImposeQuota": {
                    "template": "Quota.xml.j2",
                    "display_name": "Q-ImposeQuota",
                    "allow": 1000,
                    "interval": 1,
                    "time_unit": "hour"
                },
                
                # Message transformation policies
                "AM-AddRespHeaders": {
                    "template": "AssignMessage.xml.j2",
                    "display_name": "AM-AddRespHeaders",
                    "add": {
                        "headers": [
                            {"name": "X-Response-Time", "value": "{system.timestamp}"},
                            {"name": "X-API-Version", "value": "v1.0"}
                        ]
                    }
                },
                
                # Integration policies
                "FC-ExtractCustomLogs": {
                    "template": "FlowCallout.xml.j2",
                    "display_name": "FC-ExtractCustomLogs",
                    "shared_flow": "extract-custom-logs"
                },
                
                "FC-PushLogsToPubSub": {
                    "template": "FlowCallout.xml.j2",
                    "display_name": "FC-PushLogsToPubSub",
                    "shared_flow": "push-logs-pubsub"
                },
                
                # Error handling policies
                "RF-ResourceNotFound": {
                    "template": "RaiseFault.xml.j2",
                    "display_name": "RF-ResourceNotFound",
                    "status_code": "404",
                    "reason_phrase": "Resource Not Found",
                    "payload": '{"error": "Resource not found"}'
                },

                # Additional conditional policies that might be attached
                "REP-RegularExpressionProtection": {
                    "template": "RegularExpressionProtection.xml.j2",
                    "display_name": "REP-RegularExpressionProtection",
                    "source": "request"
                },

                "RC-ResponseCache": {
                    "template": "ResponseCache.xml.j2",
                    "display_name": "RC-ResponseCache",
                    "cache_resource": "response-cache",
                    "scope": "environment"
                },

                "FC-EnhancedMonitoring": {
                    "template": "FlowCallout.xml.j2",
                    "display_name": "FC-EnhancedMonitoring",
                    "shared_flow": "enhanced-monitoring"
                },

                "KVM-DynamicConfig": {
                    "template": "KeyValueMapOperations.xml.j2",
                    "display_name": "KVM-DynamicConfig",
                    "map_name": "dynamic-config",
                    "scope": "environment"
                }
            }
        }
        
        # Test input variables with policy parameters
        test_input_vars = {
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],
            "content_types": ["application/json"],
            "policy_params": {
                # Override some policy parameters
                "SA-SpikeArrest": {
                    "rate": "200pm"  # Override default rate
                },
                "Q-ImposeQuota": {
                    "allow": 2000  # Override default quota
                }
            }
        }
        
        # Test policy builder
        print("🏗️ Testing Policy Builder with Template-Based Configuration...")
        policy_builder = PolicyBuilder()
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=test_system_cfg,
            input_vars=test_input_vars,
            system_name="REFLECT_FDID"
        )
        
        print(f"   ✅ Policy Builder: Rendered {len(policies)} policies")
        
        # Verify all expected policies are rendered
        expected_policies = [
            "VK-VerifyApiKey", "JTP-JSONThreatProtection", "SA-SpikeArrest",
            "AM-AddRespHeaders", "Q-ImposeQuota", "FC-ExtractCustomLogs",
            "FC-PushLogsToPubSub", "RF-ResourceNotFound"
        ]
        
        rendered_policy_names = [p.name for p in policies]
        
        for expected_policy in expected_policies:
            if expected_policy in rendered_policy_names:
                print(f"      ✅ {expected_policy}: Found in rendered policies")
            else:
                print(f"      ❌ {expected_policy}: Missing from rendered policies")
                return False
        
        # Verify templates were resolved correctly
        for policy in policies:
            if policy.xml and len(policy.xml) > 50:
                print(f"      ✅ {policy.name}: Template rendered successfully ({len(policy.xml)} chars)")
            else:
                print(f"      ❌ {policy.name}: Template rendering failed")
                return False
        
        # Test attach plan structure
        print(f"\n📋 Testing Attach Plan Structure...")
        print(f"   PreFlow Request: {len(attach_plan.preflow_request)} policies")
        print(f"   PreFlow Response: {len(attach_plan.preflow_response)} policies")
        print(f"   PerFlow Request: {len(attach_plan.perflow_request)} policies")
        print(f"   PerFlow Response: {len(attach_plan.perflow_response)} policies")
        print(f"   PostClientFlow Response: {len(attach_plan.postclientflow_response)} policies")
        print(f"   NoMatchFound Request: {len(attach_plan.nomatchfound_request)} policies")
        
        # Verify attach plan has correct policies
        if len(attach_plan.preflow_request) >= 3:
            print(f"      ✅ PreFlow Request: Contains expected policies")
        else:
            print(f"      ❌ PreFlow Request: Expected at least 3 policies, got {len(attach_plan.preflow_request)}")
            return False
        
        # Test feature flag integration with template-based policies
        print(f"\n🚩 Testing Feature Flag Integration...")
        
        # Test with content types that should include JSON threat protection
        json_input_vars = {
            **test_input_vars,
            "content_types": ["application/json", "application/xml"]
        }
        
        policies_with_json, _ = policy_builder.render_policies(
            system_cfg=test_system_cfg,
            input_vars=json_input_vars,
            system_name="REFLECT_FDID"
        )
        
        json_policy_names = [p.name for p in policies_with_json]
        if "JTP-JSONThreatProtection" in json_policy_names:
            print(f"      ✅ JSON Threat Protection: Included with JSON content types")
        else:
            print(f"      ⚠️  JSON Threat Protection: Not found (may be filtered by feature flags)")
        
        # Test parameter override functionality
        print(f"\n🔧 Testing Parameter Override...")
        
        # Find the SpikeArrest policy and check if rate was overridden
        spike_arrest_policy = next((p for p in policies if p.name == "SA-SpikeArrest"), None)
        if spike_arrest_policy and "200pm" in spike_arrest_policy.xml:
            print(f"      ✅ Parameter Override: SpikeArrest rate successfully overridden to 200pm")
        else:
            print(f"      ⚠️  Parameter Override: Could not verify rate override")
        
        # Test template-based validation
        print(f"\n🔍 Testing Template-Based Validation...")
        
        # Test valid parameters
        is_valid, errors = policy_builder.validate_policy_params(
            "VK-VerifyApiKey", 
            {"keyref": "request.header.x-api-key"}, 
            template_name="VerifyApiKey.xml.j2"
        )
        
        if is_valid:
            print(f"      ✅ Template Validation: Valid parameters accepted")
        else:
            print(f"      ❌ Template Validation: Valid parameters rejected - {errors}")
        
        print("\n============================================================")
        print("✅ TEMPLATE-BASED POLICY BUILDER TEST PASSED!")
        print("Policy builder works correctly with template-based structure.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_template_based_policy_builder()
    sys.exit(0 if success else 1)
