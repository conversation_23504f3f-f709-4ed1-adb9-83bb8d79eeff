#!/usr/bin/env python3
"""
Test template-based policy catalog functionality.
This test verifies that the policy catalog works correctly with the new
template-based structure instead of system-specific policy names.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_catalog import get_policy_catalog
from app.services.policy_builder import PolicyBuilder


def test_template_based_catalog():
    """Test that policy catalog works with template-based structure."""
    print("============================================================")
    print("TESTING TEMPLATE-BASED POLICY CATALOG")
    print("============================================================")
    
    try:
        # Load policy catalog
        catalog = get_policy_catalog()
        
        # Test template-based groups
        print("🧪 Testing Template-Based Groups...")
        groups = catalog.get_groups()
        
        for group_name, group_info in groups.items():
            group_type = group_info.get("type", "unknown")
            templates = group_info.get("templates", [])
            
            print(f"   Group '{group_name}' ({group_type}): {len(templates)} templates")
            
            if templates:
                # Verify templates exist in the templates section
                for template in templates[:3]:  # Show first 3 templates
                    template_info = catalog.get_template_metadata(template)
                    if template_info:
                        print(f"      ✅ {template}: Found in catalog")
                    else:
                        print(f"      ⚠️  {template}: Not found in templates section")
        
        # Test specific template groups
        print(f"\n🔒 Testing High Security Template Set...")
        high_security_templates = catalog.get_group_templates("high_security")
        print(f"High Security templates: {high_security_templates}")
        
        for template in high_security_templates:
            template_info = catalog.get_template_metadata(template)
            if template_info:
                required_params = catalog.get_template_required_params(template)
                optional_params = catalog.get_template_optional_params(template)
                print(f"   ✅ {template}: {len(required_params)} required, {len(optional_params)} optional params")
            else:
                print(f"   ❌ {template}: Template metadata not found")
        
        # Test template validation
        print(f"\n🧪 Testing Template Validation...")
        test_template = "VerifyApiKey.xml.j2"
        test_params = {"keyref": "request.header.x-api-key", "display_name": "VK-Test"}
        
        is_valid, errors = catalog.validate_template_params(test_template, test_params)
        if is_valid:
            print(f"   ✅ {test_template}: Parameters validated successfully")
        else:
            print(f"   ❌ {test_template}: Validation failed - {errors}")
        
        # Test invalid parameters
        invalid_params = {"invalid_param": "value"}
        is_valid, errors = catalog.validate_template_params(test_template, invalid_params)
        if not is_valid:
            print(f"   ✅ {test_template}: Invalid parameters correctly rejected")
        else:
            print(f"   ❌ {test_template}: Should have rejected invalid parameters")
        
        # Test policy builder with template-based catalog
        print(f"\n🏗️ Testing Policy Builder Integration...")
        policy_builder = PolicyBuilder()
        
        # Create test configuration using templates
        test_cfg = {
            "flows": {
                "proxy": {
                    "preflow": {
                        "request": ["VK-VerifyApiKey", "SA-SpikeArrest"],
                        "response": ["AM-AddRespHeaders"]
                    }
                }
            },
            "policies": {
                "VK-VerifyApiKey": {
                    "template": "VerifyApiKey.xml.j2",
                    "keyref": "request.header.x-api-key"
                },
                "SA-SpikeArrest": {
                    "template": "SpikeArrest.xml.j2",
                    "rate": "100pm"
                },
                "AM-AddRespHeaders": {
                    "template": "AssignMessage.xml.j2",
                    "add": {
                        "headers": [
                            {"name": "X-Response-Time", "value": "{system.timestamp}"}
                        ]
                    }
                }
            }
        }
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=test_cfg,
            input_vars={"policy_params": {}},
            system_name="test"
        )
        
        print(f"   ✅ Policy Builder: Rendered {len(policies)} policies")
        
        # Verify templates were resolved correctly
        for policy in policies:
            if policy.xml and len(policy.xml) > 50:
                print(f"      ✅ {policy.name}: Template rendered successfully")
            else:
                print(f"      ❌ {policy.name}: Template rendering failed")
                return False
        
        # Test template-based parameter validation
        print(f"\n🔍 Testing Template-Based Parameter Validation...")
        
        # Test valid parameters
        is_valid, errors = policy_builder.validate_policy_params(
            "VK-VerifyApiKey", 
            {"keyref": "request.header.x-api-key"}, 
            template_name="VerifyApiKey.xml.j2"
        )
        
        if is_valid:
            print(f"   ✅ Template-based validation: Valid parameters accepted")
        else:
            print(f"   ❌ Template-based validation failed: {errors}")
        
        # Test categories and template sets
        print(f"\n📂 Testing Categories and Template Sets...")
        
        categories = catalog.get_categories()
        template_sets = catalog.get_templates()
        
        print(f"   Categories: {len(categories)}")
        for cat_name in list(categories.keys())[:3]:
            templates = catalog.get_templates_by_category(cat_name)
            print(f"      {cat_name}: {len(templates)} templates")
        
        print(f"   Template Sets: {len(template_sets)}")
        for set_name in list(template_sets.keys())[:3]:
            templates = catalog.get_group_templates(set_name)
            print(f"      {set_name}: {len(templates)} templates")
        
        print("\n============================================================")
        print("✅ TEMPLATE-BASED POLICY CATALOG TEST PASSED!")
        print("Policy catalog works correctly with template-based structure.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_template_based_catalog()
    sys.exit(0 if success else 1)
