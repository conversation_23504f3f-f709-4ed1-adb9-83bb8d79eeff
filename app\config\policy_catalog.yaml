# Optimized Policy Catalog - Template-Based Structure
# Single source of truth for policy metadata and parameter merge semantics
# Templates are now the primary keys, eliminating policy type abstraction
# Default values are maintained in: templates/*.xml.j2, system.yaml, and env.yaml

# Template definitions with metadata and parameter merge semantics
templates:
  # Security & Authentication Templates
  "VerifyApiKey.xml.j2":
    name: "API Key Verification"
    description: "Verifies API key from request headers, query parameters, or form parameters"
    required_params: ["keyref"]
    optional_params: ["display_name"]
    param_config:
      keyref: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "OAuthV2.xml.j2":
    name: "OAuth 2.0 Verification"
    description: "Verifies OAuth 2.0 access tokens"
    required_params: []
    optional_params: ["token_ref", "scope", "remove_prefix", "display_name"]
    param_config:
      token_ref: {"type": "string", "merge": "replace"}
      scope: {"type": "string", "merge": "replace"}
      remove_prefix: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "BasicAuthentication.xml.j2":
    name: "Basic Authentication"
    description: "Handles HTTP Basic Authentication encoding/decoding"
    required_params: []
    optional_params: ["operation", "source", "username_variable", "password_variable", "assign_to", "ignore_unresolved", "display_name"]
    param_config:
      operation: {"type": "string", "merge": "replace"}
      source: {"type": "string", "merge": "replace"}
      username_variable: {"type": "string", "merge": "replace"}
      password_variable: {"type": "string", "merge": "replace"}
      assign_to: {"type": "string", "merge": "replace"}
      ignore_unresolved: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Traffic Management Templates
  "SpikeArrest.xml.j2":
    name: "Spike Arrest"
    description: "Protects against traffic spikes by smoothing request rates"
    required_params: ["rate"]
    optional_params: ["message_weight_ref", "identifier_ref", "display_name"]
    param_config:
      rate: {"type": "string", "merge": "replace", "validator": "is_valid_rate"}
      message_weight_ref: {"type": "string", "merge": "replace"}
      identifier_ref: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "Quota.xml.j2":
    name: "Quota Management"
    description: "Enforces consumption limits on client apps"
    required_params: ["allow"]
    optional_params: ["interval", "timeunit", "identifier_ref", "distributed", "synchronous", "display_name"]
    param_config:
      allow: {"type": "string", "merge": "replace", "validator": "is_valid_rate"}
      interval: {"type": "string", "merge": "replace"}
      timeunit: {"type": "string", "merge": "replace"}
      identifier_ref: {"type": "string", "merge": "replace"}
      distributed: {"type": "bool", "merge": "replace"}
      synchronous: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # CORS & Messaging Templates
  "AssignMessage.CORS.xml.j2":
    name: "CORS Headers"
    description: "Handles Cross-Origin Resource Sharing (CORS) headers"
    required_params: []
    optional_params: ["allow_origins", "allow_methods", "allow_headers", "expose_headers", "max_age", "allow_credentials", "display_name"]
    param_config:
      allow_origins: {"type": "list", "merge": "replace", "normalize": "exact"}
      allow_methods: {"type": "list", "merge": "append_unique", "normalize": "exact"}
      allow_headers: {"type": "list", "merge": "append_unique", "normalize": "lower"}
      expose_headers: {"type": "list", "merge": "append_unique", "normalize": "exact"}
      max_age: {"type": "int", "merge": "replace"}
      allow_credentials: {"type": "bool", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Threat Protection Templates
  "JSONThreatProtection.xml.j2":
    name: "JSON Threat Protection"
    description: "Protects against malicious JSON payloads"
    required_params: []
    optional_params: ["source", "display_name", "array_element_count", "container_depth", "object_entry_count", "object_entry_name_length", "string_value_length"]
    param_config:
      source: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}
      array_element_count: {"type": "int", "merge": "replace"}
      container_depth: {"type": "int", "merge": "replace"}
      object_entry_count: {"type": "int", "merge": "replace"}
      object_entry_name_length: {"type": "int", "merge": "replace"}
      string_value_length: {"type": "int", "merge": "replace"}

  "XMLThreatProtection.xml.j2":
    name: "XML Threat Protection"
    description: "Protects against malicious XML payloads"
    required_params: []
    optional_params: ["source", "display_name", "node_depth", "attribute_count_per_element", "namespace_count_per_element", "child_count", "text_value_length", "attribute_value_length", "namespace_uri_length", "comment_length", "processing_instruction_data_length", "element_name_length", "attribute_name_length", "namespace_prefix_length", "processing_instruction_target_length"]
    param_config:
      source: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}
      # StructureLimits parameters
      node_depth: {"type": "int", "merge": "replace"}
      attribute_count_per_element: {"type": "int", "merge": "replace"}
      namespace_count_per_element: {"type": "int", "merge": "replace"}
      child_count: {"type": "int", "merge": "replace"}
      # ValueLimits parameters
      text_value_length: {"type": "int", "merge": "replace"}
      attribute_value_length: {"type": "int", "merge": "replace"}
      namespace_uri_length: {"type": "int", "merge": "replace"}
      comment_length: {"type": "int", "merge": "replace"}
      processing_instruction_data_length: {"type": "int", "merge": "replace"}
      # NameLimits parameters
      element_name_length: {"type": "int", "merge": "replace"}
      attribute_name_length: {"type": "int", "merge": "replace"}
      namespace_prefix_length: {"type": "int", "merge": "replace"}
      processing_instruction_target_length: {"type": "int", "merge": "replace"}

  # Message Processing Templates
  "AssignMessage.xml.j2":
    name: "Assign Message"
    description: "Modifies request or response messages by adding, copying, removing, or setting headers, query parameters, form parameters, or payload"
    required_params: []
    optional_params: ["display_name", "enabled", "continueOnError", "async", "ignore_unresolved_variables", "assign_to", "add", "copy", "remove", "set"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      enabled: {"type": "bool", "merge": "replace"}
      continueOnError: {"type": "bool", "merge": "replace"}
      async: {"type": "bool", "merge": "replace"}
      ignore_unresolved_variables: {"type": "bool", "merge": "replace"}
      assign_to: {"type": "dict", "merge": "replace"}
      add: {"type": "dict", "merge": "replace"}
      copy: {"type": "dict", "merge": "replace"}
      remove: {"type": "dict", "merge": "replace"}
      set: {"type": "dict", "merge": "replace"}

  # Script Templates
  "JavaScript.xml.j2":
    name: "JavaScript"
    description: "Executes custom JavaScript code to implement custom logic"
    required_params: []
    optional_params: ["display_name", "enabled", "continueOnError", "async", "timeLimit", "properties", "resource_url", "source_code", "include_url"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      enabled: {"type": "bool", "merge": "replace"}
      continueOnError: {"type": "bool", "merge": "replace"}
      async: {"type": "bool", "merge": "replace"}
      timeLimit: {"type": "int", "merge": "replace"}
      properties: {"type": "list", "merge": "replace"}
      resource_url: {"type": "string", "merge": "replace"}
      source_code: {"type": "string", "merge": "replace"}
      include_url: {"type": "string", "merge": "replace"}

  # Service Integration Templates
  "ServiceCallout.xml.j2":
    name: "Service Callout"
    description: "Makes HTTP calls to external services from within an API proxy flow"
    required_params: []
    optional_params: ["display_name", "enabled", "continueOnError", "async", "request", "response", "timeout", "http_target_connection", "local_target_connection"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      enabled: {"type": "bool", "merge": "replace"}
      continueOnError: {"type": "bool", "merge": "replace"}
      async: {"type": "bool", "merge": "replace"}
      request: {"type": "dict", "merge": "replace"}
      response: {"type": "dict", "merge": "replace"}
      timeout: {"type": "int", "merge": "replace"}
      http_target_connection: {"type": "dict", "merge": "replace"}
      local_target_connection: {"type": "dict", "merge": "replace"}

  # Flow Control Templates
  "FlowCallout.xml.j2":
    name: "Flow Callout"
    description: "Calls a shared flow from within an API proxy flow"
    required_params: []
    optional_params: ["display_name", "shared_flow", "parameters"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      shared_flow: {"type": "string", "merge": "replace"}
      parameters: {"type": "list", "merge": "replace"}

  # Data Extraction Templates
  "ExtractVariables.xml.j2":
    name: "Extract Variables"
    description: "Extracts variables from request or response messages using JSONPath, XPath, or regular expressions"
    required_params: []
    optional_params: ["display_name", "source", "ignore_unresolved", "json", "xml", "form_params", "headers", "regex"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      source: {"type": "string", "merge": "replace"}
      ignore_unresolved: {"type": "bool", "merge": "replace"}
      json: {"type": "list", "merge": "replace"}
      xml: {"type": "list", "merge": "replace"}
      form_params: {"type": "list", "merge": "replace"}
      headers: {"type": "list", "merge": "replace"}
      regex: {"type": "dict", "merge": "replace"}

  # Error Handling Templates
  "RaiseFault.xml.j2":
    name: "Raise Fault"
    description: "Raises custom faults and errors with configurable status codes and messages"
    required_params: []
    optional_params: ["display_name", "enabled", "continueOnError", "async", "assign_to", "headers", "payload", "status_code", "reason_phrase", "content_type", "ignore_unresolved_variables"]
    param_config:
      display_name: {"type": "string", "merge": "replace"}
      enabled: {"type": "bool", "merge": "replace"}
      continueOnError: {"type": "bool", "merge": "replace"}
      async: {"type": "bool", "merge": "replace"}
      assign_to: {"type": "dict", "merge": "replace"}
      headers: {"type": "list", "merge": "replace"}
      payload: {"type": "string", "merge": "replace"}
      status_code: {"type": "string", "merge": "replace"}
      reason_phrase: {"type": "string", "merge": "replace"}
      content_type: {"type": "string", "merge": "replace"}
      ignore_unresolved_variables: {"type": "bool", "merge": "replace"}

  # Caching Templates
  "ResponseCache.xml.j2":
    name: "Response Cache"
    description: "Caches responses to improve performance"
    required_params: []
    optional_params: ["cache_resource", "scope", "expiry_seconds", "display_name"]
    param_config:
      cache_resource: {"type": "string", "merge": "replace"}
      scope: {"type": "string", "merge": "replace"}
      expiry_seconds: {"type": "int", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "PopulateCache.xml.j2":
    name: "Populate Cache"
    description: "Populates cache with data"
    required_params: []
    optional_params: ["cache_resource", "cache_key", "expiry_seconds", "display_name"]
    param_config:
      cache_resource: {"type": "string", "merge": "replace"}
      cache_key: {"type": "string", "merge": "replace"}
      expiry_seconds: {"type": "int", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "LookupCache.xml.j2":
    name: "Lookup Cache"
    description: "Looks up data from cache"
    required_params: []
    optional_params: ["cache_resource", "cache_key", "assign_to", "display_name"]
    param_config:
      cache_resource: {"type": "string", "merge": "replace"}
      cache_key: {"type": "string", "merge": "replace"}
      assign_to: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "InvalidateCache.xml.j2":
    name: "Invalidate Cache"
    description: "Invalidates cached data"
    required_params: []
    optional_params: ["cache_resource", "cache_key", "display_name"]
    param_config:
      cache_resource: {"type": "string", "merge": "replace"}
      cache_key: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  # Utility Templates
  "KeyValueMapOperations.xml.j2":
    name: "Key Value Map Operations"
    description: "Performs operations on key-value maps"
    required_params: []
    optional_params: ["map_identifier", "operation", "key", "value", "display_name"]
    param_config:
      map_identifier: {"type": "string", "merge": "replace"}
      operation: {"type": "string", "merge": "replace"}
      key: {"type": "string", "merge": "replace"}
      value: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "AccessControl.xml.j2":
    name: "Access Control"
    description: "Controls access to resources"
    required_params: []
    optional_params: ["action", "source_address", "display_name"]
    param_config:
      action: {"type": "string", "merge": "replace"}
      source_address: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "HMAC.xml.j2":
    name: "HMAC"
    description: "Generates or verifies HMAC signatures"
    required_params: []
    optional_params: ["algorithm", "secret_key", "message", "display_name"]
    param_config:
      algorithm: {"type": "string", "merge": "replace"}
      secret_key: {"type": "string", "merge": "replace"}
      message: {"type": "string", "merge": "replace"}
      display_name: {"type": "string", "merge": "replace"}

  "RegularExpressionProtection.xml.j2":
    name: "Regular Expression Protection"
    description: "Protects against malicious regular expressions"
    required_params: []
    optional_params: ["source", "patterns", "display_name"]
    param_config:
      source: {"type": "string", "merge": "replace"}
      patterns: {"type": "list", "merge": "append"}
      display_name: {"type": "string", "merge": "replace"}

  # Additional templates can be added here following the same pattern

# Policy groups - consolidates categories and templates into single grouping mechanism
# Groups can be used for UI organization, bulk operations, or pre-configured policy sets
groups:
  # Functional Categories (for UI organization)
  security:
    name: "Security & Authentication"
    description: "Policies for authentication, authorization, and security"
    icon: "shield"
    type: "category"
    templates:
      - "VerifyApiKey.xml.j2"
      - "OAuthV2.xml.j2"
      - "BasicAuthentication.xml.j2"
      - "DecodeJWT.xml.j2"
      - "VerifyJWT.xml.j2"
      - "GenerateJWT.xml.j2"

  traffic_management:
    name: "Traffic Management"
    description: "Policies for rate limiting, quotas, and traffic control"
    icon: "traffic-light"
    type: "category"
    templates:
      - "SpikeArrest.xml.j2"
      - "Quota.xml.j2"

  message_transformation:
    name: "Message Transformation"
    description: "Policies for transforming request/response messages"
    icon: "message-circle"
    type: "category"
    templates:
      - "AssignMessage.xml.j2"
      - "AssignMessage.CORS.xml.j2"
      - "ExtractVariables.xml.j2"
      - "JSONToXML.xml.j2"
      - "XMLToJSON.xml.j2"
      - "XSLTransform.xml.j2"

  threat_protection:
    name: "Threat Protection"
    description: "Policies for protecting against malicious requests"
    icon: "shield-alert"
    type: "category"
    templates:
      - "JSONThreatProtection.xml.j2"
      - "XMLThreatProtection.xml.j2"
      - "RegularExpressionProtection.xml.j2"

  integration:
    name: "Integration & Callouts"
    description: "Policies for external service integration and shared flows"
    icon: "link"
    type: "category"
    templates:
      - "FlowCallout.xml.j2"
      - "ServiceCallout.xml.j2"
      - "JavaScript.xml.j2"

  error_handling:
    name: "Error Handling"
    description: "Policies for error responses and fault handling"
    icon: "alert-triangle"
    type: "category"
    templates:
      - "RaiseFault.xml.j2"

  caching:
    name: "Caching & Performance"
    description: "Policies for response caching and performance optimization"
    icon: "database"
    type: "category"
    templates:
      - "ResponseCache.xml.j2"
      - "PopulateCache.xml.j2"
      - "LookupCache.xml.j2"
      - "InvalidateCache.xml.j2"

  utilities:
    name: "Utilities & Operations"
    description: "Utility policies for various operations"
    icon: "tool"
    type: "category"
    templates:
      - "KeyValueMapOperations.xml.j2"
      - "AccessControl.xml.j2"
      - "HMAC.xml.j2"

  # Pre-configured Template Sets (for common use cases)
  basic_security:
    name: "Basic API Security"
    description: "Standard security setup with API key verification and rate limiting"
    icon: "shield-check"
    type: "template_set"
    templates:
      - "VerifyApiKey.xml.j2"
      - "SpikeArrest.xml.j2"
      - "Quota.xml.j2"

  cors_enabled:
    name: "CORS-Enabled API"
    description: "API with CORS support for web applications"
    icon: "globe"
    type: "template_set"
    templates:
      - "VerifyApiKey.xml.j2"
      - "AssignMessage.CORS.xml.j2"
      - "SpikeArrest.xml.j2"

  high_security:
    name: "High Security API"
    description: "Enhanced security with threat protection"
    icon: "shield-alert"
    type: "template_set"
    templates:
      - "VerifyApiKey.xml.j2"
      - "JSONThreatProtection.xml.j2"
      - "XMLThreatProtection.xml.j2"
      - "SpikeArrest.xml.j2"
      - "Quota.xml.j2"

  comprehensive_api:
    name: "Comprehensive API"
    description: "Full-featured API with security, transformation, and monitoring"
    icon: "layers"
    type: "template_set"
    templates:
      - "VerifyApiKey.xml.j2"
      - "FlowCallout.xml.j2"
      - "SpikeArrest.xml.j2"
      - "AssignMessage.xml.j2"
      - "Quota.xml.j2"
      - "ExtractVariables.xml.j2"
      - "RaiseFault.xml.j2"

# Note: Default parameter values are maintained in:
# 1. Template files (*.xml.j2) - Template fallback defaults
# 2. System configuration (systems/*.yaml) - System-wide defaults
# 3. Environment configuration (env/*/*.yaml) - Environment-specific overrides
# This eliminates duplication and provides clear precedence: TEMPLATE < SYSTEM < ENV < INPUT
