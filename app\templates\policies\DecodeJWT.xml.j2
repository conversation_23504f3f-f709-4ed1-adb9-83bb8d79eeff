<DecodeJWT name="{{ name }}">
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}     <!-- variable containing JWT -->
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.claims_prefix %}<ClaimsPrefix>{{ params.claims_prefix }}</ClaimsPrefix>{% endif %}
  {% if params.header_prefix %}<HeaderPrefix>{{ params.header_prefix }}</HeaderPrefix>{% endif %}
</DecodeJWT>