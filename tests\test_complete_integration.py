#!/usr/bin/env python3
"""
Complete integration test for enhanced endpoint configurations
"""

import sys
import os
import yaml
from pathlib import Path

# Add the app directory to the Python path
project_root = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, project_root)

from app.services.policy_builder import PolicyBuilder
from app.services.target_builder import TargetBuilder
from app.services.flow_builder import FlowBuilder


def test_complete_integration():
    """Test complete integration of all enhanced components"""
    print("Testing Complete Integration...")
    
    # Load the REFLECT_FDID system configuration
    config_path = Path(__file__).parent.parent / "app" / "config" / "systems" / "REFLECT_FDID.yaml"
    
    with open(config_path, 'r') as f:
        system_cfg = yaml.safe_load(f)
    
    success = True
    
    try:
        # 1. Test PolicyBuilder with new structure
        print("\n1. Testing PolicyBuilder...")
        policy_builder = PolicyBuilder()
        
        input_vars = {"policy_params": {}}
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=system_cfg,
            input_vars=input_vars,
            system_name="REFLECT_FDID"
        )
        
        print(f"   ✅ Rendered {len(policies)} policies")
        print(f"   ✅ Attach plan: PreFlow={len(attach_plan.preflow_request)}, PerFlow={len(attach_plan.perflow_request)}")
        
        # 2. Test TargetBuilder with multiple targets
        print("\n2. Testing TargetBuilder...")
        target_builder = TargetBuilder()
        
        # Test default target
        default_xml = target_builder.build_target_endpoint_xml(
            system_cfg=system_cfg,
            endpoint_name="default"
        )
        print(f"   ✅ Built default target: {len(default_xml)} chars")
        
        # Test default-jo target
        jo_xml = target_builder.build_target_endpoint_xml(
            system_cfg=system_cfg,
            endpoint_name="default-jo"
        )
        print(f"   ✅ Built default-jo target: {len(jo_xml)} chars")
        
        # Verify different URLs
        if "payments.internal.example.com" in default_xml and "payments-jo.internal.example.com" in jo_xml:
            print("   ✅ Different target URLs correctly configured")
        else:
            print("   ❌ Target URLs not correctly configured")
            success = False
        
        # 3. Test FlowBuilder with new structure
        print("\n3. Testing FlowBuilder...")
        flow_builder = FlowBuilder()
        
        # Mock OAS content
        mock_oas = {
            "openapi": "3.0.0",
            "info": {"title": "Test API", "version": "1.0.0"},
            "servers": [{"url": "https://api.example.com/v1"}],
            "paths": {
                "/test": {
                    "get": {
                        "operationId": "getTest",
                        "responses": {"200": {"description": "Success"}}
                    }
                }
            }
        }
        
        proxy_xml = flow_builder.build_proxy_endpoint_xml(
            oas_content=mock_oas,
            attach_plan=attach_plan,
            system_cfg=system_cfg,
            proxy_endpoint_name="default",
            target_endpoint_name="default",
            virtual_host="default"
        )
        
        print(f"   ✅ Built proxy endpoint: {len(proxy_xml)} chars")
        
        # Verify proxy contains expected elements
        expected_elements = ["<ProxyEndpoint", "<PreFlow", "<PostFlow", "<PostClientFlow"]
        missing_elements = []
        for element in expected_elements:
            if element not in proxy_xml:
                missing_elements.append(element)
        
        if not missing_elements:
            print("   ✅ Proxy XML contains all expected elements")
        else:
            print(f"   ❌ Proxy XML missing elements: {missing_elements}")
            success = False
        
        # 4. Test configuration structure validation
        print("\n4. Testing Configuration Structure...")
        
        # Check targets structure
        targets = system_cfg.get("targets", {})
        if len(targets) >= 2:
            print(f"   ✅ Found {len(targets)} target configurations")
        else:
            print(f"   ❌ Expected at least 2 targets, found {len(targets)}")
            success = False
        
        # Check proxy flows structure
        flows = system_cfg.get("flows", {})
        proxy_flows = flows.get("proxy", {})
        expected_flows = ["preflow", "per_operation", "postflow", "postclientflow", "nomatchfound"]
        missing_flows = [f for f in expected_flows if f not in proxy_flows]
        
        if not missing_flows:
            print("   ✅ All expected proxy flows are configured")
        else:
            print(f"   ❌ Missing proxy flows: {missing_flows}")
            success = False
        
        # Check policies structure
        policies_cfg = system_cfg.get("policies", {})
        if len(policies_cfg) > 0:
            print(f"   ✅ Found {len(policies_cfg)} policy definitions")
        else:
            print("   ❌ No policy definitions found")
            success = False
        
        # 5. Test clean code architecture principles
        print("\n5. Testing Clean Code Architecture...")

        # Test modular design - services are properly separated
        # (Already imported at module level)

        # Test comprehensive error handling
        try:
            invalid_cfg = {"invalid": "config"}
            target_builder.build_target_endpoint_xml(
                system_cfg=invalid_cfg,
                endpoint_name="nonexistent"
            )
            print("   ❌ Error handling not working")
            success = False
        except Exception:
            print("   ✅ Comprehensive error handling works")

        # Test structured logging is available
        import structlog
        logger = structlog.get_logger()
        print("   ✅ Structured logging configured")
        
        return success
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("=" * 60)
    print("COMPLETE INTEGRATION TEST")
    print("=" * 60)
    
    success = test_complete_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ COMPLETE INTEGRATION TEST PASSED!")
        print("All enhanced endpoint configurations are working correctly.")
    else:
        print("❌ INTEGRATION TEST FAILED!")
        print("Please check the implementation.")
    print("=" * 60)
