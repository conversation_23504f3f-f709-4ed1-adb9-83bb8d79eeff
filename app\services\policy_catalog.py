"""
Policy Catalog Service
Provides centralized access to policy metadata, templates, and configurations.
Consolidates the functionality previously spread across multiple files.
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Dict, List, Optional, Set
import yaml
import structlog

from ..core.settings import settings
from ..core.errors import ValidationError

log = structlog.get_logger()


class PolicyCatalog:
    """
    Centralized policy catalog that provides:
    - Policy metadata and parameter definitions
    - Template mappings
    - Parameter merge configurations
    - Policy categories and templates
    """
    
    def __init__(self, catalog_path: Optional[str] = None):
        """Initialize the policy catalog from YAML file"""
        if catalog_path:
            self._catalog_path = Path(catalog_path)
        else:
            sys_dir = Path(settings.systems_dir).resolve()
            self._catalog_path = sys_dir.parent.joinpath("policy_catalog.yaml")
        
        self._catalog_data = self._load_catalog()
        
    def _load_catalog(self) -> Dict[str, Any]:
        """Load the consolidated policy catalog from YAML"""
        if not self._catalog_path.exists():
            log.warning("policy_catalog_not_found", path=str(self._catalog_path))
            return {"policies": {}, "categories": {}, "templates": {}}
        
        try:
            with open(self._catalog_path, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f) or {}
            
            log.info("policy_catalog_loaded", 
                    path=str(self._catalog_path),
                    policies=len(data.get("policies", {})),
                    categories=len(data.get("categories", {})),
                    templates=len(data.get("templates", {})))
            
            return data
        except Exception as e:
            log.error("policy_catalog_load_failed", path=str(self._catalog_path), error=str(e))
            raise ValidationError(f"Failed to load policy catalog: {e}")
    
    # ==================== Policy Information ====================
    
    def get_policy_info(self, policy_name: str) -> Optional[Dict[str, Any]]:
        """Get complete information about a policy"""
        return self._catalog_data.get("policies", {}).get(policy_name)
    
    def get_policy_template(self, policy_name: str) -> Optional[str]:
        """Get the template file name for a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("template") if policy_info else None
    
    def get_policy_category(self, policy_name: str) -> Optional[str]:
        """Get the category of a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("category") if policy_info else None
    
    def get_policy_description(self, policy_name: str) -> Optional[str]:
        """Get the description of a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("description") if policy_info else None
    
    def get_required_params(self, policy_name: str) -> List[str]:
        """Get required parameters for a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("required_params", []) if policy_info else []
    
    def get_optional_params(self, policy_name: str) -> List[str]:
        """Get optional parameters for a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("optional_params", []) if policy_info else []
    
    def get_required_params(self, policy_name: str) -> List[str]:
        """Get required parameters for a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("required_params", []) if policy_info else []

    def get_optional_params(self, policy_name: str) -> List[str]:
        """Get optional parameters for a policy"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("optional_params", []) if policy_info else []

    def get_param_config(self, policy_name: str) -> Dict[str, Dict[str, Any]]:
        """Get parameter configuration for merge semantics"""
        policy_info = self.get_policy_info(policy_name)
        return policy_info.get("param_config", {}) if policy_info else {}

    def get_all_params(self, policy_name: str) -> List[str]:
        """Get all parameters (required + optional) for a policy"""
        required = self.get_required_params(policy_name)
        optional = self.get_optional_params(policy_name)
        return required + optional
    
    # ==================== Groups (Categories + Templates) ====================

    def get_groups(self) -> Dict[str, Dict[str, Any]]:
        """Get all policy groups (categories and template sets)"""
        return self._catalog_data.get("groups", {})

    def get_group_info(self, group_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific group"""
        return self.get_groups().get(group_name)

    def get_group_templates(self, group_name: str) -> List[str]:
        """Get templates for a specific group"""
        group_info = self.get_group_info(group_name)
        if group_info:
            return group_info.get("templates", [])
        return []

    def get_templates_by_category(self, category: str) -> List[str]:
        """Get all templates in a specific category"""
        templates = []
        for group_name, group_info in self.get_groups().items():
            if group_info.get("type") == "category" and group_name == category:
                templates.extend(group_info.get("templates", []))
        return templates

    def get_group_policies(self, group_name: str) -> List[str]:
        """Get policies included in a group (backward compatibility - now returns templates)"""
        group_info = self.get_group_info(group_name)
        # For backward compatibility, return templates as policies
        return group_info.get("templates", group_info.get("policies", [])) if group_info else []

    def get_groups_by_type(self, group_type: str) -> Dict[str, Dict[str, Any]]:
        """Get groups filtered by type (category, template, or template_set)"""
        groups = {}
        for group_name, group_info in self.get_groups().items():
            if group_info.get("type") == group_type:
                groups[group_name] = group_info
        return groups

    def get_categories(self) -> Dict[str, Dict[str, Any]]:
        """Get all categories"""
        return self.get_groups_by_type("category")

    def get_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get all template sets"""
        return self.get_groups_by_type("template_set")

    def get_category_info(self, category_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific category (backward compatibility)"""
        group_info = self.get_group_info(category_name)
        return group_info if group_info and group_info.get("type") == "category" else None

    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific template set"""
        group_info = self.get_group_info(template_name)
        return group_info if group_info and group_info.get("type") == "template_set" else None

    def get_policies_by_category(self, category_name: str) -> List[str]:
        """Get all templates in a specific category"""
        return self.get_group_templates(category_name)

    def get_template_policies(self, template_name: str) -> List[str]:
        """Get templates included in a template set"""
        return self.get_group_templates(template_name)
    
    # ==================== Template Information ====================

    def get_template_metadata(self, template_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific template file"""
        return self._catalog_data.get("templates", {}).get(template_name)

    def get_template_required_params(self, template_name: str) -> List[str]:
        """Get required parameters for a template"""
        template_info = self.get_template_metadata(template_name)
        return template_info.get("required_params", []) if template_info else []

    def get_template_optional_params(self, template_name: str) -> List[str]:
        """Get optional parameters for a template"""
        template_info = self.get_template_metadata(template_name)
        return template_info.get("optional_params", []) if template_info else []

    def get_template_param_config(self, template_name: str) -> Dict[str, Dict[str, Any]]:
        """Get parameter configuration for a template"""
        template_info = self.get_template_metadata(template_name)
        return template_info.get("param_config", {}) if template_info else {}

    def validate_template_exists(self, template_name: str) -> bool:
        """Check if a template exists in the catalog"""
        return template_name in self._catalog_data.get("templates", {})

    def validate_template_params(self, template_name: str, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate parameters for a template.
        Returns (is_valid, list_of_errors)
        """
        errors = []

        if not self.validate_template_exists(template_name):
            errors.append(f"Template '{template_name}' not found in catalog")
            return False, errors

        required_params = self.get_template_required_params(template_name)
        optional_params = self.get_template_optional_params(template_name)
        all_valid_params = set(required_params + optional_params)

        # Check required parameters
        for param in required_params:
            if param not in params:
                errors.append(f"Required parameter '{param}' missing for template '{template_name}'")

        # Check for unknown parameters
        for param in params.keys():
            if param not in all_valid_params:
                errors.append(f"Unknown parameter '{param}' for template '{template_name}'")

        return len(errors) == 0, errors

    # ==================== Utility Methods ====================
    
    def list_all_policies(self) -> List[str]:
        """Get list of all available policies"""
        return list(self._catalog_data.get("policies", {}).keys())
    
    def list_all_groups(self) -> List[str]:
        """Get list of all groups"""
        return list(self._catalog_data.get("groups", {}).keys())

    def list_all_categories(self) -> List[str]:
        """Get list of all categories (backward compatibility)"""
        return [name for name, info in self.get_groups().items() if info.get("type") == "category"]

    def list_all_templates(self) -> List[str]:
        """Get list of all template sets"""
        return [name for name, info in self.get_groups().items() if info.get("type") == "template_set"]

    def list_all_template_files(self) -> List[str]:
        """Get list of all template files referenced in the catalog"""
        templates = set()
        for group_info in self.get_groups().values():
            templates.update(group_info.get("templates", []))
        return sorted(list(templates))
    
    def validate_policy_exists(self, policy_name: str) -> bool:
        """Check if a policy exists in the catalog"""
        return policy_name in self._catalog_data.get("policies", {})
    
    def validate_template_exists(self, template_name: str) -> bool:
        """Check if a template exists in the catalog"""
        group_info = self.get_group_info(template_name)
        return group_info is not None and group_info.get("type") == "template"

    def validate_group_exists(self, group_name: str) -> bool:
        """Check if a group exists in the catalog"""
        return group_name in self._catalog_data.get("groups", {})

    # ==================== Parameter Validation ====================

    def validate_policy_params(self, policy_name: str, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate parameters for a policy.
        Returns (is_valid, list_of_errors)
        """
        errors = []

        if not self.validate_policy_exists(policy_name):
            errors.append(f"Policy '{policy_name}' does not exist in catalog")
            return False, errors

        required_params = self.get_required_params(policy_name)
        optional_params = self.get_optional_params(policy_name)
        all_valid_params = set(required_params + optional_params)
        provided_params = set(params.keys())

        # Check for missing required parameters
        missing_required = set(required_params) - provided_params
        if missing_required:
            errors.append(f"Missing required parameters for {policy_name}: {sorted(missing_required)}")

        # Check for unknown parameters
        unknown_params = provided_params - all_valid_params
        if unknown_params:
            errors.append(f"Unknown parameters for {policy_name}: {sorted(unknown_params)}")

        # Check parameter types and validators
        param_config = self.get_param_config(policy_name)
        for param_name, param_value in params.items():
            if param_name in param_config:
                param_spec = param_config[param_name]
                param_type = param_spec.get("type", "string")
                validator = param_spec.get("validator")

                # Type validation
                type_error = self._validate_param_type(param_name, param_value, param_type)
                if type_error:
                    errors.append(f"{policy_name}.{param_name}: {type_error}")

                # Custom validator validation
                if validator and hasattr(self, f"_validate_{validator}"):
                    validator_func = getattr(self, f"_validate_{validator}")
                    if not validator_func(param_value):
                        errors.append(f"{policy_name}.{param_name}: Invalid value '{param_value}' (failed {validator} validation)")

        return len(errors) == 0, errors

    def _validate_param_type(self, param_name: str, value: Any, expected_type: str) -> Optional[str]:
        """Validate parameter type. Returns error message if invalid, None if valid."""
        if expected_type == "string":
            if not isinstance(value, str):
                return f"Expected string, got {type(value).__name__}"
        elif expected_type == "int":
            if not isinstance(value, int):
                return f"Expected int, got {type(value).__name__}"
        elif expected_type == "bool":
            if not isinstance(value, bool):
                return f"Expected bool, got {type(value).__name__}"
        elif expected_type == "list":
            if not isinstance(value, list):
                return f"Expected list, got {type(value).__name__}"
        return None

    def _validate_is_valid_rate(self, value: str) -> bool:
        """Validate rate format (e.g., '100pm', '50ps')"""
        if not isinstance(value, str):
            return False

        import re
        # Rate format: number + time unit (pm=per minute, ps=per second, etc.)
        pattern = r'^\d+p[smhd]$'
        return bool(re.match(pattern, value))

    def validate_group_policies(self, group_name: str, policy_params: Dict[str, Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """
        Validate parameters for all policies in a group.
        Returns (is_valid, list_of_errors)
        """
        errors = []

        if not self.validate_group_exists(group_name):
            errors.append(f"Group '{group_name}' does not exist")
            return False, errors

        group_policies = self.get_group_policies(group_name)

        for policy_name in group_policies:
            params = policy_params.get(policy_name, {})
            is_valid, policy_errors = self.validate_policy_params(policy_name, params)
            if not is_valid:
                errors.extend(policy_errors)

        return len(errors) == 0, errors
    
    def get_template_mapping(self) -> Dict[str, str]:
        """Get policy name to template file mapping (for backward compatibility)"""
        mapping = {}
        for policy_name, policy_info in self._catalog_data.get("policies", {}).items():
            template = policy_info.get("template")
            if template:
                mapping[policy_name] = template
        return mapping
    
    def get_param_catalog(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Get parameter catalog for merge semantics (for backward compatibility)"""
        catalog = {}
        for policy_name, policy_info in self._catalog_data.get("policies", {}).items():
            param_config = policy_info.get("param_config", {})
            if param_config:
                catalog[policy_name] = param_config
        return catalog


# Global instance for easy access
_policy_catalog: Optional[PolicyCatalog] = None


def get_policy_catalog() -> PolicyCatalog:
    """Get the global policy catalog instance"""
    global _policy_catalog
    if _policy_catalog is None:
        _policy_catalog = PolicyCatalog()
    return _policy_catalog


def reload_policy_catalog() -> PolicyCatalog:
    """Reload the policy catalog (useful for testing or config changes)"""
    global _policy_catalog
    _policy_catalog = PolicyCatalog()
    return _policy_catalog
