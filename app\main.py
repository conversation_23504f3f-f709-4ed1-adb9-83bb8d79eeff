from fastapi import FastAPI
from .core.settings import settings
from .core.logging import install_logging
from .core.errors import install_exception_handlers
from .api.v1.router import router as v1_router

def create_app() -> FastAPI:
    app = FastAPI(
        title="Apigee Proxy Factory",
        version="0.1.0",
        description="Automates Apigee proxy creation and export",
    )
    install_logging(app, settings.log_level)
    install_exception_handlers(app)
    app.include_router(v1_router, prefix="/arabi-assist/api-automation/v1")
    return app

app = create_app()
