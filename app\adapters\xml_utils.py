"""
XML utilities (lxml)
- Safe parse and pretty print
- Simple XPath helpers
- Step creation helper for Apigee policy attachments
"""

from __future__ import annotations

from typing import Iterable, List, Optional

from lxml import etree as ET


def parse_xml(xml: str | bytes) -> ET._Element:
    """
    Parse XML text/bytes into an lxml Element with safe defaults.
    """
    parser = ET.XMLParser(remove_blank_text=True)
    if isinstance(xml, (bytes, bytearray)):
        return ET.fromstring(xml, parser=parser)
    return ET.fromstring(xml.encode("utf-8"), parser=parser)


def tostring(elem: ET._Element, *, pretty: bool = True) -> str:
    """
    Serialize an lxml Element to a unicode string.
    """
    return ET.tostring(elem, encoding="utf-8", pretty_print=pretty, xml_declaration=False).decode("utf-8")


def pretty_xml(xml: str | bytes) -> str:
    """
    Pretty-print XML text or bytes by parsing and re-serializing.
    """
    return tostring(parse_xml(xml), pretty=True).strip()


def findall(elem: ET._Element, xpath: str) -> List[ET._Element]:
    """
    Run XPath and return a list of elements.
    """
    return list(elem.xpath(xpath))


def first(elem: ET._Element, xpath: str) -> Optional[ET._Element]:
    """
    Return the first match for XPath or None.
    """
    res = elem.xpath(xpath)
    return res[0] if res else None


def set_text(elem: ET._Element, xpath: str, value: str) -> None:
    """
    Set text for the first element matching XPath. Creates nothing if missing.
    """
    node = first(elem, xpath)
    if node is not None:
        node.text = value


def ensure_child(parent: ET._Element, tag: str, *, attrib: Optional[dict] = None) -> ET._Element:
    """
    Ensure a child element with a given tag exists. Returns the child (existing or new).
    """
    node = parent.find(tag)
    if node is None:
        node = ET.SubElement(parent, tag, **(attrib or {}))
    return node


def append_step(parent: ET._Element, policy_name: str) -> ET._Element:
    """
    Convenience helper to append an Apigee <Step><Name>policy</Name></Step>.
    """
    step = ET.SubElement(parent, "Step")
    name = ET.SubElement(step, "Name")
    name.text = policy_name
    return step
