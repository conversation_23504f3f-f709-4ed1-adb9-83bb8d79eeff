<BasicAuthentication name="{{ name }}">
  {% if params.operation %}<Operation>{{ params.operation }}</Operation>{% endif %}  <!-- Encode | Decode -->
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}              <!-- e.g., request.header.Authorization -->
  {% if params.username_variable %}<UsernameVariable>{{ params.username_variable }}</UsernameVariable>{% endif %}
  {% if params.password_variable %}<PasswordVariable>{{ params.password_variable }}</PasswordVariable>{% endif %}
  {% if params.assign_to %}<AssignTo>{{ params.assign_to }}</AssignTo>{% endif %}
  {% if params.ignore_unresolved is defined %}<IgnoreUnresolvedVariables>{{ 'true' if params.ignore_unresolved else 'false' }}</IgnoreUnresolvedVariables>{% endif %}
</BasicAuthentication>