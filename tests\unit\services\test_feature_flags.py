"""
Unit tests for FeatureFlagEvaluator service
==========================================
Comprehensive test coverage for feature flag evaluation, hierarchical configuration,
and conditional logic.
"""

import pytest
from unittest.mock import Mock, patch, mock_open
from pathlib import Path
import tempfile
import yaml

from app.services.feature_flags import (
    FeatureFlagEvaluator, 
    get_feature_evaluator, 
    is_feature_enabled
)


class TestFeatureFlagEvaluator:
    """Test suite for FeatureFlagEvaluator class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.evaluator = FeatureFlagEvaluator(env_name="test", system_name="test-system")
        
        # Sample flag configurations
        self.global_flags = {
            "simple_flag": True,
            "disabled_flag": False,
            "complex_flag": {
                "enabled": True,
                "environments": {
                    "dev": True,
                    "test": True,
                    "prod": False
                },
                "percentage": 50,
                "conditions": [
                    {
                        "type": "environment",
                        "values": ["test", "dev"]
                    }
                ]
            },
            "percentage_flag": {
                "enabled": True,
                "percentage": 25
            },
            "conditional_flag": {
                "enabled": True,
                "conditions": [
                    {
                        "type": "context",
                        "key": "method_count",
                        "operator": "greater_than",
                        "value": 3
                    }
                ]
            }
        }
    
    def test_init(self):
        """Test FeatureFlagEvaluator initialization"""
        evaluator = FeatureFlagEvaluator("prod", "payments")
        assert evaluator.env_name == "prod"
        assert evaluator.system_name == "payments"
        assert hasattr(evaluator, '_flags_cache')
    
    @patch('app.services.feature_flags.Path')
    @patch('builtins.open', new_callable=mock_open)
    @patch('app.services.feature_flags.yaml.safe_load')
    def test_load_global_flags(self, mock_yaml, mock_file, mock_path):
        """Test loading global feature flags"""
        mock_yaml.return_value = self.global_flags
        mock_path.return_value.exists.return_value = True
        
        evaluator = FeatureFlagEvaluator("test", "test-system")
        
        # Verify file operations
        mock_file.assert_called()
        mock_yaml.assert_called()
        
        # Verify flags are loaded
        assert len(evaluator._flags_cache) > 0
    
    @patch('app.services.feature_flags.Path')
    def test_load_flags_file_not_found(self, mock_path):
        """Test loading flags when files don't exist"""
        mock_path.return_value.exists.return_value = False
        
        evaluator = FeatureFlagEvaluator("test", "test-system")
        
        # Should handle missing files gracefully
        assert isinstance(evaluator._flags_cache, dict)
    
    def test_is_enabled_simple_flag(self):
        """Test simple boolean flag evaluation"""
        with patch.object(self.evaluator, '_flags_cache', self.global_flags):
            assert self.evaluator.is_enabled("simple_flag") is True
            assert self.evaluator.is_enabled("disabled_flag") is False
            assert self.evaluator.is_enabled("nonexistent_flag") is False
    
    def test_is_enabled_complex_flag(self):
        """Test complex flag evaluation with conditions"""
        with patch.object(self.evaluator, '_flags_cache', self.global_flags):
            # Should be enabled for test environment
            assert self.evaluator.is_enabled("complex_flag") is True
            
            # Test with production environment
            prod_evaluator = FeatureFlagEvaluator("prod", "test-system")
            with patch.object(prod_evaluator, '_flags_cache', self.global_flags):
                assert prod_evaluator.is_enabled("complex_flag") is False
    
    def test_is_enabled_with_context(self):
        """Test flag evaluation with context"""
        with patch.object(self.evaluator, '_flags_cache', self.global_flags):
            context = {"method_count": 5}
            assert self.evaluator.is_enabled("conditional_flag", context) is True
            
            context = {"method_count": 2}
            assert self.evaluator.is_enabled("conditional_flag", context) is False
    
    def test_evaluate_complex_flag_disabled(self):
        """Test complex flag evaluation when globally disabled"""
        disabled_flag = {
            "enabled": False,
            "environments": {"test": True}
        }
        
        with patch.object(self.evaluator, '_flags_cache', {"test_flag": disabled_flag}):
            assert self.evaluator.is_enabled("test_flag") is False
    
    def test_evaluate_complex_flag_environment_specific(self):
        """Test environment-specific flag evaluation"""
        env_flag = {
            "enabled": True,
            "environments": {
                "dev": True,
                "test": False,
                "prod": True
            }
        }
        
        with patch.object(self.evaluator, '_flags_cache', {"env_flag": env_flag}):
            # Test environment is disabled
            assert self.evaluator.is_enabled("env_flag") is False
            
            # Dev environment should be enabled
            dev_evaluator = FeatureFlagEvaluator("dev", "test-system")
            with patch.object(dev_evaluator, '_flags_cache', {"env_flag": env_flag}):
                assert dev_evaluator.is_enabled("env_flag") is True
    
    def test_evaluate_percentage_flag(self):
        """Test percentage-based flag evaluation"""
        with patch.object(self.evaluator, '_flags_cache', self.global_flags):
            # Mock hash function to control percentage outcome
            with patch('builtins.hash', return_value=20):  # 20% < 25%
                assert self.evaluator.is_enabled("percentage_flag") is True
            
            with patch('builtins.hash', return_value=30):  # 30% > 25%
                assert self.evaluator.is_enabled("percentage_flag") is False
    
    def test_evaluate_conditions_environment(self):
        """Test environment condition evaluation"""
        conditions = [{"type": "environment", "values": ["test", "dev"]}]
        
        assert self.evaluator._evaluate_conditions(conditions, {}) is True
        
        prod_evaluator = FeatureFlagEvaluator("prod", "test-system")
        assert prod_evaluator._evaluate_conditions(conditions, {}) is False
    
    def test_evaluate_conditions_system(self):
        """Test system condition evaluation"""
        conditions = [{"type": "system", "values": ["test-system", "payments"]}]
        
        assert self.evaluator._evaluate_conditions(conditions, {}) is True
        
        other_evaluator = FeatureFlagEvaluator("test", "other-system")
        assert other_evaluator._evaluate_conditions(conditions, {}) is False
    
    def test_evaluate_conditions_context(self):
        """Test context condition evaluation"""
        conditions = [
            {
                "type": "context",
                "key": "method_count",
                "operator": "greater_than",
                "value": 3
            }
        ]
        
        context = {"method_count": 5}
        assert self.evaluator._evaluate_conditions(conditions, context) is True
        
        context = {"method_count": 2}
        assert self.evaluator._evaluate_conditions(conditions, context) is False
    
    def test_evaluate_conditions_context_operators(self):
        """Test different context condition operators"""
        # Test equals operator
        conditions = [{"type": "context", "key": "status", "operator": "equals", "value": "active"}]
        assert self.evaluator._evaluate_conditions(conditions, {"status": "active"}) is True
        assert self.evaluator._evaluate_conditions(conditions, {"status": "inactive"}) is False
        
        # Test in operator
        conditions = [{"type": "context", "key": "role", "operator": "in", "value": ["admin", "user"]}]
        assert self.evaluator._evaluate_conditions(conditions, {"role": "admin"}) is True
        assert self.evaluator._evaluate_conditions(conditions, {"role": "guest"}) is False
        
        # Test less_than operator
        conditions = [{"type": "context", "key": "count", "operator": "less_than", "value": 10}]
        assert self.evaluator._evaluate_conditions(conditions, {"count": 5}) is True
        assert self.evaluator._evaluate_conditions(conditions, {"count": 15}) is False
    
    def test_evaluate_conditions_api_characteristic(self):
        """Test API characteristic condition evaluation"""
        conditions = [
            {
                "type": "api_characteristic",
                "characteristic": "method_count",
                "operator": "greater_than",
                "value": 3
            }
        ]
        
        context = {"methods": ["GET", "POST", "PUT", "DELETE", "PATCH"]}
        assert self.evaluator._evaluate_conditions(conditions, context) is True
        
        context = {"methods": ["GET", "POST"]}
        assert self.evaluator._evaluate_conditions(conditions, context) is False
    
    def test_evaluate_conditions_has_security(self):
        """Test has_security characteristic evaluation"""
        conditions = [
            {
                "type": "api_characteristic",
                "characteristic": "has_security",
                "value": True
            }
        ]
        
        context = {"security_policies": ["VerifyApiKey"]}
        assert self.evaluator._evaluate_conditions(conditions, context) is True
        
        context = {"security_policies": []}
        assert self.evaluator._evaluate_conditions(conditions, context) is False
    
    def test_get_flag_value(self):
        """Test getting flag values (not just boolean)"""
        value_flags = {
            "string_flag": "test_value",
            "number_flag": 42,
            "complex_value_flag": {
                "enabled": True,
                "value": "complex_value",
                "default": "default_value"
            }
        }
        
        with patch.object(self.evaluator, '_flags_cache', value_flags):
            assert self.evaluator.get_flag_value("string_flag") == "test_value"
            assert self.evaluator.get_flag_value("number_flag") == 42
            assert self.evaluator.get_flag_value("complex_value_flag") == "complex_value"
            assert self.evaluator.get_flag_value("nonexistent", "default") == "default"
    
    def test_get_enabled_flags(self):
        """Test getting list of enabled flags"""
        with patch.object(self.evaluator, '_flags_cache', self.global_flags):
            enabled_flags = self.evaluator.get_enabled_flags()
            
            assert "simple_flag" in enabled_flags
            assert "disabled_flag" not in enabled_flags
            assert isinstance(enabled_flags, list)
    
    def test_reload_flags(self):
        """Test reloading feature flags"""
        with patch.object(self.evaluator, '_load_feature_flags') as mock_load:
            self.evaluator.reload_flags()
            mock_load.assert_called_once()
    
    def test_hierarchical_flag_precedence(self):
        """Test hierarchical flag precedence (Global < Environment < System)"""
        global_flags = {"test_flag": False}
        env_flags = {"test_flag": True}
        system_flags = {"test_flag": False}
        
        with patch.object(self.evaluator, '_load_global_flags', return_value=global_flags):
            with patch.object(self.evaluator, '_load_environment_flags', return_value=env_flags):
                with patch.object(self.evaluator, '_load_system_flags', return_value=system_flags):
                    self.evaluator._load_feature_flags()
                    
                    # System flags should take precedence
                    assert self.evaluator._flags_cache["test_flag"] is False


class TestFeatureFlagGlobalFunctions:
    """Test global feature flag functions"""
    
    def test_get_feature_evaluator(self):
        """Test get_feature_evaluator function"""
        evaluator = get_feature_evaluator("prod", "payments")
        
        assert isinstance(evaluator, FeatureFlagEvaluator)
        assert evaluator.env_name == "prod"
        assert evaluator.system_name == "payments"
    
    def test_get_feature_evaluator_caching(self):
        """Test feature evaluator caching"""
        evaluator1 = get_feature_evaluator("test", "system1")
        evaluator2 = get_feature_evaluator("test", "system1")
        
        # Should return the same instance for same parameters
        assert evaluator1 is evaluator2
        
        # Should create new instance for different parameters
        evaluator3 = get_feature_evaluator("prod", "system1")
        assert evaluator1 is not evaluator3
    
    def test_is_feature_enabled_convenience(self):
        """Test is_feature_enabled convenience function"""
        with patch('app.services.feature_flags.get_feature_evaluator') as mock_get:
            mock_evaluator = Mock()
            mock_evaluator.is_enabled.return_value = True
            mock_get.return_value = mock_evaluator
            
            result = is_feature_enabled("test_flag", "prod", "payments", {"key": "value"})
            
            assert result is True
            mock_get.assert_called_once_with("prod", "payments")
            mock_evaluator.is_enabled.assert_called_once_with("test_flag", {"key": "value"})


class TestFeatureFlagErrorHandling:
    """Test error handling scenarios"""
    
    def test_load_flags_yaml_error(self):
        """Test handling of YAML parsing errors"""
        with patch('app.services.feature_flags.Path') as mock_path:
            mock_path.return_value.exists.return_value = True
            
            with patch('builtins.open', mock_open(read_data="invalid: yaml: content:")):
                with patch('app.services.feature_flags.yaml.safe_load', side_effect=yaml.YAMLError("Invalid YAML")):
                    # Should handle YAML errors gracefully
                    evaluator = FeatureFlagEvaluator("test", "test-system")
                    assert isinstance(evaluator._flags_cache, dict)
    
    def test_load_flags_file_permission_error(self):
        """Test handling of file permission errors"""
        with patch('app.services.feature_flags.Path') as mock_path:
            mock_path.return_value.exists.return_value = True
            
            with patch('builtins.open', side_effect=PermissionError("Permission denied")):
                # Should handle permission errors gracefully
                evaluator = FeatureFlagEvaluator("test", "test-system")
                assert isinstance(evaluator._flags_cache, dict)
    
    def test_evaluate_conditions_missing_context(self):
        """Test condition evaluation with missing context keys"""
        conditions = [{"type": "context", "key": "missing_key", "operator": "equals", "value": "test"}]
        
        # Should return False for missing context keys
        assert self.evaluator._evaluate_conditions(conditions, {}) is False
    
    def test_evaluate_conditions_invalid_condition_type(self):
        """Test condition evaluation with invalid condition type"""
        conditions = [{"type": "invalid_type", "key": "test"}]
        
        # Should return False for unknown condition types
        assert self.evaluator._evaluate_conditions(conditions, {}) is False
    
    def test_evaluate_single_condition_malformed(self):
        """Test single condition evaluation with malformed condition"""
        condition = {"type": "context"}  # Missing required fields
        
        # Should handle malformed conditions gracefully
        assert self.evaluator._evaluate_single_condition(condition, {}) is False


class TestFeatureFlagIntegration:
    """Integration tests for feature flags"""
    
    @pytest.mark.integration
    def test_load_real_feature_flags(self):
        """Test loading actual feature flag configuration"""
        try:
            evaluator = FeatureFlagEvaluator("test", "test-system")
            
            # Should load without errors
            assert isinstance(evaluator._flags_cache, dict)
            
            # Test some expected flags from the actual config
            if "advanced_security" in evaluator._flags_cache:
                result = evaluator.is_enabled("advanced_security")
                assert isinstance(result, bool)
                
        except Exception as e:
            pytest.skip(f"Real feature flag config not available: {e}")
    
    @pytest.mark.integration
    def test_complex_flag_evaluation_integration(self):
        """Test complex flag evaluation with real configuration"""
        try:
            evaluator = FeatureFlagEvaluator("test", "payments")
            
            context = {
                "methods": ["GET", "POST", "PUT", "DELETE"],
                "content_types": ["application/json"],
                "security_policies": ["VerifyApiKey"]
            }
            
            # Test various flags that might exist
            test_flags = ["smart_caching", "json_threat_protection", "conditional_policies"]
            
            for flag in test_flags:
                if flag in evaluator._flags_cache:
                    result = evaluator.is_enabled(flag, context)
                    assert isinstance(result, bool)
                    
        except Exception as e:
            pytest.skip(f"Integration test environment not available: {e}")
