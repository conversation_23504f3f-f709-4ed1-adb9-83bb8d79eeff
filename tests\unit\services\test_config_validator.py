"""
Unit tests for ConfigValidator service
=====================================
Comprehensive test coverage for configuration validation,
system config validation, and template validation.
"""

import pytest
from unittest.mock import Mock, patch, mock_open
from pathlib import Path
import tempfile
import xml.etree.ElementTree as ET

from app.services.config_validator import (
    ValidationResult,
    SystemConfigValidator,
    TemplateValidator,
    ConfigurationValidator
)
from app.core.errors import ValidationError


class TestValidationResult:
    """Test suite for ValidationResult class"""
    
    def test_init_valid(self):
        """Test ValidationResult initialization for valid result"""
        result = ValidationResult(is_valid=True)
        
        assert result.is_valid is True
        assert result.errors == []
        assert result.warnings == []
        assert result.context is None
    
    def test_init_invalid_with_errors(self):
        """Test ValidationResult initialization with errors"""
        errors = ["Error 1", "Error 2"]
        warnings = ["Warning 1"]
        
        result = ValidationResult(
            is_valid=False,
            errors=errors,
            warnings=warnings,
            context="test_context"
        )
        
        assert result.is_valid is False
        assert result.errors == errors
        assert result.warnings == warnings
        assert result.context == "test_context"
    
    def test_add_error(self):
        """Test adding error to ValidationResult"""
        result = ValidationResult(is_valid=True)
        
        result.add_error("Test error")
        
        assert result.is_valid is False
        assert "Test error" in result.errors
    
    def test_add_warning(self):
        """Test adding warning to ValidationResult"""
        result = ValidationResult()
        
        result.add_warning("Test warning")
        
        assert result.is_valid is True  # Warnings don't affect validity
        assert "Test warning" in result.warnings
    
    def test_merge_results(self):
        """Test merging ValidationResult objects"""
        result1 = ValidationResult(is_valid=True, warnings=["Warning 1"])
        result2 = ValidationResult(is_valid=False, errors=["Error 1"], warnings=["Warning 2"])
        
        result1.merge(result2)
        
        assert result1.is_valid is False
        assert "Error 1" in result1.errors
        assert "Warning 1" in result1.warnings
        assert "Warning 2" in result1.warnings
    
    def test_to_dict(self):
        """Test converting ValidationResult to dictionary"""
        result = ValidationResult(
            is_valid=False,
            errors=["Error 1"],
            warnings=["Warning 1"],
            context="test"
        )
        
        result_dict = result.to_dict()
        
        assert result_dict["is_valid"] is False
        assert result_dict["errors"] == ["Error 1"]
        assert result_dict["warnings"] == ["Warning 1"]
        assert result_dict["context"] == "test"


class TestSystemConfigValidator:
    """Test suite for SystemConfigValidator class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = SystemConfigValidator()
        
        self.valid_config = {
            "metadata": {
                "system": "test-system",
                "owner_team": "test-team",
                "description": "Test system"
            },
            "policy_defaults": {
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "SpikeArrest": {"rate": "100pm"},
                "Quota": {"allow": "1000pm", "timeunit": "minute"}
            },
            "flows": {
                "preflow": {
                    "request": ["VerifyApiKey", "SpikeArrest"],
                    "response": []
                },
                "per_operation": {
                    "request": ["Quota"],
                    "response": []
                },
                "cors": {
                    "enabled": True,
                    "policy": "CORS"
                }
            },
            "target": {
                "baseurl": "https://api.example.com",
                "connect_timeout_ms": 5000,
                "read_timeout_ms": 30000
            },
            "features": {
                "advanced_security": True,
                "caching_enabled": False
            }
        }
    
    def test_init(self):
        """Test SystemConfigValidator initialization"""
        validator = SystemConfigValidator()
        
        assert hasattr(validator, 'helpers')
    
    def test_validate_system_config_valid(self):
        """Test validation of valid system configuration"""
        result = self.validator.validate_system_config(self.valid_config, "test-system")
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_metadata_missing_required_fields(self):
        """Test metadata validation with missing required fields"""
        metadata = {"system": "test-system"}  # Missing owner_team
        
        result = self.validator._validate_metadata(metadata)
        
        assert result.is_valid is False
        assert any("owner_team" in error for error in result.errors)
    
    def test_validate_metadata_invalid_system_name(self):
        """Test metadata validation with invalid system name"""
        metadata = {
            "system": "Invalid System Name!",  # Invalid characters
            "owner_team": "test-team"
        }
        
        result = self.validator._validate_metadata(metadata)
        
        assert result.is_valid is False
        assert any("lowercase" in error for error in result.errors)
    
    def test_validate_policy_defaults_valid(self):
        """Test validation of valid policy defaults"""
        policy_defaults = {
            "SpikeArrest": {"rate": "100pm"},
            "Quota": {"allow": "1000pm", "timeunit": "minute"}
        }
        
        result = self.validator._validate_policy_defaults(policy_defaults)
        
        assert result.is_valid is True
    
    def test_validate_policy_defaults_invalid_structure(self):
        """Test validation of policy defaults with invalid structure"""
        policy_defaults = {
            "SpikeArrest": "invalid_structure"  # Should be dict
        }
        
        result = self.validator._validate_policy_defaults(policy_defaults)
        
        assert result.is_valid is False
        assert any("dictionary" in error for error in result.errors)
    
    def test_validate_policy_params_spike_arrest(self):
        """Test validation of SpikeArrest policy parameters"""
        # Valid rate
        result = self.validator._validate_policy_params("SpikeArrest", {"rate": "100pm"})
        assert result.is_valid is True
        
        # Invalid rate
        result = self.validator._validate_policy_params("SpikeArrest", {"rate": "invalid"})
        assert result.is_valid is False
        assert any("Invalid rate format" in error for error in result.errors)
    
    def test_validate_policy_params_quota(self):
        """Test validation of Quota policy parameters"""
        # Valid parameters
        result = self.validator._validate_policy_params("Quota", {
            "allow": "1000pm",
            "timeunit": "minute"
        })
        assert result.is_valid is True
        
        # Invalid quota format
        result = self.validator._validate_policy_params("Quota", {"allow": "invalid"})
        assert result.is_valid is False
        
        # Invalid timeunit
        result = self.validator._validate_policy_params("Quota", {"timeunit": "invalid"})
        assert result.is_valid is False
    
    def test_validate_policy_params_verify_api_key(self):
        """Test validation of VerifyApiKey policy parameters"""
        # Valid keyref
        result = self.validator._validate_policy_params("VerifyApiKey", {
            "keyref": "request.header.x-api-key"
        })
        assert result.is_valid is True
        
        # Keyref without request prefix (warning)
        result = self.validator._validate_policy_params("VerifyApiKey", {
            "keyref": "header.x-api-key"
        })
        assert result.is_valid is True
        assert len(result.warnings) > 0
    
    def test_validate_policy_params_cors(self):
        """Test validation of CORS policy parameters"""
        # Valid CORS config
        result = self.validator._validate_policy_params("CORS", {
            "allow_origins": ["https://example.com", "*"],
            "allow_headers": ["Content-Type", "Authorization"]
        })
        assert result.is_valid is True
        
        # Invalid origin URL
        result = self.validator._validate_policy_params("CORS", {
            "allow_origins": ["invalid-url"]
        })
        assert result.is_valid is False
        
        # Invalid header name
        result = self.validator._validate_policy_params("CORS", {
            "allow_headers": ["Invalid Header!"]
        })
        assert result.is_valid is False
    
    def test_validate_flows_config_valid(self):
        """Test validation of valid flows configuration"""
        flows = {
            "preflow": {
                "request": ["VerifyApiKey", "SpikeArrest"],
                "response": []
            },
            "per_operation": {
                "request": ["Quota"],
                "response": []
            }
        }
        
        result = self.validator._validate_flows_config(flows)
        
        assert result.is_valid is True
    
    def test_validate_flows_config_missing_flows(self):
        """Test validation with missing flow configurations"""
        flows = {}  # Missing preflow and per_operation
        
        result = self.validator._validate_flows_config(flows)
        
        # Should have warnings for missing flows
        assert len(result.warnings) > 0
    
    def test_validate_flows_config_invalid_structure(self):
        """Test validation with invalid flow structure"""
        flows = {
            "preflow": "invalid_structure"  # Should be dict
        }
        
        result = self.validator._validate_flows_config(flows)
        
        assert result.is_valid is False
        assert any("dictionary" in error for error in result.errors)
    
    def test_validate_flows_config_invalid_policies_list(self):
        """Test validation with invalid policies list"""
        flows = {
            "preflow": {
                "request": "should_be_list"  # Should be list
            }
        }
        
        result = self.validator._validate_flows_config(flows)
        
        assert result.is_valid is False
        assert any("list" in error for error in result.errors)
    
    def test_validate_target_config_valid(self):
        """Test validation of valid target configuration"""
        target = {
            "baseurl": "https://api.example.com",
            "connect_timeout_ms": 5000,
            "read_timeout_ms": 30000
        }
        
        result = self.validator._validate_target_config(target)
        
        assert result.is_valid is True
    
    def test_validate_target_config_invalid_url(self):
        """Test validation with invalid target URL"""
        target = {"baseurl": "invalid-url"}
        
        result = self.validator._validate_target_config(target)
        
        assert result.is_valid is False
        assert any("Invalid target baseurl" in error for error in result.errors)
    
    def test_validate_target_config_invalid_timeout(self):
        """Test validation with invalid timeout values"""
        target = {
            "connect_timeout_ms": -1000,  # Negative timeout
            "read_timeout_ms": "invalid"  # Non-integer
        }
        
        result = self.validator._validate_target_config(target)
        
        assert result.is_valid is False
        assert len(result.errors) >= 2
    
    def test_validate_target_config_high_timeout_warning(self):
        """Test validation with very high timeout values"""
        target = {
            "connect_timeout_ms": 400000  # > 5 minutes
        }
        
        result = self.validator._validate_target_config(target)
        
        assert result.is_valid is True
        assert len(result.warnings) > 0
        assert any("Very high" in warning for warning in result.warnings)
    
    def test_validate_features_valid(self):
        """Test validation of valid features configuration"""
        features = {
            "advanced_security": True,
            "caching_enabled": False
        }
        
        result = self.validator._validate_features(features)
        
        assert result.is_valid is True
    
    def test_validate_features_non_boolean_warning(self):
        """Test validation with non-boolean feature values"""
        features = {
            "advanced_security": "true",  # String instead of boolean
            "cache_size": 100  # Integer instead of boolean
        }
        
        result = self.validator._validate_features(features)
        
        assert result.is_valid is True
        assert len(result.warnings) >= 2
    
    def test_is_valid_policy_name(self):
        """Test policy name validation"""
        assert self.validator._is_valid_policy_name("VerifyApiKey") is True
        assert self.validator._is_valid_policy_name("SpikeArrest") is True
        assert self.validator._is_valid_policy_name("UnknownPolicy") is False


class TestTemplateValidator:
    """Test suite for TemplateValidator class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = TemplateValidator()
    
    def test_validate_template_not_found(self):
        """Test validation of non-existent template"""
        template_path = Path("nonexistent_template.xml.j2")
        
        result = self.validator.validate_template(template_path)
        
        assert result.is_valid is False
        assert any("not found" in error for error in result.errors)
    
    def test_validate_template_syntax_valid(self):
        """Test validation of valid template syntax"""
        template_content = '<Policy name="{{ name }}">{{ params.value }}</Policy>'
        
        result = self.validator._validate_template_syntax(template_content)
        
        assert result.is_valid is True
    
    def test_validate_template_syntax_invalid(self):
        """Test validation of invalid template syntax"""
        template_content = '<Policy>{{ unclosed_variable</Policy>'
        
        result = self.validator._validate_template_syntax(template_content)
        
        assert result.is_valid is False
        assert any("syntax error" in error for error in result.errors)
    
    def test_validate_template_syntax_undefined_variables(self):
        """Test detection of potentially undefined variables"""
        template_content = '<Policy>{{ undefined_var }}</Policy>'
        
        result = self.validator._validate_template_syntax(template_content)
        
        # Should have warnings for undefined variables
        assert len(result.warnings) > 0
    
    def test_validate_rendered_xml_valid(self):
        """Test validation of valid rendered XML"""
        xml_content = '<SpikeArrest name="SA-Test"><Rate>100pm</Rate></SpikeArrest>'
        
        result = self.validator._validate_rendered_xml(xml_content)
        
        assert result.is_valid is True
    
    def test_validate_rendered_xml_invalid(self):
        """Test validation of invalid XML"""
        xml_content = '<SpikeArrest><Rate>100pm</SpikeArrest>'  # Missing closing tag
        
        result = self.validator._validate_rendered_xml(xml_content)
        
        assert result.is_valid is False
        assert any("Invalid XML" in error for error in result.errors)
    
    def test_validate_rendered_xml_empty(self):
        """Test validation of empty XML"""
        xml_content = ""
        
        result = self.validator._validate_rendered_xml(xml_content)
        
        assert result.is_valid is False
        assert any("empty" in error for error in result.errors)
    
    def test_validate_rendered_xml_unresolved_variables(self):
        """Test detection of unresolved template variables"""
        xml_content = '<Policy>{{ unresolved_var }}</Policy>'
        
        result = self.validator._validate_rendered_xml(xml_content)
        
        assert len(result.warnings) > 0
        assert any("Unresolved" in warning for warning in result.warnings)


class TestConfigurationValidator:
    """Test suite for ConfigurationValidator class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = ConfigurationValidator()
    
    def test_init(self):
        """Test ConfigurationValidator initialization"""
        validator = ConfigurationValidator()
        
        assert hasattr(validator, 'system_validator')
        assert hasattr(validator, 'template_validator')
        assert isinstance(validator.system_validator, SystemConfigValidator)
        assert isinstance(validator.template_validator, TemplateValidator)
    
    @patch('app.services.config_validator.yaml.safe_load')
    @patch('builtins.open', new_callable=mock_open)
    def test_validate_all_system_configs(self, mock_file, mock_yaml):
        """Test validation of all system configurations"""
        mock_yaml.return_value = {
            "metadata": {"system": "test", "owner_team": "team"},
            "policy_defaults": {},
            "flows": {"preflow": {"request": [], "response": []}},
            "target": {"baseurl": "https://api.example.com"}
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            systems_dir = config_dir / "systems"
            systems_dir.mkdir()
            
            # Create a test system file
            system_file = systems_dir / "test-system.yaml"
            system_file.write_text("test content")
            
            with patch.object(systems_dir, 'glob', return_value=[system_file]):
                results = self.validator.validate_all(config_dir)
                
                assert "system:test-system" in results
                assert isinstance(results["system:test-system"], ValidationResult)
    
    def test_validate_all_system_config_load_error(self):
        """Test handling of system config load errors"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            systems_dir = config_dir / "systems"
            systems_dir.mkdir()
            
            # Create a test system file
            system_file = systems_dir / "bad-system.yaml"
            system_file.write_text("invalid: yaml: content:")
            
            with patch('app.services.config_validator.yaml.safe_load', side_effect=Exception("YAML error")):
                with patch.object(systems_dir, 'glob', return_value=[system_file]):
                    results = self.validator.validate_all(config_dir)
                    
                    assert "system:bad-system" in results
                    result = results["system:bad-system"]
                    assert result.is_valid is False
                    assert any("Failed to load" in error for error in result.errors)
    
    def test_generate_validation_report(self):
        """Test generation of validation report"""
        results = {
            "system:test1": ValidationResult(is_valid=True),
            "system:test2": ValidationResult(
                is_valid=False,
                errors=["Error 1", "Error 2"],
                warnings=["Warning 1"]
            ),
            "template:test.xml.j2": ValidationResult(is_valid=True)
        }
        
        report = self.validator.generate_validation_report(results)
        
        assert "CONFIGURATION VALIDATION REPORT" in report
        assert "Total Configurations: 3" in report
        assert "Valid: 2" in report
        assert "Invalid: 1" in report
        assert "✅ VALID system:test1" in report
        assert "❌ INVALID system:test2" in report
        assert "ERROR: Error 1" in report
        assert "WARNING: Warning 1" in report


class TestConfigValidatorErrorHandling:
    """Test error handling scenarios"""
    
    def setup_method(self):
        """Set up error handling test fixtures"""
        self.validator = SystemConfigValidator()
    
    def test_validate_system_config_none_input(self):
        """Test validation with None input"""
        with pytest.raises((TypeError, AttributeError)):
            self.validator.validate_system_config(None, "test")
    
    def test_validate_system_config_empty_input(self):
        """Test validation with empty configuration"""
        result = self.validator.validate_system_config({}, "test")
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_policy_params_unknown_policy(self):
        """Test validation of unknown policy parameters"""
        result = self.validator._validate_policy_params("UnknownPolicy", {"param": "value"})
        
        # Should not fail for unknown policies (just skip validation)
        assert result.is_valid is True
    
    def test_validate_flows_config_malformed_cors(self):
        """Test validation with malformed CORS configuration"""
        flows = {
            "cors": {
                "enabled": "not_boolean",  # Should be boolean
                # Missing policy field when enabled
            }
        }
        
        result = self.validator._validate_flows_config(flows)
        
        assert result.is_valid is False
        assert len(result.errors) > 0


class TestConfigValidatorIntegration:
    """Integration tests for config validator"""
    
    @pytest.mark.integration
    def test_validate_real_system_config(self):
        """Test validation of real system configuration"""
        try:
            validator = SystemConfigValidator()
            
            # Try to load and validate a real system config
            config_path = Path("app/config/systems/reflect.yaml")
            if config_path.exists():
                import yaml
                with open(config_path, 'r') as f:
                    config = yaml.safe_load(f)
                
                result = validator.validate_system_config(config, "reflect")
                
                # Real config should be valid or have only warnings
                if not result.is_valid:
                    print(f"Validation errors: {result.errors}")
                    print(f"Validation warnings: {result.warnings}")
                
                # At minimum, should not crash
                assert isinstance(result, ValidationResult)
            else:
                pytest.skip("Real system config not found")
                
        except Exception as e:
            pytest.skip(f"Integration test environment not available: {e}")
    
    @pytest.mark.integration
    def test_validate_all_real_configs(self):
        """Test validation of all real configurations"""
        try:
            validator = ConfigurationValidator()
            config_dir = Path("app/config")
            
            if config_dir.exists():
                results = validator.validate_all(config_dir)
                
                assert isinstance(results, dict)
                
                # Generate report
                report = validator.generate_validation_report(results)
                assert isinstance(report, str)
                assert "CONFIGURATION VALIDATION REPORT" in report
                
            else:
                pytest.skip("Real config directory not found")
                
        except Exception as e:
            pytest.skip(f"Integration test environment not available: {e}")
