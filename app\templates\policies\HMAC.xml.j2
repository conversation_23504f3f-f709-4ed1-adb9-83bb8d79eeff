<HMAC name="{{ name }}">
  {% if params.algorithm %}<Algorithm>{{ params.algorithm }}</Algorithm>{% endif %}  <!-- HmacSHA256, HmacSHA1, ... -->
  {% if params.message %}<Message>{{ params.message }}</Message>{% endif %}          <!-- variable or literal -->
  {% if params.secret_key %}
  <SecretKey>
    {% if params.secret_key.ref %}<Value ref="{{ params.secret_key.ref }}"/>{% elif params.secret_key.inline %}<Value>{{ params.secret_key.inline }}</Value>{% endif %}
  </SecretKey>
  {% endif %}
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.encoding %}<OutputEncoding>{{ params.encoding }}</OutputEncoding>{% endif %} <!-- base64 | hex -->
</HMAC>