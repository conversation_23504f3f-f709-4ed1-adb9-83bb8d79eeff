<!-- TargetEndpoint template with PreFlow and PostFlow support -->
<TargetEndpoint name="{{ name }}">
  {% if preflow_request or preflow_response %}
  <PreFlow name="PreFlow">
    {% if preflow_request %}
    <Request>
      {% for policy in preflow_request %}
      <Step>
        <Name>{{ policy }}</Name>
      </Step>
      {% endfor %}
    </Request>
    {% endif %}
    {% if preflow_response %}
    <Response>
      {% for policy in preflow_response %}
      <Step>
        <Name>{{ policy }}</Name>
      </Step>
      {% endfor %}
    </Response>
    {% endif %}
  </PreFlow>
  {% endif %}

  {% if postflow_request or postflow_response %}
  <PostFlow name="PostFlow">
    {% if postflow_request %}
    <Request>
      {% for policy in postflow_request %}
      <Step>
        <Name>{{ policy }}</Name>
      </Step>
      {% endfor %}
    </Request>
    {% endif %}
    {% if postflow_response %}
    <Response>
      {% for policy in postflow_response %}
      <Step>
        <Name>{{ policy }}</Name>
      </Step>
      {% endfor %}
    </Response>
    {% endif %}
  </PostFlow>
  {% endif %}

  <HTTPTargetConnection>
    <URL>{{ url }}</URL>
    <SSLInfo>
      <Enabled>{{ 'true' if ssl.enabled else 'false' }}</Enabled>
    </SSLInfo>
    <Properties>
      <Property name="connect.timeout.millis">{{ timeouts.connect_ms }}</Property>
      <Property name="read.timeout.millis">{{ timeouts.read_ms }}</Property>
    </Properties>
  </HTTPTargetConnection>
</TargetEndpoint>
