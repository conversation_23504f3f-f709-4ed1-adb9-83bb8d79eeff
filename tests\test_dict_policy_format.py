#!/usr/bin/env python3
"""
Test dict-format policy entries with the new optimized structure.
This test verifies that the policy builder correctly handles dict entries
with 'policy' field and optional parameters.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.policy_builder import PolicyBuilder


def test_dict_policy_format():
    """Test dict-format policy entries with the new structure."""
    print("============================================================")
    print("TESTING DICT-FORMAT POLICY ENTRIES")
    print("============================================================")
    
    # Test configuration with dict-format entries
    test_cfg = {
        "flows": {
            "proxy": {
                "preflow": {
                    "request": [
                        "VK-VerifyApiKey",  # String format
                        {  # Dict format with policy field
                            "policy": "SA-SpikeArrest",
                            "params": {
                                "rate": "200pm"  # Override default rate
                            }
                        },
                        {  # Dict format with custom display name
                            "policy": "AM-SetRequestData",
                            "display_name": "AM-CustomRequestData",
                            "params": {
                                "custom_header": "X-Custom-Value"
                            }
                        }
                    ],
                    "response": ["AM-AddRespHeaders"]
                }
            }
        },
        "policies": {
            "VK-VerifyApiKey": {
                "template": "VerifyApiKey.xml.j2",
                "keyref": "request.header.x-api-key"
            },
            "SA-SpikeArrest": {
                "template": "SpikeArrest.xml.j2",
                "rate": "100pm"  # Default rate
            },
            "AM-SetRequestData": {
                "template": "AssignMessage.xml.j2",
                "set": {
                    "headers": [
                        {"name": "X-Request-ID", "value": "{messageid}"}
                    ]
                }
            },
            "AM-AddRespHeaders": {
                "template": "AssignMessage.xml.j2",
                "add": {
                    "headers": [
                        {"name": "X-Response-Time", "value": "{system.timestamp}"}
                    ]
                }
            }
        }
    }
    
    try:
        # Test policy rendering
        policy_builder = PolicyBuilder()
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=test_cfg,
            input_vars={"policy_params": {}},
            system_name="test"
        )
        
        print(f"✅ Successfully rendered {len(policies)} policies")
        
        # Verify policies were created
        policy_names = [p.name for p in policies]
        print(f"✅ Policy names: {policy_names}")
        
        # Check that dict-format entries were processed correctly
        spike_arrest_policies = [p for p in policies if "SpikeArrest" in p.name]
        if spike_arrest_policies:
            print("✅ Dict-format SpikeArrest policy processed correctly")
        
        custom_request_policies = [p for p in policies if "CustomRequestData" in p.name]
        if custom_request_policies:
            print("✅ Dict-format policy with custom display name processed correctly")
        
        # Verify attach plan
        preflow_req = attach_plan.preflow_request
        print(f"✅ PreFlow request policies: {len(preflow_req)} policies")

        if len(preflow_req) >= 3:
            print("✅ All dict-format policies included in attach plan")
        
        print("\n============================================================")
        print("✅ DICT-FORMAT POLICY TEST PASSED!")
        print("Both string and dict-format policy entries work correctly.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_dict_policy_format()
    sys.exit(0 if success else 1)
