# Smart Policy Template Development Guide

## Overview

The Apigee Proxy Framework provides a smart template system with built-in validation, conditional rendering, and environment awareness. This guide explains how to develop and enhance policy templates.

## Template Helper Functions

### Validation Helpers

Templates have access to validation functions for common parameter types:

```jinja2
<!-- Validate rate format (100pm, 50ps, etc.) -->
{% set rate_value = safe_default(params.rate, '100pm', is_valid_rate) %}
<Rate>{{ rate_value }}</Rate>

<!-- Validate URL format -->
{% if is_valid_url(params.target_url) %}
  <TargetURL>{{ params.target_url }}</TargetURL>
{% endif %}

<!-- Validate header names -->
{% if is_valid_header_name(header_name) %}
  <Header name="{{ header_name }}">{{ header_value }}</Header>
{% endif %}
```

### Environment Awareness

Templates can adapt behavior based on the deployment environment:

```jinja2
<!-- Environment-specific defaults -->
{% if is_production() %}
  <Rate>50pm</Rate>  <!-- Conservative in production -->
{% else %}
  <Rate>100pm</Rate> <!-- More permissive in dev/test -->
{% endif %}

<!-- Environment-specific values -->
{% set timeout = get_env_specific_value({"dev": 5000, "prod": 30000}, 10000) %}
<Timeout>{{ timeout }}</Timeout>
```

### Feature Flags

Use feature flags to conditionally include advanced functionality:

```jinja2
<!-- Advanced features behind feature flags -->
{% if feature_enabled('advanced_traffic_management') %}
  <Identifier ref="{{ params.identifier_ref }}"/>
{% endif %}

<!-- Security features for production -->
{% if is_production() and feature_enabled('security_headers') %}
  <Header name="X-Content-Type-Options">nosniff</Header>
{% endif %}
```

### Safe Parameter Access

Always use safe parameter access to handle missing values:

```jinja2
<!-- Safe parameter access with .get() -->
{% if params.get('message_weight_ref') %}
  <MessageWeight ref="{{ params.message_weight_ref }}"/>
{% endif %}

<!-- Safe default with validation -->
{% set interval = safe_default(params.interval, 1, lambda x: isinstance(x, int) and x > 0) %}
<Interval>{{ interval }}</Interval>
```

## Template Examples

### Enhanced SpikeArrest Template

```xml
<SpikeArrest name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  
  <!-- Smart rate validation with environment defaults -->
  {% set rate_value = safe_default(params.rate, '100pm', is_valid_rate) %}
  {% if is_production() %}
    {% set rate_value = safe_default(params.rate, '50pm', is_valid_rate) %}
  {% endif %}
  
  <Rate>{{ rate_value }}</Rate>
  
  <!-- Optional advanced features -->
  {% if params.get('message_weight_ref') %}
    <MessageWeight ref="{{ params.message_weight_ref }}"/>
  {% endif %}
  
  {% if feature_enabled('advanced_traffic_management') and params.get('identifier_ref') %}
    <Identifier ref="{{ params.identifier_ref }}"/>
  {% endif %}
</SpikeArrest>
```

### Smart CORS Template

```xml
<AssignMessage name="{{ name }}">
  <DisplayName>{{ params.display_name | default(name) }}</DisplayName>
  <Set>
    <Headers>
      <!-- Smart origin handling -->
      {% set origins = normalize_list(params.get('allow_origins', []), ',') %}
      {% if not origins %}
        {% if is_production() %}
          {% set origins = ['https://app.example.com'] %}
        {% else %}
          {% set origins = ['*'] %}
        {% endif %}
      {% endif %}
      <Header name="Access-Control-Allow-Origin">
        {{ helpers.format_origins_list(origins) }}
      </Header>
      
      <!-- Environment-aware security headers -->
      {% if is_production() and feature_enabled('security_headers') %}
        <Header name="X-Content-Type-Options">nosniff</Header>
        <Header name="X-Frame-Options">DENY</Header>
      {% endif %}
    </Headers>
  </Set>
</AssignMessage>
```

## Best Practices

### 1. Always Use Safe Parameter Access

```jinja2
<!-- ❌ Bad: Direct access can cause errors -->
{% if params.optional_param %}

<!-- ✅ Good: Safe access with .get() -->
{% if params.get('optional_param') %}
```

### 2. Provide Sensible Defaults

```jinja2
<!-- ✅ Good: Always provide fallback values -->
{% set timeout = params.get('timeout', 30000) %}
{% set rate = safe_default(params.rate, '100pm', is_valid_rate) %}
```

### 3. Use Environment Awareness

```jinja2
<!-- ✅ Good: Different behavior per environment -->
{% if is_production() %}
  <!-- More restrictive settings -->
{% else %}
  <!-- More permissive for development -->
{% endif %}
```

### 4. Validate Parameters

```jinja2
<!-- ✅ Good: Validate before using -->
{% if is_valid_rate(params.rate) %}
  <Rate>{{ params.rate }}</Rate>
{% else %}
  <Rate>100pm</Rate>  <!-- Safe default -->
{% endif %}
```

### 5. Use Feature Flags for Optional Features

```jinja2
<!-- ✅ Good: Optional features behind flags -->
{% if feature_enabled('advanced_logging') %}
  <LogLevel>DEBUG</LogLevel>
{% endif %}
```

## Testing Templates

Use the template testing framework to validate your templates:

```python
from app.testing.template_tester import TemplateTestCase, TemplateTester

# Create test cases
test_case = TemplateTestCase(
    name="MyPolicy_Basic",
    template="MyPolicy.xml.j2",
    params={"rate": "100pm"},
    expected_elements=["Rate"],
    expected_values={"Rate": "100pm"}
)

# Run tests
tester = TemplateTester()
tester.add_test_case(test_case)
results = tester.run_all_tests()
```

## Available Helper Functions

| Function | Purpose | Example |
|----------|---------|---------|
| `is_valid_rate(rate)` | Validate rate format | `is_valid_rate("100pm")` |
| `is_valid_url(url)` | Validate URL format | `is_valid_url("https://example.com")` |
| `is_valid_header_name(name)` | Validate HTTP header name | `is_valid_header_name("Content-Type")` |
| `safe_default(value, default, validator)` | Safe default with validation | `safe_default(params.rate, "100pm", is_valid_rate)` |
| `normalize_list(value, separator)` | Convert to list | `normalize_list("a,b,c", ",")` |
| `is_production()` | Check if production env | `is_production()` |
| `feature_enabled(flag)` | Check feature flag | `feature_enabled("advanced_security")` |
| `xml_escape(text)` | Escape XML characters | `xml_escape(user_input)` |

## Migration from Legacy Templates

When migrating legacy templates:

1. Replace direct parameter access with `.get()` methods
2. Add validation using helper functions
3. Add environment-aware defaults
4. Use feature flags for optional functionality
5. Add comprehensive test cases

This ensures your templates are robust, maintainable, and environment-aware.
