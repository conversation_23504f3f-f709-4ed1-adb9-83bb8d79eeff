#!/usr/bin/env python3
"""
Setup configuration for Apigee Proxy Factory
============================================
Package configuration with dependency management for different environments.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
def read_requirements(filename):
    """Read requirements from file"""
    requirements_path = this_directory / filename
    if requirements_path.exists():
        with open(requirements_path, 'r', encoding='utf-8') as f:
            return [
                line.strip() 
                for line in f 
                if line.strip() and not line.startswith('#') and not line.startswith('-r')
            ]
    return []

# Core requirements
install_requires = read_requirements('requirements.txt')

# Optional requirements
extras_require = {
    'dev': read_requirements('requirements-dev.txt'),
    'test': read_requirements('requirements-test.txt'),
    'optional': read_requirements('requirements-optional.txt'),
    'all': (
        read_requirements('requirements-dev.txt') + 
        read_requirements('requirements-test.txt') + 
        read_requirements('requirements-optional.txt')
    )
}

setup(
    name="apigee-proxy-factory",
    version="1.0.0",
    description="Comprehensive Python framework for generating Apigee API proxy configurations with intelligent policy management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Apigee Proxy Factory Team",
    author_email="<EMAIL>",
    url="https://github.com/your-org/apigee-proxy-factory",
    
    # Package configuration
    packages=find_packages(exclude=['tests*', 'docs*']),
    include_package_data=True,
    package_data={
        'app': [
            'templates/**/*.j2',
            'config/**/*.yaml',
            'config/**/*.yml',
            'docs/**/*.md'
        ]
    },
    
    # Dependencies
    install_requires=install_requires,
    extras_require=extras_require,
    
    # Python version requirement
    python_requires=">=3.8",
    
    # Entry points
    entry_points={
        'console_scripts': [
            'apigee-proxy-factory=app.main:main',
            'apf-test=run_tests:main',
        ],
    },
    
    # Classifiers
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
        "Topic :: System :: Systems Administration",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
        "Framework :: FastAPI",
        "Environment :: Web Environment",
    ],
    
    # Keywords
    keywords=[
        "apigee", "api", "proxy", "gateway", "microservices", 
        "policy", "template", "configuration", "fastapi",
        "feature-flags", "validation", "enterprise"
    ],
    
    # Project URLs
    project_urls={
        "Bug Reports": "https://github.com/your-org/apigee-proxy-factory/issues",
        "Source": "https://github.com/your-org/apigee-proxy-factory",
        "Documentation": "https://apigee-proxy-factory.readthedocs.io/",
    },
    
    # Additional metadata
    zip_safe=False,
    platforms=['any'],
)
