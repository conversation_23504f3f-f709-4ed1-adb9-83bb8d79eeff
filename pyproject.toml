[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "apigee-proxy-factory"
version = "1.0.0"
description = "Comprehensive Python framework for generating Apigee API proxy configurations with intelligent policy management"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Apigee Proxy Factory Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Apigee Proxy Factory Team", email = "<EMAIL>"}
]
keywords = [
    "apigee", "api", "proxy", "gateway", "microservices", 
    "policy", "template", "configuration", "fastapi",
    "feature-flags", "validation", "enterprise"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: System :: Systems Administration",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
    "Framework :: FastAPI",
    "Environment :: Web Environment",
]
requires-python = ">=3.8"
dependencies = [
    "fastapi>=0.111.0",
    "uvicorn[standard]>=0.30.0",
    "pydantic>=2.8.0",
    "pydantic-settings>=2.3.0",
    "httpx>=0.27.0",
    "jinja2>=3.1.0",
    "lxml>=5.2.0",
    "PyYAML>=6.0.0",
    "python-multipart>=0.0.9",
    "structlog>=24.1.0",
    "orjson>=3.10.0",
    "markupsafe>=2.1.0",
    "jsonschema>=4.20.0",
    "cerberus>=1.3.5",
    "python-dotenv>=1.0.0",
    "cachetools>=5.3.0",
    "typing-extensions>=4.8.0",
    "cryptography>=41.0.0",
    "aiofiles>=23.2.0",
    "pytest>=7.4.0"
]

[project.optional-dependencies]
dev = [
    "pytest-cov>=4.1.0",
    "pytest-html>=4.1.0",
    "pytest-xdist>=3.3.0",
    "pytest-mock>=3.12.0",
    "pytest-asyncio>=0.21.0",
    "pytest-timeout>=2.2.0",
    "pytest-benchmark>=4.0.0",
    "pytest-watch>=4.2.0",
    "responses>=0.24.0",
    "factory-boy>=3.3.0",
    "faker>=20.0.0",
    "freezegun>=1.2.0",
    "httpx-mock>=0.10.0",
    "flake8>=6.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "mypy>=1.6.0",
    "bandit>=1.7.5",
    "pylint>=3.0.0",
    "coverage>=7.3.0",
    "pre-commit>=3.5.0",
    "sphinx>=7.2.0",
    "mkdocs>=1.5.0",
    "ipython>=8.16.0"
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-html>=4.1.0",
    "pytest-mock>=3.12.0",
    "responses>=0.24.0",
    "faker>=20.0.0",
    "freezegun>=1.2.0",
    "coverage>=7.3.0",
    "bandit>=1.7.5",
    "safety>=2.3.0"
]
optional = [
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "prometheus-client>=0.18.0",
    "opentelemetry-api>=1.20.0",
    "sentry-sdk>=1.35.0",
    "rich>=13.6.0"
]

[project.urls]
Homepage = "https://github.com/your-org/apigee-proxy-factory"
Documentation = "https://apigee-proxy-factory.readthedocs.io/"
Repository = "https://github.com/your-org/apigee-proxy-factory.git"
"Bug Tracker" = "https://github.com/your-org/apigee-proxy-factory/issues"
Changelog = "https://github.com/your-org/apigee-proxy-factory/blob/main/CHANGELOG.md"

[project.scripts]
apigee-proxy-factory = "app.main:main"
apf-test = "run_tests:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["app*"]
exclude = ["tests*", "docs*"]

[tool.setuptools.package-data]
app = [
    "templates/**/*.j2",
    "config/**/*.yaml",
    "config/**/*.yml",
    "docs/**/*.md"
]

# Black configuration
[tool.black]
line-length = 120
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy configuration
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "lxml.*",
    "structlog.*",
    "yaml.*",
    "jinja2.*"
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-fail-under=80"
]
markers = [
    "integration: Integration tests that may require external services",
    "slow: Tests that take longer than usual to run",
    "external: Tests that require external services",
    "unit: Unit tests (default)",
    "api: API endpoint tests",
    "services: Service layer tests",
    "config: Configuration tests",
    "templates: Template tests",
    "performance: Performance tests"
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]
timeout = 300

# Coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

# Bandit security configuration
[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
skips = ["B101", "B601"]

# Flake8 configuration (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See setup.cfg for flake8 configuration
