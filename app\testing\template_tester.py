"""
Template Testing Framework
=========================
Comprehensive testing system for policy templates with validation and edge case handling.
Provides automated testing of template rendering, parameter validation, and conditional logic.
"""

import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
import structlog

from ..services.policy_builder import PolicyBuilder
from ..services.template_helpers import TemplateHelpers

log = structlog.get_logger(__name__)


class TemplateTestCase:
    """Represents a single template test case"""
    
    def __init__(self, name: str, template: str, params: Dict[str, Any], 
                 expected_elements: Optional[List[str]] = None,
                 expected_values: Optional[Dict[str, str]] = None,
                 should_fail: bool = False,
                 env_name: str = "test",
                 features: Optional[Dict[str, Any]] = None):
        self.name = name
        self.template = template
        self.params = params
        self.expected_elements = expected_elements or []
        self.expected_values = expected_values or {}
        self.should_fail = should_fail
        self.env_name = env_name
        self.features = features or {}


class TemplateTestResult:
    """Results of a template test"""
    
    def __init__(self, test_case: TemplateTestCase, success: bool, 
                 rendered_xml: Optional[str] = None, error: Optional[str] = None,
                 validation_errors: Optional[List[str]] = None):
        self.test_case = test_case
        self.success = success
        self.rendered_xml = rendered_xml
        self.error = error
        self.validation_errors = validation_errors or []


class TemplateTester:
    """Main template testing framework"""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        self.templates_dir = templates_dir or Path("app/templates/policies")
        self.policy_builder = PolicyBuilder()
        self.test_cases: List[TemplateTestCase] = []
    
    def add_test_case(self, test_case: TemplateTestCase):
        """Add a test case to the suite"""
        self.test_cases.append(test_case)
    
    def run_test_case(self, test_case: TemplateTestCase) -> TemplateTestResult:
        """Run a single test case"""
        try:
            # Initialize template helpers with test environment
            helpers = TemplateHelpers(env_name=test_case.env_name, features=test_case.features)
            self.policy_builder._template_helpers = helpers
            
            # Render the template
            rendered_xml = self.policy_builder._render_template(
                test_case.template,
                name=f"TEST-{test_case.name}",
                params=test_case.params,
                input={"methods": ["GET"], "headers": ["Authorization"]},
                env={"name": test_case.env_name}
            )
            
            if test_case.should_fail:
                return TemplateTestResult(
                    test_case, False, rendered_xml, 
                    "Expected test to fail but it succeeded"
                )
            
            # Validate XML structure
            validation_errors = self._validate_xml(rendered_xml, test_case)
            
            success = len(validation_errors) == 0
            return TemplateTestResult(test_case, success, rendered_xml, None, validation_errors)
            
        except Exception as e:
            if test_case.should_fail:
                return TemplateTestResult(test_case, True, None, str(e))
            else:
                return TemplateTestResult(test_case, False, None, str(e))
    
    def _validate_xml(self, xml: str, test_case: TemplateTestCase) -> List[str]:
        """Validate rendered XML against test expectations"""
        errors = []
        
        try:
            # Parse XML
            root = ET.fromstring(xml)
            
            # Check expected elements exist
            for element_path in test_case.expected_elements:
                if not self._find_element(root, element_path):
                    errors.append(f"Expected element not found: {element_path}")
            
            # Check expected values
            for element_path, expected_value in test_case.expected_values.items():
                element = self._find_element(root, element_path)
                if element is None:
                    errors.append(f"Element not found for value check: {element_path}")
                elif element.text != expected_value:
                    errors.append(f"Value mismatch for {element_path}: expected '{expected_value}', got '{element.text}'")
            
        except ET.ParseError as e:
            errors.append(f"Invalid XML: {str(e)}")
        
        return errors
    
    def _find_element(self, root: ET.Element, path: str) -> Optional[ET.Element]:
        """Find element by path (simplified XPath)"""
        try:
            # Handle simple element names
            if '/' not in path and '[' not in path:
                return root.find(f".//{path}")

            # Handle XPath-like paths
            return root.find(path)
        except:
            # Fallback: search in XML text
            return None
    
    def run_all_tests(self) -> List[TemplateTestResult]:
        """Run all test cases"""
        results = []
        for test_case in self.test_cases:
            result = self.run_test_case(test_case)
            results.append(result)
        return results
    
    def generate_test_report(self, results: List[TemplateTestResult]) -> str:
        """Generate a comprehensive test report"""
        total_tests = len(results)
        passed_tests = sum(1 for r in results if r.success)
        failed_tests = total_tests - passed_tests
        
        report = []
        report.append("=" * 60)
        report.append("TEMPLATE TEST REPORT")
        report.append("=" * 60)
        report.append(f"Total Tests: {total_tests}")
        report.append(f"Passed: {passed_tests}")
        report.append(f"Failed: {failed_tests}")
        report.append(f"Success Rate: {(passed_tests/total_tests*100):.1f}%")
        report.append("")
        
        # Detailed results
        for result in results:
            status = "✅ PASS" if result.success else "❌ FAIL"
            report.append(f"{status} {result.test_case.name}")
            
            if not result.success:
                if result.error:
                    report.append(f"  Error: {result.error}")
                if result.validation_errors:
                    for error in result.validation_errors:
                        report.append(f"  Validation: {error}")
                report.append("")
        
        return "\n".join(report)


def create_standard_test_suite() -> TemplateTester:
    """Create a standard test suite for common policy templates"""
    tester = TemplateTester()
    
    # SpikeArrest Tests
    tester.add_test_case(TemplateTestCase(
        name="SpikeArrest_Basic",
        template="SpikeArrest.xml.j2",
        params={"rate": "100pm"},
        expected_elements=["Rate"],
        expected_values={"Rate": "100pm"}
    ))
    
    tester.add_test_case(TemplateTestCase(
        name="SpikeArrest_Invalid_Rate",
        template="SpikeArrest.xml.j2",
        params={"rate": "invalid"},
        expected_values={"Rate": "100pm"}  # Should fallback to default
    ))
    
    tester.add_test_case(TemplateTestCase(
        name="SpikeArrest_Production_Default",
        template="SpikeArrest.xml.j2",
        params={},
        expected_values={"Rate": "50pm"},  # Production default
        env_name="prod"
    ))
    
    # Quota Tests
    tester.add_test_case(TemplateTestCase(
        name="Quota_Basic",
        template="Quota.xml.j2",
        params={"allow": "1000pm", "interval": "1", "timeunit": "minute"},
        expected_elements=["Allow", "Interval", "TimeUnit", "Identifier"],
        expected_values={"Interval": "1", "TimeUnit": "minute"}
    ))
    
    tester.add_test_case(TemplateTestCase(
        name="Quota_Invalid_TimeUnit",
        template="Quota.xml.j2",
        params={"allow": "1000pm", "timeunit": "invalid"},
        expected_values={"TimeUnit": "minute"}  # Should fallback to default
    ))
    
    # CORS Tests
    tester.add_test_case(TemplateTestCase(
        name="CORS_Basic",
        template="AssignMessage.CORS.xml.j2",
        params={"allow_origins": ["https://example.com"]},
        expected_elements=["Set", "Headers"]
    ))

    tester.add_test_case(TemplateTestCase(
        name="CORS_Production_Security",
        template="AssignMessage.CORS.xml.j2",
        params={},
        env_name="prod",
        features={"security_headers": True},
        expected_elements=["Set", "Headers"]
    ))
    
    # Feature Flag Tests
    tester.add_test_case(TemplateTestCase(
        name="SpikeArrest_Advanced_Features",
        template="SpikeArrest.xml.j2",
        params={"rate": "100pm", "identifier_ref": "request.header.client_id"},
        features={"advanced_traffic_management": True},
        expected_elements=["Identifier"]
    ))
    
    return tester
