from __future__ import annotations

import json
from typing import Any, Dict, Optional

from fastapi import APIRouter, File, Form, HTTPException, UploadFile
from typing import Optional

from ....core.errors import ValidationError
from ....services.orchestrator import (
    BootstrapRequest as OrchestratorBootstrapRequest,
    GitOptions as OrchestratorGitOptions,
    Orchestrator,
)
from ..models.bootstrap import BootstrapResponse, FeatureImpactSummary, PolicyRecommendation

router = APIRouter()


@router.post(
    "/bootstrap",
    response_model=BootstrapResponse,
    summary="Bootstrap a new Apigee proxy (rev 1) from Swagger/OAS with enhanced policy parameter support",
)
async def bootstrap(
    # --- swagger source (either file or URL) ---
    swagger_file: Optional[UploadFile] = File(None, description="Swagger/OpenAPI file (JSON or YAML) - optional if swaggerUrl is provided"),
    swaggerUrl: Optional[str] = Form(None, description="URL to fetch Swagger/OpenAPI specification from - optional if swagger_file is provided"),

    # --- required metadata ---
    passcode: str = Form(..., description="Apigee Edge SSO passcode"),
    systemName: str = Form(..., description="System key (matches systems/<name>.yaml)"),
    proxyName: str = Form(..., description="Apigee proxy name to create"),

    # --- policy configuration options ---
    policyParams: Optional[str] = Form(
        None,
        description='JSON string of policy parameters using direct policy name mapping. '
                   'Example: {"VerifyApiKey": {"keyref": "request.header.x-custom-key"}, '
                   '"CORS": {"allow_origins": ["https://app.example.com"]}}'
    ),

    policyGroup: Optional[str] = Form(
        None,
        description='Policy group/template names to apply. Can be a single group (e.g., "basic_security") or '
                   'comma-separated multiple groups (e.g., "basic_security,cors_enabled,high_security"). '
                   'When specified, applies all policies from all groups. Can be combined with policyParams for customization.'
    ),

    # --- mandatory Git commit message ---
    gitCommitMessage: str = Form(..., description="Commit message (repo URL and branch read from system config)"),
):
    """
    Bootstrap a new Apigee proxy from Swagger/OAS with enhanced swagger processing and Bruno collection generation.

    This endpoint creates a new proxy (revision 1) for LOWER ENVIRONMENTS ONLY (dev/test) with:
    - **Swagger URL Support**: Fetch swagger from URL instead of file upload
    - **Proxy Swagger Generation**: Creates Apigee-specific swagger with proxy endpoints
    - **Bruno Collection**: Automatically generates Bruno API collection for testing
    - **Git Integration**: Pushes both proxy bundle and Bruno collection to GitLab
    - Automatic policy attachment based on system configuration and policy groups
    - Feature-driven policy selection based on system-defined feature flags
    - Enhanced parameter resolution (system defaults < env overrides < input params)

    **Swagger Source Options:**
    - `swagger_file`: Upload swagger file directly (JSON or YAML)
    - `swaggerUrl`: Fetch swagger from URL (alternative to file upload)

    **Policy Configuration Options:**
    - `policyParams`: Direct policy parameter mapping using policy names as keys
    - `policyGroup`: Apply pre-configured policy groups/templates (supports comma-separated multiple groups)
    - System configuration provides base policies and feature flags
    - Environment configuration can override system defaults

    **Generated Artifacts:**
    - Apigee proxy bundle with transformed swagger
    - Bruno API collection for endpoint testing
    - Git repository with both artifacts in organized structure

    **Parameter Precedence:** Template Defaults < System Config < Environment Config < Input Parameters

    Note: For production deployments, use the separate production deployment pipeline.
    """

    # Validate environment - bootstrap API only for lower environments
    from ....core.settings import settings
    current_env = getattr(settings, 'apigee_env', 'test').lower()
    if current_env in ['prod', 'production']:
        raise HTTPException(
            status_code=403,
            detail="Bootstrap API is not available for production environment. Use production deployment pipeline instead."
        )

    try:
        # Validate swagger source - either file or URL must be provided
        if not swagger_file and not swaggerUrl:
            raise ValidationError("Either swagger_file or swaggerUrl must be provided")

        if swagger_file and swaggerUrl:
            raise ValidationError("Provide either swagger_file or swaggerUrl, not both")

        # Read swagger content
        swagger_bytes = b""
        if swagger_file:
            swagger_bytes = await swagger_file.read()
            if not swagger_bytes:
                raise ValidationError("Uploaded swagger_file is empty")

        # Removed deprecated swagger headers and apigee schemes parsing

        # Parse dynamic parameters
        parsed_policy_params = {}

        try:
            if policyParams:
                parsed_policy_params = json.loads(policyParams)
                if not isinstance(parsed_policy_params, dict):
                    raise ValidationError("policyParams must be a JSON object")

        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON in parameter: {str(e)}")

        # Handle policy group expansion and combination with system policies
        combined_policy_params = parsed_policy_params.copy()

        if policyGroup:
            try:
                from ....services.policy_catalog import get_policy_catalog
                catalog = get_policy_catalog()

                # Parse comma-separated policy groups
                policy_groups = [group.strip() for group in policyGroup.split(',') if group.strip()]

                if not policy_groups:
                    raise ValidationError("No valid policy groups found in policyGroup parameter")

                # Validate all groups exist
                invalid_groups = []
                for group_name in policy_groups:
                    if not catalog.validate_group_exists(group_name):
                        invalid_groups.append(group_name)

                if invalid_groups:
                    raise ValidationError(f"Policy groups do not exist: {', '.join(invalid_groups)}")

                # Collect policies from all groups
                all_group_policies = set()
                for group_name in policy_groups:
                    group_policies = catalog.get_group_policies(group_name)
                    all_group_policies.update(group_policies)

                # Add group policies to combined policy params if not already specified
                for policy_name in all_group_policies:
                    if policy_name not in combined_policy_params:
                        # Add policy with empty params (will use system/env defaults)
                        combined_policy_params[policy_name] = {}

            except Exception as e:
                raise ValidationError(f"Failed to process policy groups '{policyGroup}': {str(e)}")

        # Store the combined policy params for the orchestrator
        parsed_policy_params = combined_policy_params

        # Git options - commit message is mandatory, repo URL and branch from system config
        git_opts = OrchestratorGitOptions(
            commit_message=gitCommitMessage,
            # repo_url and branch will be read from system config
        )

        orch_req = OrchestratorBootstrapRequest(
            passcode=passcode,
            system_name=systemName,
            proxy_name=proxyName,
            swagger_content=swagger_bytes,
            git=git_opts,
            # Enhanced parameters
            policy_params=parsed_policy_params,
            # Swagger processing options
            swagger_url=swaggerUrl,
        )

        # Execute orchestration
        orch = Orchestrator()
        result = orch.bootstrap(orch_req)

        # Create feature impact summary for response
        feature_impact_summary = None
        if result.feature_impact:
            feature_impact_summary = FeatureImpactSummary(
                policies_added=result.feature_impact.get("policies_added", []),
                policies_removed=result.feature_impact.get("policies_removed", []),
                policies_modified=result.feature_impact.get("policies_modified", []),
                recommendations=[
                    PolicyRecommendation(**rec) for rec in result.feature_impact.get("recommendations", [])
                ],
                active_feature_flags=result.feature_impact.get("active_feature_flags", {})
            )

        return BootstrapResponse(
            proxyName=result.proxy_name,
            revision=result.revision,
            apigeeOrg=result.org,
            apigeeEnv=result.env,
            bundleSha256=result.bundle_sha256,
            exportSize=result.export_size,
            gitCommitId=result.git_commit_id,
            gitWebUrl=result.git_web_url,
            feature_impact=feature_impact_summary,
            policies_generated=result.policies_generated or [],
            environment_notes=result.environment_notes,
        )

    except ValidationError as ve:
        # Domain-style error; let global handler format it
        raise ve
    except HTTPException:
        raise
    except Exception as e:
        # Bubble to global unhandled exception handler
        raise e