<DecodeJWS name="{{ name }}">
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}     <!-- variable containing compact JWS -->
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.algorithm %}<Algorithm>{{ params.algorithm }}</Algorithm>{% endif %}
  {% if params.public_key %}
  <PublicKey>
    {% if params.public_key.ref %}<Value ref="{{ params.public_key.ref }}"/>{% elif params.public_key.inline %}<Value>{{ params.public_key.inline }}</Value>{% endif %}
  </PublicKey>
  {% endif %}
</DecodeJWS>