"""
Template Helper Functions for Smart Policy Templates
==================================================
Provides validation, utility functions, and smart parameter handling for Jinja2 templates.
These helpers enable intelligent template rendering with validation and conditional logic.
"""

import re
from typing import Any, Dict, List, Optional, Tuple, Union
import structlog

log = structlog.get_logger(__name__)


class TemplateHelpers:
    """Collection of helper functions for policy templates"""
    
    def __init__(self, env_name: str = "test", features: Optional[Dict[str, Any]] = None):
        self.env_name = env_name
        self.features = features or {}
    
    # ==================== Validation Helpers ====================
    
    def is_valid_rate(self, rate: str) -> bool:
        """
        Validate rate format for SpikeArrest and Quota policies.
        Valid formats: 100pm, 10ps, 500ph, 1000pd
        """
        if not isinstance(rate, str):
            return False
        pattern = r'^\d+p[smhd]$'
        return bool(re.match(pattern, rate.strip()))
    
    def is_valid_url(self, url: str) -> bool:
        """Validate URL format"""
        if not isinstance(url, str):
            return False
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(url_pattern, url.strip()))
    
    def is_valid_header_name(self, header: str) -> bool:
        """Validate HTTP header name format"""
        if not isinstance(header, str):
            return False
        # RFC 7230 compliant header name
        pattern = r'^[!#$%&\'*+\-.0-9A-Z^_`a-z|~]+$'
        return bool(re.match(pattern, header.strip()))
    
    def is_valid_json_path(self, path: str) -> bool:
        """Validate JSONPath expression"""
        if not isinstance(path, str):
            return False
        # Basic JSONPath validation
        return path.strip().startswith('$.')
    
    def is_valid_xpath(self, xpath: str) -> bool:
        """Validate XPath expression"""
        if not isinstance(xpath, str):
            return False
        # Basic XPath validation
        xpath = xpath.strip()
        return xpath.startswith('/') or xpath.startswith('./') or xpath.startswith('../')
    
    def is_valid_variable_name(self, name: str) -> bool:
        """Validate Apigee flow variable name"""
        if not isinstance(name, str):
            return False
        # Apigee variable names: alphanumeric, dots, underscores, hyphens
        pattern = r'^[a-zA-Z][a-zA-Z0-9._-]*$'
        return bool(re.match(pattern, name.strip()))

    def parse_rate_format(self, rate: str) -> Tuple[str, str]:
        """
        Parse rate format (e.g., '500pm') into count and time unit.
        Returns tuple of (count, timeunit) where timeunit is the full word.

        Examples:
        - '500pm' -> ('500', 'minute')
        - '100ps' -> ('100', 'second')
        - '200ph' -> ('200', 'hour')
        - '10pd' -> ('10', 'day')
        """
        if not isinstance(rate, str) or not self.is_valid_rate(rate):
            return ('500', 'minute')  # Default fallback

        # Extract number and time unit suffix
        pattern = r'^(\d+)p([smhd])$'
        match = re.match(pattern, rate.strip())
        if not match:
            return ('500', 'minute')  # Default fallback

        count = match.group(1)
        unit_suffix = match.group(2)

        # Map suffix to full time unit name
        unit_mapping = {
            's': 'second',
            'm': 'minute',
            'h': 'hour',
            'd': 'day'
        }

        timeunit = unit_mapping.get(unit_suffix, 'minute')
        return (count, timeunit)

    # ==================== Utility Helpers ====================
    
    def safe_default(self, value: Any, default: Any, validator: Optional[callable] = None) -> Any:
        """
        Safely provide default value with optional validation.
        If value is invalid or None, returns default.
        """
        if value is None:
            return default
        
        if validator and not validator(value):
            log.warning("invalid_parameter_using_default", value=value, default=default)
            return default
        
        return value
    
    def normalize_list(self, value: Any, separator: str = ',') -> List[str]:
        """
        Normalize value to a list of strings.
        Handles: strings (split by separator), lists, single values
        """
        if value is None:
            return []
        
        if isinstance(value, list):
            return [str(item).strip() for item in value if item is not None]
        
        if isinstance(value, str):
            if separator in value:
                return [item.strip() for item in value.split(separator) if item.strip()]
            return [value.strip()] if value.strip() else []
        
        return [str(value).strip()]
    
    def format_headers_list(self, headers: List[str]) -> str:
        """Format list of headers for CORS policies"""
        if not headers:
            return ""
        
        # Validate and normalize headers
        valid_headers = []
        for header in headers:
            if self.is_valid_header_name(header):
                valid_headers.append(header.lower())
            else:
                log.warning("invalid_header_skipped", header=header)
        
        return ','.join(valid_headers)
    
    def format_origins_list(self, origins: List[str]) -> str:
        """Format list of origins for CORS policies"""
        if not origins:
            return "*"
        
        # Validate origins
        valid_origins = []
        for origin in origins:
            if origin == "*" or self.is_valid_url(origin):
                valid_origins.append(origin)
            else:
                log.warning("invalid_origin_skipped", origin=origin)
        
        return ','.join(valid_origins) if valid_origins else "*"
    
    # ==================== Environment & Feature Helpers ====================
    
    def is_production(self) -> bool:
        """Check if current environment is production"""
        return self.env_name.lower() in ['prod', 'production']
    
    def is_development(self) -> bool:
        """Check if current environment is development"""
        return self.env_name.lower() in ['dev', 'development', 'test']
    
    def feature_enabled(self, feature_name: str) -> bool:
        """Check if a feature flag is enabled"""
        return bool(self.features.get(feature_name, False))
    
    def get_env_specific_value(self, values: Dict[str, Any], default: Any = None) -> Any:
        """
        Get environment-specific value from a dict.
        Example: {"dev": "debug", "prod": "error"} -> returns based on current env
        """
        if not isinstance(values, dict):
            return default
        
        # Try exact match first
        if self.env_name in values:
            return values[self.env_name]
        
        # Try common aliases
        env_aliases = {
            'dev': ['development', 'test'],
            'prod': ['production'],
            'staging': ['stage', 'uat']
        }
        
        for env, aliases in env_aliases.items():
            if self.env_name.lower() in aliases and env in values:
                return values[env]
        
        # Return default value from dict or provided default
        return values.get('default', default)
    
    # ==================== Conditional Logic Helpers ====================
    
    def should_include_policy(self, policy_name: str, conditions: Dict[str, Any]) -> bool:
        """
        Determine if a policy should be included based on conditions.
        Conditions can include environment, features, parameters, etc.
        """
        if not conditions:
            return True
        
        # Environment conditions
        if 'env' in conditions:
            env_condition = conditions['env']
            if isinstance(env_condition, str):
                if self.env_name != env_condition:
                    return False
            elif isinstance(env_condition, list):
                if self.env_name not in env_condition:
                    return False
        
        # Feature flag conditions
        if 'features' in conditions:
            feature_conditions = conditions['features']
            if isinstance(feature_conditions, dict):
                for feature, required_value in feature_conditions.items():
                    if self.features.get(feature) != required_value:
                        return False
            elif isinstance(feature_conditions, list):
                for feature in feature_conditions:
                    if not self.feature_enabled(feature):
                        return False
        
        # Parameter existence conditions
        if 'requires_params' in conditions:
            # This would need to be checked by the template with actual params
            pass
        
        return True
    
    def get_security_level(self) -> str:
        """Get security level based on environment and features"""
        if self.is_production():
            return "high"
        elif self.feature_enabled("advanced_security"):
            return "medium"
        else:
            return "basic"
    
    # ==================== Template Utilities ====================
    
    def xml_escape(self, value: str) -> str:
        """Escape XML special characters"""
        if not isinstance(value, str):
            value = str(value)
        
        replacements = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&apos;'
        }
        
        for char, escape in replacements.items():
            value = value.replace(char, escape)
        
        return value
    
    def generate_condition(self, path_pattern: str, verb: str = None, 
                          include_verb: bool = True) -> str:
        """
        Generate flow condition string for Apigee.
        Example: "(proxy.pathsuffix MatchesPath '/users/*') and (request.verb = 'GET')"
        """
        condition_parts = []
        
        # Path condition
        if path_pattern:
            if '*' in path_pattern or '{' in path_pattern:
                condition_parts.append(f"(proxy.pathsuffix MatchesPath '{path_pattern}')")
            else:
                condition_parts.append(f"(proxy.pathsuffix = '{path_pattern}')")
        
        # Verb condition
        if include_verb and verb:
            condition_parts.append(f"(request.verb = '{verb.upper()}')")
        
        return " and ".join(condition_parts) if condition_parts else ""
