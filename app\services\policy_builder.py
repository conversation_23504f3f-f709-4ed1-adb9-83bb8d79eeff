"""
PolicyBuilder (env-aware, catalog-driven)
----------------------------------------
Renders Apigee policy XMLs from Jinja2 templates using 3 sources of param values:

  DEFAULT < ENV < INPUT

- DEFAULT: system YAML (systems/<system>.yaml)
- ENV:     per-env YAML (env/<env>/<system>.yaml), kept outside the system file
- INPUT:   API request payload + OAS-derived values (supports list ops)

Per-param merge semantics are defined by a small "param catalog" YAML, so lists
(e.g., CORS headers) can default to append_unique, while origins may replace, etc.

Templates should only reference {{ params.* }}; no default values in templates.
"""

from __future__ import annotations

from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import jinja2
import structlog
import yaml

from ..core.errors import ValidationError
from ..core.settings import settings
from .template_helpers import TemplateHelpers
from .feature_flags import get_feature_evaluator
from .conditional_policies import get_conditional_attacher

log = structlog.get_logger(__name__)


# ---------------------- Data models ----------------------

@dataclass(frozen=True)
class PolicyRender:
    name: str
    xml: str


@dataclass(frozen=True)
class AttachPlan:
    preflow_request: List[str]
    preflow_response: List[str]
    perflow_request: List[str]
    perflow_response: List[str]
    cors_policy: Optional[str] = None  # if configured/enabled
    # Enhanced flows for new proxy endpoint structure
    postflow_request: List[str] = None
    postflow_response: List[str] = None
    postclientflow_request: List[str] = None
    postclientflow_response: List[str] = None
    nomatchfound_request: List[str] = None
    nomatchfound_response: List[str] = None


# ---------------------- Param catalog ----------------------

# Built-in param catalog defaults; can be overridden by YAML (see _load_param_catalog)
# type: string|int|bool|list ; merge for lists: replace|append_unique|prepend_unique|remove_only
# normalize: lower|exact (applies to list items)
_BUILTIN_PARAM_CATALOG: Dict[str, Dict[str, Dict[str, Any]]] = {
    "CORS": {
        "allow_origins":     {"type": "list", "merge": "replace",       "normalize": "exact"},
        "allow_methods":     {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "allow_headers":     {"type": "list", "merge": "append_unique", "normalize": "lower"},
        "expose_headers":    {"type": "list", "merge": "append_unique", "normalize": "exact"},
        "max_age":           {"type": "int",  "merge": "replace"},
        "allow_credentials": {"type": "bool", "merge": "replace"},
    },
    "Quota": {
        "allow":          {"type": "string", "merge": "replace"},
        "interval":       {"type": "string", "merge": "replace"},
        "timeunit":       {"type": "string", "merge": "replace"},
        "identifier_ref": {"type": "string", "merge": "replace"},
    },
    "Spike-Arrest": {
        "rate": {"type": "string", "merge": "replace"},
    },
    "Verify-API-Key": {
        "keyref": {"type": "string", "merge": "replace"},
    },
    # Add others as needed (OAuthV2, ThreatProtection...)
}


def _load_param_catalog() -> Dict[str, Dict[str, Dict[str, Any]]]:
    """
    Load param catalog from consolidated policy catalog (template-based) or fall back to built-in defaults.
    """
    try:
        from .policy_catalog import get_policy_catalog
        catalog = get_policy_catalog()

        # Build parameter catalog from template information
        param_catalog = {}
        for template_name in catalog.list_all_template_files():
            template_config = catalog.get_template_param_config(template_name)
            if template_config:
                # Use template name as key for parameter configuration
                param_catalog[template_name] = template_config

        # Merge with built-in defaults
        merged = {k: dict(v) for k, v in _BUILTIN_PARAM_CATALOG.items()}
        for template, spec in param_catalog.items():
            merged.setdefault(template, {})
            for k, cfg in (spec or {}).items():
                merged[template][k] = cfg or {}

        log.info("param_catalog_loaded", source="policy_catalog.yaml", policies=len(param_catalog))
        return merged
    except Exception as e:
        log.warning("param_catalog_failed", error=str(e), using_builtin=True)
        return _BUILTIN_PARAM_CATALOG





# ---------------------- Policy builder ----------------------

class PolicyBuilder:
    """
    Enhanced PolicyBuilder for Apigee proxy policy generation and attachment planning.

    Implements clean code architecture with single responsibility principle:
    - Policy rendering from templates with parameter resolution
    - Attachment plan generation for flow-based policy execution
    - Template-based policy correlation with direct specification
    - Multiple policy instances support with unique naming

    Architecture:
    - Uses dependency injection for template engine and configuration loader
    - Implements comprehensive error handling with structured logging
    - Follows parameter resolution hierarchy: system < environment < input
    - Supports feature-driven policy selection with system-level evaluation

    Configuration Structure (New Optimized Format):
        policies:
          AM-SetRequestData:
            template: "AssignMessage.xml.j2"
            display_name: "AM-SetRequestData"
            set:
              headers:
                - name: "X-Request-ID"
                  value: "{messageid}"

        flows:
          proxy:
            preflow:
              request: ["VK-VerifyApiKey", "AM-SetRequestData"]
              response: ["AM-AddRespHeaders"]
            per_operation:
              request: ["Q-ImposeQuota"]
              response: ["FC-ExtractCustomLogs"]
            postclientflow:
              response: ["FC-PushLogsToPubSub"]
            nomatchfound:
              request: ["RF-ResourceNotFound"]

    Returns:
        Tuple[List[PolicyRender], AttachPlan]: Rendered policies and attachment plan

    Raises:
        ValidationError: For configuration validation failures
        TemplateError: For template rendering issues
    """

    def __init__(self, templates_dir: Optional[str] = None) -> None:
        tdir = templates_dir or settings.templates_dir
        base = Path(tdir).joinpath("policies").resolve()
        if not base.exists():
            raise ValidationError(
                f"Policies templates directory not found: {base}",
                details={"templates_dir": str(base)},
            )
        self._env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(base)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
        self._env.undefined = jinja2.StrictUndefined
        self._templates_root = base

        self._param_catalog = _load_param_catalog()
        # Template mapping removed - using direct template specification in policies

        # Initialize template helpers (will be updated per render call)
        self._template_helpers = None

        # Initialize policy catalog for validation
        try:
            from .policy_catalog import get_policy_catalog
            self._policy_catalog = get_policy_catalog()
        except Exception as e:
            log.warning("policy_catalog_init_failed", error=str(e))
            self._policy_catalog = None

    def render_policies(
        self,
        system_cfg: Dict[str, Any],
        input_vars: Dict[str, Any],
        *,
        system_name: Optional[str] = None,   # used to load env overrides if provided
    ) -> Tuple[List[PolicyRender], AttachPlan]:
        """
        Render policies and generate attachment plan using enhanced configuration structure.

        Implements the core policy rendering logic with template-based correlation,
        parameter resolution hierarchy, and comprehensive flow support.

        Args:
            system_cfg: System configuration containing flows.proxy.* and policies.*
            input_vars: Input variables with policy_params for parameter overrides
            system_name: Optional system name for environment-aware configuration

        Returns:
            Tuple containing:
            - List[PolicyRender]: Rendered policy objects with XML content
            - AttachPlan: Policy attachment plan for all flow types

        Raises:
            ValidationError: For configuration validation failures
            TemplateError: For template rendering issues

        Configuration Structure Expected:
            flows:
              proxy:
                preflow:
                  request: ["VK-VerifyApiKey", "SA-SpikeArrest"]
                  response: ["AM-AddRespHeaders"]
                postclientflow:
                  response: ["FC-PushLogsToPubSub"]
                nomatchfound:
                  request: ["RF-ResourceNotFound"]
            policies:
              VK-VerifyApiKey:
                template: "VerifyApiKey.xml.j2"
                keyref: "request.header.x-api-key"

        Architecture:
        - Validates configuration structure (flows.proxy.*, policies.*)
        - Resolves templates directly from policy definitions
        - Applies parameter resolution hierarchy (system < env < input)
        - Generates comprehensive AttachPlan for all flow types
        - Implements comprehensive error handling with structured logging
        """
        # Initialize template helpers with current context
        env_name = getattr(settings, 'apigee_env', 'test')
        # Features are now system-driven, loaded from feature flags service
        try:
            from .feature_flags import get_feature_evaluator
            get_feature_evaluator(env_name, system_name)
            # Create a basic features dict for template helpers
            system_features = {}
        except Exception:
            system_features = {}

        self._template_helpers = TemplateHelpers(env_name=env_name, features=system_features)

        # Initialize feature flag evaluator
        feature_evaluator = get_feature_evaluator(env_name, system_name)

        # Build context for conditional policy evaluation
        context = {
            "env_name": env_name,
            "system_name": system_name,
            "methods": input_vars.get("methods", []),
            "headers": input_vars.get("headers", []),
            "features": system_features,  # Use system_features instead of undefined features
            "security_policies": [],  # Will be populated as we process policies
            "content_types": self._extract_content_types(input_vars),
        }

        """
        Process system configuration using the new optimized structure.
        Expected structure: flows.proxy.* and policies.*
        """
        system_cfg = system_cfg or {}

        # Extract flows configuration - expect flows.proxy structure
        flows_cfg = system_cfg.get("flows", {})
        if "proxy" not in flows_cfg:
            raise ValidationError(
                "Invalid system configuration: expected 'flows.proxy' structure",
                details={"available_keys": list(flows_cfg.keys())}
            )

        proxy_flows = flows_cfg.get("proxy", {})
        preflow_req = _normalize_entries(proxy_flows.get("preflow", {}).get("request", []))
        preflow_resp = _normalize_entries(proxy_flows.get("preflow", {}).get("response", []))
        perflow_req = _normalize_entries(proxy_flows.get("per_operation", {}).get("request", []))
        perflow_resp = _normalize_entries(proxy_flows.get("per_operation", {}).get("response", []))

        # Extract target flow policies - these also need to be rendered
        target_flow_policies = self._extract_target_flow_policies(system_cfg)

        # CORS configuration
        cors_cfg = flows_cfg.get("cors", {})

        # Policy definitions - expect policies.* structure
        sys_defaults = system_cfg.get("policies", {})
        if not sys_defaults:
            raise ValidationError(
                "Invalid system configuration: 'policies' section is required",
                details={"available_keys": list(system_cfg.keys())}
            )

        # Combine system policies with input policies (from policy groups and direct input)
        # Also track feature-driven changes for recommendations
        combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact = self._combine_system_and_input_policies(
            preflow_req, preflow_resp, perflow_req, perflow_resp, cors_cfg, input_vars, system_name, system_cfg
        )

        # Combine all policies from proxy flows and target flows, removing duplicates
        all_proxy_policies = (
            combined_preflow_req + combined_preflow_resp +
            combined_perflow_req + combined_perflow_resp
        )

        # Create unique policy list by combining proxy and target flow policies
        all_policies_to_render = self._create_unique_policy_list(all_proxy_policies, target_flow_policies)

        # Store feature impact for response
        self._last_feature_impact = feature_impact

        # Optional env overrides (kept outside systems)
        env_overrides = _load_env_overrides(system_name) if system_name else {}

        # CORS configuration (use combined CORS config)
        cors_enabled = bool(combined_cors_cfg.get("enabled"))
        cors_name = None

        outputs: List[PolicyRender] = []
        seen_names: set[str] = set()

        def _render_entries(entries: List[Any]) -> List[str]:
            names: List[str] = []
            for e in entries:
                """
                Process policy entries using the new optimized format.
                Supports both string references and dict configurations.
                """
                if isinstance(e, str):
                    # Direct policy name reference (optimized format)
                    policy_name = e.strip()
                    attach_params = {}
                    pol_name = policy_name
                    policy_label = policy_name
                elif isinstance(e, dict):
                    # Dict format with optional parameters (new optimized format)
                    # Support both 'policy' and 'name' fields for flexibility
                    # 'policy' is the preferred field in the new optimized structure
                    policy_name = str(e.get("policy") or e.get("name", "")).strip()
                    attach_params = dict(e.get("params") or {})

                    if not policy_name:
                        raise ValidationError(
                            "Policy entry missing 'policy' or 'name' field",
                            details={
                                "entry": e,
                                "expected_format": {"policy": "PolicyName", "params": {}},
                                "note": "Use 'policy' field for new optimized structure"
                            }
                        )

                    # Support custom display name or use policy name
                    pol_name = str(e.get("display_name", policy_name)).strip()
                    policy_label = policy_name
                else:
                    # Fallback to string conversion
                    policy_name = str(e).strip()
                    attach_params = {}
                    pol_name = policy_name
                    policy_label = policy_name

                # Resolve template from policy definition
                policy_def = sys_defaults.get(policy_name, {})
                template = policy_def.get("template")
                if not template:
                    raise ValidationError(
                        f"No template found for policy '{policy_name}' in policy definitions",
                        details={"policy": policy_name, "available": list(sys_defaults.keys())}
                    )

                # Resolve params with DEFAULT < ENV < INPUT
                final_params = self._resolve_params(
                    policy_label=policy_label,
                    sys_defaults=sys_defaults,
                    env_overrides=env_overrides,
                    attach_params=attach_params,
                    input_vars=input_vars,
                )

                xml = self._render_template(template, name=pol_name, params=final_params, input=input_vars)

                # Always add the policy - allow multiple instances with different display names
                outputs.append(PolicyRender(name=pol_name, xml=xml))
                seen_names.add(pol_name)

                # Log if we have multiple instances of the same policy type for debugging
                previous_entries = entries[:entries.index(e)]
                previous_policy_names = []
                for entry in previous_entries:
                    if isinstance(entry, str):
                        previous_policy_names.append(entry)
                    elif isinstance(entry, dict):
                        previous_policy_names.append(entry.get("policy") or entry.get("name", ""))

                if policy_name in previous_policy_names:
                    log.info("multiple_policy_instances", policy_type=policy_name, display_name=pol_name)

                names.append(pol_name)
            return names

        # Render all unique policies (proxy + target flows combined, duplicates removed)
        # This ensures each policy is rendered only once, even if used in multiple flows
        all_rendered_names = _render_entries(all_policies_to_render)

        # Extract names for each flow type from the combined policies (handle both string and dict formats)
        def _extract_policy_names(policy_list):
            names = []
            for p in policy_list:
                if isinstance(p, str):
                    names.append(p.strip())
                elif isinstance(p, dict):
                    policy_name = p.get("policy", "")
                    if policy_name:
                        names.append(policy_name)
            return names

        pre_req_names = _extract_policy_names(combined_preflow_req)
        pre_resp_names = _extract_policy_names(combined_preflow_resp)
        per_req_names = _extract_policy_names(combined_perflow_req)
        per_resp_names = _extract_policy_names(combined_perflow_resp)

        # Extract enhanced flow policies from system configuration
        enhanced_flows = {}
        if "flows" in system_cfg and "proxy" in system_cfg["flows"]:
            proxy_flows = system_cfg["flows"]["proxy"]
            enhanced_flows = {
                "postflow_request": _normalize_entries(proxy_flows.get("postflow", {}).get("request", [])),
                "postflow_response": _normalize_entries(proxy_flows.get("postflow", {}).get("response", [])),
                "postclientflow_request": _normalize_entries(proxy_flows.get("postclientflow", {}).get("request", [])),
                "postclientflow_response": _normalize_entries(proxy_flows.get("postclientflow", {}).get("response", [])),
                "nomatchfound_request": _normalize_entries(proxy_flows.get("nomatchfound", {}).get("request", [])),
                "nomatchfound_response": _normalize_entries(proxy_flows.get("nomatchfound", {}).get("response", [])),
            }

        # Render enhanced flow policies
        enhanced_flow_names = {}
        for flow_type, entries in enhanced_flows.items():
            if entries:
                enhanced_flow_names[flow_type] = _render_entries(entries)
            else:
                enhanced_flow_names[flow_type] = []

        # Add conditional policies if feature is enabled
        if feature_evaluator.is_enabled("conditional_policies", context):
            conditional_attacher = get_conditional_attacher()

            # Get conditional policies for preflow
            conditional_preflow = conditional_attacher.get_applicable_policies(context, "preflow")
            if conditional_preflow:
                log.info("conditional_policies_attached",
                        flow="preflow",
                        count=len(conditional_preflow))
                conditional_names = _render_entries(conditional_preflow)
                pre_req_names.extend(conditional_names)

        # Optional: CORS as a dedicated policy entry with same merging
        if cors_enabled:
            # Handle both old and new CORS configuration formats (use combined config)
            if "policy" in combined_cors_cfg:
                # New simplified format
                policy_name = str(combined_cors_cfg.get("policy", "CORS")).strip()
                cors_name = str(combined_cors_cfg.get("display_name", "AM-AddCors")).strip()
                # Resolve CORS policy from policies section
                cors_policy_def = sys_defaults.get(policy_name, {})
                if cors_policy_def:
                    cors_template = cors_policy_def.get("template", "AssignMessage.xml.j2")
                else:
                    cors_template = "AssignMessage.xml.j2"
                    log.warning(
                        "cors_policy_not_found",
                        policy_name=policy_name,
                        using_default_template=cors_template
                    )
                policy_label = policy_name
            else:
                # Legacy format
                cors_template = combined_cors_cfg.get("template", "AssignMessage.CORS.xml.j2")
                cors_name = str(combined_cors_cfg.get("name") or "CORS").strip()
                policy_label = "CORS"

            cors_params_attach = dict(combined_cors_cfg.get("params") or {})

            final_params = self._resolve_params(
                policy_label=policy_label,
                sys_defaults=sys_defaults,
                env_overrides=env_overrides,
                attach_params=cors_params_attach,
                input_vars=input_vars,
            )
            xml = self._render_template(cors_template, name=cors_name, params=final_params, input=input_vars)
            if cors_name not in seen_names:
                outputs.append(PolicyRender(name=cors_name, xml=xml))
                seen_names.add(cors_name)

        plan = AttachPlan(
            preflow_request=pre_req_names,
            preflow_response=pre_resp_names,
            perflow_request=per_req_names,
            perflow_response=per_resp_names,
            cors_policy=cors_name,
            # Enhanced flows
            postflow_request=enhanced_flow_names.get("postflow_request", []),
            postflow_response=enhanced_flow_names.get("postflow_response", []),
            postclientflow_request=enhanced_flow_names.get("postclientflow_request", []),
            postclientflow_response=enhanced_flow_names.get("postclientflow_response", []),
            nomatchfound_request=enhanced_flow_names.get("nomatchfound_request", []),
            nomatchfound_response=enhanced_flow_names.get("nomatchfound_response", []),
        )

        log.info(
            "policies_rendered",
            pre_req=len(pre_req_names),
            pre_resp=len(pre_resp_names),
            per_req=len(per_req_names),
            per_resp=len(per_resp_names),
            cors=bool(cors_name),
        )
        return outputs, plan

    def validate_policy_params(self, policy_name: str, params: Dict[str, Any], template_name: Optional[str] = None) -> Tuple[bool, List[str]]:
        """
        Validate parameters for a policy using the policy catalog.
        Now uses template-based validation instead of policy-specific validation.
        Returns (is_valid, list_of_errors)
        """
        if not self._policy_catalog:
            log.warning("policy_catalog_not_available", policy=policy_name)
            return True, []  # Skip validation if catalog not available

        # Use template name for validation if provided, otherwise try to infer from policy
        if template_name:
            return self._policy_catalog.validate_template_params(template_name, params)
        else:
            # Try to validate using policy name (backward compatibility)
            return self._policy_catalog.validate_policy_params(policy_name, params)

    def validate_input_params(self, input_vars: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate all policy parameters in input_vars.
        Returns (is_valid, list_of_errors)
        """
        if not self._policy_catalog:
            return True, []  # Skip validation if catalog not available

        policy_params = input_vars.get("policy_params", {})
        all_errors = []

        for policy_name, params in policy_params.items():
            is_valid, errors = self.validate_policy_params(policy_name, params)
            if not is_valid:
                all_errors.extend(errors)

        return len(all_errors) == 0, all_errors

    def _combine_system_and_input_policies(
        self,
        preflow_req: List[Dict[str, Any]],
        preflow_resp: List[Dict[str, Any]],
        perflow_req: List[Dict[str, Any]],
        perflow_resp: List[Dict[str, Any]],
        cors_cfg: Dict[str, Any],
        input_vars: Dict[str, Any],
        system_name: Optional[str] = None,
        system_cfg: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]], List[Dict[str, Any]], Dict[str, Any], Dict[str, Any]]:
        """
        Combine system-defined policies with input policies (from policy groups and direct input).
        Creates a unified list of policies to render, avoiding duplicates.

        Priority order:
        1. System-defined policies (from flows configuration)
        2. Input policies from policy_params (including policy groups)
        3. System-driven feature flag evaluation for policy inclusion/exclusion

        Returns: (combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact)
        """
        # Get input policy parameters (includes policies from policy groups)
        policy_params = input_vars.get("policy_params", {})

        # Create sets to track which policies are already configured in system flows
        system_policies = set()
        for entries in [preflow_req, preflow_resp, perflow_req, perflow_resp]:
            for entry in entries:
                if isinstance(entry, dict) and "policy" in entry:
                    system_policies.add(entry["policy"])
                elif isinstance(entry, str):
                    system_policies.add(entry)

        # Add CORS policy if configured in system
        if cors_cfg.get("enabled") and cors_cfg.get("policy"):
            system_policies.add(cors_cfg["policy"])

        # Find input policies that are not already in system configuration
        additional_policies = []
        for policy_name, params in policy_params.items():
            if policy_name not in system_policies:
                # Create policy entry for additional policies
                policy_entry = {
                    "policy": policy_name,
                    "params": params if params else {}
                }
                additional_policies.append(policy_entry)

        # Apply system-driven feature flag filtering and track changes
        env_name = getattr(settings, 'apigee_env', 'test')
        filtered_additional_policies, feature_impact = self._apply_system_driven_feature_filtering(
            additional_policies, input_vars, env_name, system_name, system_cfg
        )

        # Combine policies - add additional policies to appropriate flows
        # For now, add additional policies to preflow request (can be made configurable)
        combined_preflow_req = list(preflow_req) + filtered_additional_policies
        combined_preflow_resp = list(preflow_resp)
        combined_perflow_req = list(perflow_req)
        combined_perflow_resp = list(perflow_resp)
        combined_cors_cfg = dict(cors_cfg)

        log.info(
            "policies_combined",
            system_policies=len(system_policies),
            additional_policies=len(filtered_additional_policies),
            total_preflow_req=len(combined_preflow_req),
            total_perflow_req=len(combined_perflow_req),
            feature_changes=len(feature_impact.get("recommendations", []))
        )

        return combined_preflow_req, combined_preflow_resp, combined_perflow_req, combined_perflow_resp, combined_cors_cfg, feature_impact

    def _apply_system_driven_feature_filtering(
        self,
        policies: List[Dict[str, Any]],
        input_vars: Dict[str, Any],
        env_name: str,
        system_name: Optional[str],
        system_cfg: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Apply system-driven feature flag filtering and track changes for recommendations.

        Args:
            policies: List of policy entries to filter
            input_vars: Input variables containing context
            env_name: Current environment name
            system_name: System name for feature flag evaluation
            system_cfg: System configuration to get template information

        Returns:
            Tuple of (filtered_policies, feature_impact_summary)
        """
        from .feature_flags import get_feature_evaluator

        # Initialize feature impact tracking
        feature_impact = {
            "policies_added": [],
            "policies_removed": [],
            "policies_modified": [],
            "recommendations": [],
            "active_feature_flags": {}
        }

        # Get feature flag evaluator for system-driven evaluation
        evaluator = get_feature_evaluator(env_name, system_name)

        filtered_policies = []

        for policy_entry in policies:
            policy_name = policy_entry.get("policy", "")

            # Try to get template information from system configuration
            policy_template = None
            if system_cfg and "policies" in system_cfg:
                policy_config = system_cfg["policies"].get(policy_name, {})
                policy_template = policy_config.get("template")

            # Check if policy should be included based on system-driven feature flags
            should_include, reason, feature_flag = self._should_include_policy_system_driven(
                policy_name, input_vars, evaluator, policy_template
            )

            if should_include:
                filtered_policies.append(policy_entry)
                feature_impact["policies_added"].append(policy_name)

                if feature_flag:
                    feature_impact["active_feature_flags"][feature_flag] = True
                    feature_impact["recommendations"].append({
                        "policy_name": policy_name,
                        "action": "added",
                        "reason": reason,
                        "feature_flag": feature_flag,
                        "environment_impact": f"Policy added in {env_name} environment"
                    })
            else:
                feature_impact["policies_removed"].append(policy_name)

                if feature_flag:
                    feature_impact["active_feature_flags"][feature_flag] = False
                    feature_impact["recommendations"].append({
                        "policy_name": policy_name,
                        "action": "removed",
                        "reason": reason,
                        "feature_flag": feature_flag,
                        "environment_impact": f"Policy filtered out in {env_name} environment"
                    })

                log.debug("policy_filtered_by_system_feature_flag", policy=policy_name, reason=reason)

        return filtered_policies, feature_impact

    def _should_include_policy_system_driven(
        self,
        policy_name: str,
        input_vars: Dict[str, Any],
        evaluator,
        policy_template: Optional[str] = None
    ) -> Tuple[bool, str, Optional[str]]:
        """
        Determine if a policy should be included based on system-driven feature flags.
        Now uses template-based feature flag mappings instead of policy-specific names.

        Args:
            policy_name: Name of the policy instance (e.g., "JTP-JSONThreatProtection")
            input_vars: Full input context
            evaluator: Feature flag evaluator
            policy_template: Template name (e.g., "JSONThreatProtection.xml.j2")

        Returns:
            Tuple of (should_include, reason, feature_flag_name)
        """
        # Template-based feature flag mappings (generic and reusable)
        template_feature_mappings = {
            "JSONThreatProtection.xml.j2": "json_threat_protection",
            "XMLThreatProtection.xml.j2": "xml_threat_protection",
            "ResponseCache.xml.j2": "smart_caching",
            "BasicAuthentication.xml.j2": "advanced_security",
            "MessageLogging.xml.j2": "debug_logging",
            "DebugPolicy.xml.j2": "debug_logging",
        }

        # Fallback: try to infer template from policy name patterns
        policy_template_fallbacks = {
            # Pattern matching for common policy naming conventions
            "JTP-": "JSONThreatProtection.xml.j2",
            "XTP-": "XMLThreatProtection.xml.j2",
            "RC-": "ResponseCache.xml.j2",
            "BA-": "BasicAuthentication.xml.j2",
            "ML-": "MessageLogging.xml.j2",
            "DP-": "DebugPolicy.xml.j2",
        }

        # Determine the template to use for feature flag mapping
        template_to_check = policy_template
        if not template_to_check:
            # Try to infer template from policy name patterns
            for prefix, template in policy_template_fallbacks.items():
                if policy_name.startswith(prefix):
                    template_to_check = template
                    break

        # Check if template has a specific feature flag requirement
        feature_flag = None
        if template_to_check:
            feature_flag = template_feature_mappings.get(template_to_check)

        if feature_flag:
            # Create context for feature flag evaluation
            context = {
                "content_types": input_vars.get("content_types", []),
                "methods": input_vars.get("methods", []),
                "policy_name": policy_name,
                "template": template_to_check,
                "api_characteristics": {
                    "method_count": len(input_vars.get("methods", [])),
                    "has_security": any(policy in input_vars.get("policy_params", {})
                                      for policy in ["VerifyApiKey", "OAuthV2", "BasicAuthentication"])
                }
            }

            # Add content_type for single content type evaluation
            if context["content_types"]:
                context["content_type"] = context["content_types"][0]

            is_enabled = evaluator.is_enabled(feature_flag, context)
            reason = f"Feature flag '{feature_flag}' is {'enabled' if is_enabled else 'disabled'} for template '{template_to_check}'"

            if not is_enabled:
                return False, reason, feature_flag

        # Content-type based filtering using template information
        content_types = input_vars.get("content_types", [])
        if template_to_check == "JSONThreatProtection.xml.j2" or policy_name.startswith("JTP-"):
            json_content_types = ["application/json", "application/vnd.api+json"]
            has_json = any(ct in json_content_types for ct in content_types)
            if not has_json and content_types:  # Only filter if content types are specified
                return False, "No JSON content types detected", None
        elif template_to_check == "XMLThreatProtection.xml.j2" or policy_name.startswith("XTP-"):
            xml_content_types = ["application/xml", "text/xml"]
            has_xml = any(ct in xml_content_types for ct in content_types)
            if not has_xml and content_types:  # Only filter if content types are specified
                return False, "No XML content types detected", None

        # Environment-based filtering for debug policies using template information
        env_name = getattr(settings, 'apigee_env', 'test').lower()
        debug_templates = ["MessageLogging.xml.j2", "DebugPolicy.xml.j2"]
        if (template_to_check in debug_templates or
            policy_name.startswith(("ML-", "DP-"))) and env_name in ['prod', 'production']:
            return False, "Debug policies disabled in production", "debug_logging"

        # Default: include the policy
        reason = "Policy included by default"
        return True, reason, feature_flag

    def _extract_content_types(self, input_vars: Dict[str, Any]) -> List[str]:
        """Extract content types from input variables (e.g., from OAS)"""
        content_types = []

        # Check if OAS context provides content types
        oas = input_vars.get("oas", {})
        if "request_content_types" in oas:
            content_types.extend(oas["request_content_types"])

        # Default content types based on common patterns
        methods = input_vars.get("methods", [])
        if any(method in ["POST", "PUT", "PATCH"] for method in methods):
            content_types.extend(["application/json", "application/xml"])

        return list(set(content_types))  # Remove duplicates

    def _extract_target_flow_policies(self, system_cfg: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract all policies referenced in target endpoint flows.
        Target flows are defined in targets.*.flows.* structure.

        Args:
            system_cfg: System configuration containing targets section

        Returns:
            List of normalized policy entries from target flows
        """
        target_policies = []
        targets_cfg = system_cfg.get("targets", {})

        for _, target_config in targets_cfg.items():
            target_flows = target_config.get("flows", {})

            # Extract policies from preflow
            preflow = target_flows.get("preflow", {})
            target_policies.extend(_normalize_entries(preflow.get("request", [])))
            target_policies.extend(_normalize_entries(preflow.get("response", [])))

            # Extract policies from postflow
            postflow = target_flows.get("postflow", {})
            target_policies.extend(_normalize_entries(postflow.get("request", [])))
            target_policies.extend(_normalize_entries(postflow.get("response", [])))

        # Remove duplicates while preserving order
        seen = set()
        unique_policies = []
        for policy in target_policies:
            # Handle both string and dict formats
            if isinstance(policy, str):
                policy_name = policy.strip()
                policy_dict = {"policy": policy_name}
            else:
                policy_name = policy.get("policy", "")
                policy_dict = policy

            if policy_name and policy_name not in seen:
                seen.add(policy_name)
                unique_policies.append(policy_dict)

        log.debug("target_flow_policies_extracted",
                 count=len(unique_policies),
                 policies=[p.get("policy", "") for p in unique_policies])

        return unique_policies

    def _create_unique_policy_list(self, proxy_policies: List[Dict[str, Any]], target_policies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create a unique list of policies by combining proxy and target flow policies.
        Removes duplicates while preserving order, with proxy policies taking precedence.

        Args:
            proxy_policies: Policies from proxy flows
            target_policies: Policies from target flows

        Returns:
            List of unique policy entries with duplicates removed
        """
        seen = set()
        unique_policies = []

        # Add proxy policies first (they take precedence)
        for policy in proxy_policies:
            # Handle both string and dict formats
            if isinstance(policy, str):
                policy_name = policy.strip()
                policy_dict = {"policy": policy_name}
            else:
                policy_name = policy.get("policy", "")
                policy_dict = policy

            if policy_name and policy_name not in seen:
                seen.add(policy_name)
                unique_policies.append(policy_dict)

        # Add target policies that aren't already in proxy policies
        for policy in target_policies:
            # Handle both string and dict formats
            if isinstance(policy, str):
                policy_name = policy.strip()
                policy_dict = {"policy": policy_name}
            else:
                policy_name = policy.get("policy", "")
                policy_dict = policy

            if policy_name and policy_name not in seen:
                seen.add(policy_name)
                unique_policies.append(policy_dict)

        log.debug("unique_policies_created",
                 total_proxy=len(proxy_policies),
                 total_target=len(target_policies),
                 unique_count=len(unique_policies),
                 duplicates_removed=len(proxy_policies) + len(target_policies) - len(unique_policies))

        return unique_policies

    # -------------------- internals --------------------

    def _resolve_params(
        self,
        *,
        policy_label: str,
        sys_defaults: Dict[str, Any],
        env_overrides: Dict[str, Any],
        attach_params: Dict[str, Any],
        input_vars: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Compute final params for a given policy using:
            DEFAULT < ENV < INPUT
        with per-param semantics from the param catalog + list ops.
        """
        # 0) Choose catalog key: use policy label if present, otherwise a generic mapping
        catalog = self._param_catalog.get(policy_label) or self._param_catalog.get(_canonical(policy_label)) or {}

        # 1) Collect layers (raw dicts) for the given policy
        def _get_legacy(d: Dict[str, Any], key: str) -> Dict[str, Any]:
            """Get params from legacy nested structure: policy.params"""
            return ((d.get(key) or {}).get("params")) if isinstance(d.get(key), dict) else {}

        def _get_simplified(d: Dict[str, Any], key: str) -> Dict[str, Any]:
            """Get params from simplified structure: direct policy parameters"""
            policy_data = d.get(key) or d.get(_canonical(key))
            if isinstance(policy_data, dict):
                return policy_data
            return {}

        # Try simplified structure first, then fall back to legacy
        layer_default = _get_simplified(sys_defaults, policy_label) or _get_legacy(sys_defaults, policy_label) or {}

        # Environment overrides - check both policy_defaults and legacy policies.overrides
        env_policy_defaults = env_overrides.get("policy_defaults", {})
        env_legacy_overrides = env_overrides.get("policies", {}).get("overrides", {})
        layer_env = _get_simplified(env_policy_defaults, policy_label) or _get_legacy(env_legacy_overrides, policy_label) or {}

        layer_attach = attach_params or {}

        # 2) Merge DEFAULT < ENV < ATTACH for non-list scalars & dicts;
        #    For lists, use param catalog semantics. Also collect YAML directives (__append etc.).
        merged = _merge_three_layers(catalog, layer_default, layer_env, layer_attach)

        # 3) Apply INPUT overlay (UI + OAS). INPUT can provide list ops or simple scalars.
        policy_input = _extract_policy_input(policy_label, input_vars)
        merged = _apply_input_overlay(catalog, merged, policy_input)

        # 4) Render any strings (e.g., "User sent {{ input.quota }}") using Jinja
        rendered = _render_params_dict(self._env, merged, {"input": input_vars, "env": settings.apigee_env})
        return rendered

    def _render_template(self, template_name: str, **context: Any) -> str:
        try:
            tmpl = self._env.get_template(template_name)
        except jinja2.TemplateNotFound as e:
            raise ValidationError(
                "Policy template not found",
                details={"template": template_name, "templates_root": str(self._templates_root)},
            ) from e

        # Add template helpers to context
        enhanced_context = dict(context)
        if self._template_helpers:
            enhanced_context['helpers'] = self._template_helpers
            # Also add helper functions directly for convenience
            enhanced_context.update({
                'is_valid_rate': self._template_helpers.is_valid_rate,
                'is_valid_url': self._template_helpers.is_valid_url,
                'safe_default': self._template_helpers.safe_default,
                'normalize_list': self._template_helpers.normalize_list,
                'is_production': self._template_helpers.is_production,
                'feature_enabled': self._template_helpers.feature_enabled,
                'xml_escape': self._template_helpers.xml_escape,
                'parse_rate_format': self._template_helpers.parse_rate_format,
            })

        try:
            xml: str = tmpl.render(**enhanced_context)
        except jinja2.UndefinedError as e:
            log.warning(
                "template_missing_variable",
                template=template_name,
                context=enhanced_context,
                error=str(e),
            )
            raise ValidationError(
                "Missing variable while rendering policy template",
                details={"template": template_name, "error": str(e)},
            ) from e

        return xml.strip()


# ---------------- helpers: loading & normalizing ----------------

def _canonical(s: str) -> str:
    return (s or "").strip()




def _normalize_entries(value: Any) -> List[Any]:
    """
    Normalize policy entries using the new optimized format.

    Supports:
    - String format: ["VK-VerifyApiKey", "SA-SpikeArrest", ...]
    - Dict format: [{"name": "VK-VerifyApiKey", "params": {...}}, ...]
    - Mixed format: ["VK-VerifyApiKey", {"name": "SA-SpikeArrest", "params": {...}}]

    Args:
        value: Policy list from configuration

    Returns:
        List[Any]: Normalized policy entries

    Raises:
        ValidationError: If policy list format is invalid
    """
    if not value:
        return []
    if not isinstance(value, list):
        raise ValidationError("Policy list must be a list", details={"value": value})

    normalized = []
    for e in value:
        if isinstance(e, str):
            # Direct policy name reference (optimized format)
            normalized.append(e.strip())
        elif isinstance(e, dict):
            # Dict format with optional parameters
            normalized.append(e)
        else:
            raise ValidationError("Policy entry must be a string or object", details={"entry": e})

    return normalized

def _load_env_overrides(system_name: Optional[str]) -> Dict[str, Any]:
    """
    Load env overrides YAML from <envs_dir>/<env>/<system>.yaml if present.
    envs_dir defaults to sibling of systems_dir: config/env
    """
    if not system_name:
        return {}
    sys_dir = Path(settings.systems_dir).resolve()
    envs_dir = getattr(settings, "envs_dir", None)
    env_root = Path(envs_dir) if envs_dir else sys_dir.parent.joinpath("env")
    path = env_root.joinpath(settings.apigee_env, f"{system_name}.yaml")
    if not path.exists():
        log.debug("env_overrides_not_found", path=str(path))
        return {}
    try:
        with open(path, "r", encoding="utf-8") as f:
            y = yaml.safe_load(f) or {}
        log.info("env_overrides_loaded", system=system_name, env=settings.apigee_env, path=str(path))
        return y
    except Exception as e:
        log.warning("env_overrides_failed", path=str(path), error=str(e))
        return {}

# ---------------- helpers: merging ----------------

def _merge_three_layers(
    catalog: Dict[str, Dict[str, Any]],
    default_layer: Dict[str, Any],
    env_layer: Dict[str, Any],
    attach_layer: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Merge DEFAULT < ENV < ATTACH for params dicts using per-key semantics from catalog.
    - Scalars: replace
    - Dicts: shallow merge (later overrides earlier)
    - Lists: follow catalog.merge (replace|append_unique|prepend_unique). Also resolve YAML directives.
    """
    # Start with empty; fold in three layers
    result: Dict[str, Any] = {}

    # We also collect directives like key__append from all layers and apply at the end.
    directives: Dict[str, Dict[str, List[Any]]] = {}

    def fold(src: Dict[str, Any]):
        for key, val in (src or {}).items():
            if "__" in key and key.split("__", 1)[1] in ("append", "prepend", "replace", "remove"):
                k, op = key.split("__", 1)
                directives.setdefault(k, {}).setdefault(op, [])
                directives[k][op].extend(_as_list(val))
                continue

            spec = catalog.get(key, {})
            typ = (spec.get("type") or _infer_type(val))
            merge_mode = spec.get("merge", "replace")

            if typ == "list":
                base = _as_list(result.get(key))
                incoming = _as_list(val)
                if base is None:
                    base = []
                if incoming is None:
                    incoming = []

                if merge_mode == "replace":
                    result[key] = list(incoming)
                elif merge_mode == "append_unique":
                    result[key] = _append_unique(base, incoming)
                elif merge_mode == "prepend_unique":
                    result[key] = _prepend_unique(base, incoming)
                else:  # fallback to replace
                    result[key] = list(incoming)
            elif typ == "dict" and isinstance(val, dict):
                dest = result.get(key, {})
                if not isinstance(dest, dict):
                    dest = {}
                tmp = dict(dest)
                tmp.update(val)
                result[key] = tmp
            else:
                result[key] = val

    # Apply layers
    for layer in (default_layer, env_layer, attach_layer):
        fold(layer)

    # Apply directives after base merges
    for k, ops in directives.items():
        spec = catalog.get(k, {})
        typ = spec.get("type", "list")
        norm = spec.get("normalize")
        current = _as_list(result.get(k)) or []
        if "replace" in ops:
            current = _as_list(ops["replace"])
        if "prepend" in ops:
            current = _prepend_unique(current, _as_list(ops["prepend"]))
        if "append" in ops:
            current = _append_unique(current, _as_list(ops["append"]))
        if "remove" in ops:
            current = [x for x in current if x not in set(_as_list(ops["remove"]))]
        # normalize & dedupe
        if typ == "list":
            current = _normalize_list(current, norm)
        result[k] = current

    # Normalize lists from non-directive merges too
    for k, v in list(result.items()):
        spec = catalog.get(k, {})
        if spec.get("type") == "list":
            result[k] = _normalize_list(_as_list(v) or [], spec.get("normalize"))

    return result


def _apply_input_overlay(
    catalog: Dict[str, Dict[str, Any]],
    merged: Dict[str, Any],
    policy_input: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Apply INPUT layer to an already-merged dict, respecting catalog semantics and list ops.
    INPUT can provide:
      - Scalars (replace)
      - Lists (treated as 'replace' unless catalog says append_unique)
      - Dict list-ops: {append:[], prepend:[], replace:[], remove:[]}
    """
    out = dict(merged)

    for key, val in (policy_input or {}).items():
        spec = catalog.get(key, {})
        typ = spec.get("type", _infer_type(val))
        norm = spec.get("normalize")
        default_list_mode = spec.get("merge", "replace")

        if typ == "list":
            base = _as_list(out.get(key)) or []
            # Dict with ops?
            if isinstance(val, dict):
                current = list(base)
                if "replace" in val:
                    current = _as_list(val.get("replace"))
                if "prepend" in val:
                    current = _prepend_unique(current, _as_list(val.get("prepend")))
                if "append" in val:
                    current = _append_unique(current, _as_list(val.get("append")))
                if "remove" in val:
                    current = [x for x in current if x not in set(_as_list(val.get("remove")))]
                out[key] = _normalize_list(current, norm)
            else:
                # plain list input: obey default strategy
                incoming = _as_list(val) or []
                if default_list_mode == "append_unique":
                    out[key] = _normalize_list(_append_unique(base, incoming), norm)
                elif default_list_mode == "prepend_unique":
                    out[key] = _normalize_list(_prepend_unique(base, incoming), norm)
                else:  # replace
                    out[key] = _normalize_list(incoming, norm)
        elif typ == "dict" and isinstance(val, dict):
            # Ensure we have a proper dictionary to work with
            existing_val = out.get(key) or {}
            if not isinstance(existing_val, dict):
                # Log the issue for debugging
                log.warning("dict_merge_type_mismatch",
                           key=key,
                           existing_type=type(existing_val).__name__,
                           existing_value=existing_val,
                           expected_type="dict")
                # Start with empty dict if existing value is not a dict
                existing_val = {}

            tmp = dict(existing_val)
            tmp.update(val)
            out[key] = tmp
        else:
            out[key] = val

    return out


# ---------------- helpers: rendering params ----------------

def _render_params_dict(env: jinja2.Environment, params: Dict[str, Any], ctx: Dict[str, Any]) -> Dict[str, Any]:
    def _render(val: Any) -> Any:
        if isinstance(val, str):
            t = env.from_string(val)
            return t.render(**ctx)
        elif isinstance(val, dict):
            return {k: _render(v) for k, v in val.items()}
        elif isinstance(val, list):
            return [_render(v) for v in val]
        return val
    return _render(params)


# ---------------- helpers: types & lists ----------------

def _infer_type(val: Any) -> str:
    if isinstance(val, list):
        return "list"
    if isinstance(val, bool):
        return "bool"
    if isinstance(val, int):
        return "int"
    if isinstance(val, dict):
        return "dict"
    return "string"

def _as_list(val: Any) -> Optional[List[Any]]:
    if val is None:
        return None
    if isinstance(val, list):
        return list(val)
    return [val]

def _append_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out = list(base)
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    return out

def _prepend_unique(base: List[Any], incoming: List[Any]) -> List[Any]:
    seen = set(base)
    out: List[Any] = []
    for x in incoming or []:
        if x not in seen:
            out.append(x)
            seen.add(x)
    out.extend(base)
    return out

def _normalize_list(items: List[Any], mode: Optional[str]) -> List[Any]:
    if not mode:
        # ensure uniqueness while preserving order
        return _append_unique([], items)
    if mode == "lower":
        normalized = [str(x).lower() for x in items]
        return _append_unique([], normalized)
    # 'exact' or unknown → unique preserve order
    return _append_unique([], items)


# ---------------- helpers: input extraction ----------------

def _extract_policy_input(policy_label: str, input_vars: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhanced policy input extraction with direct policy name mapping support:
      - Direct policy parameters: input.policy_params.<PolicyName> (direct parameter mapping)
      - Legacy structured overrides: input.policy.<PolicyName> (structured list ops)
      - Legacy convenience mappings:
          Quota.allow          <- input.quota
          Spike-Arrest.rate    <- input.spikeArrest
          CORS.allow_methods   <- input.methods (+ maybe oas.methods if present)
          CORS.allow_headers   <- input.headers (+ oas.request_headers)
          CORS.expose_headers  <- oas.response_headers

    Precedence: Direct Policy Params > Legacy Structured > Legacy Convenience
    """
    out: Dict[str, Any] = {}

    # 1. Enhanced direct policy parameter mapping (highest precedence)
    policy_params = input_vars.get("policy_params", {}) if isinstance(input_vars, dict) else {}
    if isinstance(policy_params, dict):
        # Look for exact policy name match first
        if policy_label in policy_params and isinstance(policy_params[policy_label], dict):
            out.update(policy_params[policy_label])

        # Also check for canonical policy name mapping (e.g., "Spike-Arrest" -> "SpikeArrest")
        canonical_label = _canonical(policy_label)
        if canonical_label != policy_label and canonical_label in policy_params:
            if isinstance(policy_params[canonical_label], dict):
                # Merge with lower precedence than exact match
                for k, v in policy_params[canonical_label].items():
                    if k not in out:  # Don't override exact matches
                        out[k] = v

    # 2. Legacy structured per-policy map from input: { policy: { key: {append/prepend/replace/remove} } }
    policy_map = (input_vars.get("policy") or {}) if isinstance(input_vars, dict) else {}
    if isinstance(policy_map.get(policy_label), dict):
        # Merge with lower precedence than direct params
        for k, v in policy_map[policy_label].items():
            if k not in out:  # Don't override direct params
                out[k] = v

    # 3. OAS-derived and method/header convenience overlays (lowest precedence)
    # Updated to work with actual policy names instead of policy types

    # CORS policies (detect by policy name patterns or template)
    if (policy_label.startswith("AM-") and "cors" in policy_label.lower()) or policy_label == "CORS":
        # caller may set methods/headers; treat as append_unique by default
        if "methods" in input_vars and "allow_methods" not in out:
            out.setdefault("allow_methods", {})  # dict ops
            out["allow_methods"].setdefault("append", [])
            out["allow_methods"]["append"] += _as_list(input_vars["methods"]) or []

        if "headers" in input_vars and "allow_headers" not in out:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(input_vars["headers"]) or []

        # OAS-derived headers (only if not already set)
        oas = input_vars.get("oas") or {}
        if "request_headers" in oas and "allow_headers" not in out:
            out.setdefault("allow_headers", {})
            out["allow_headers"].setdefault("append", [])
            out["allow_headers"]["append"] += _as_list(oas["request_headers"]) or []
        if "response_headers" in oas and "expose_headers" not in out:
            out.setdefault("expose_headers", {})
            out["expose_headers"].setdefault("append", [])
            out["expose_headers"]["append"] += _as_list(oas["response_headers"]) or []

    # Quota policies (detect by policy name patterns)
    elif policy_label.startswith("Q-") or policy_label == "Quota":
        if "quota" in input_vars and "allow" not in out:
            out["allow"] = input_vars["quota"]

    # SpikeArrest policies (detect by policy name patterns)
    elif policy_label.startswith("SA-") or policy_label == "Spike-Arrest":
        if "spikeArrest" in input_vars and "rate" not in out:
            out["rate"] = input_vars["spikeArrest"]

    return out