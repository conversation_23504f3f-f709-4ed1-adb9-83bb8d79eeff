#!/usr/bin/env python3
"""
Test script for Phase 1 enhancements:
- Enhanced parameter input system
- Simplified configuration structure
- Direct policy name mapping
"""

import json
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set PYTHONPATH environment variable for proper module resolution
os.environ['PYTHONPATH'] = str(project_root)

from app.services.policy_builder import PolicyBuilder
from app.config.loader import Config<PERSON>oader


def test_enhanced_parameter_input():
    """Test the enhanced parameter input system with direct policy name mapping"""
    print("Testing Enhanced Parameter Input System...")
    
    try:
        # Initialize PolicyBuilder
        builder = PolicyBuilder()
        print("✓ PolicyBuilder initialized successfully")
        
        # Load system configuration
        loader = ConfigLoader()
        cfgs = loader.load(system_name="reflect")
        print("✓ System configuration loaded")
        
        # Test enhanced input variables - fully configuration-driven (no legacy params)
        input_vars = {
            # OAS-derived values
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],

            # Enhanced parameters - direct policy name mapping
            "policy_params": {
                "VerifyApiKey": {
                    "keyref": "request.header.x-custom-key"
                },
                "CORS": {
                    "allow_origins": ["https://app.example.com", "https://admin.example.com"],
                    "allow_methods": ["GET", "POST", "PUT"],
                    "allow_headers": ["Authorization", "Content-Type", "x-request-id"]
                },
                "Quota": {
                    "allow": "2000pm",
                    "interval": "1",
                    "timeunit": "minute",
                    "identifier_ref": "request.header.client_id"
                },
                "SpikeArrest": {
                    "rate": "75pm"
                }
            },
            "flow_config": {},
            "env_overrides": {},
            "features": {"advanced_security": True}
        }
        
        # Render policies with enhanced parameters
        policies, attach_plan = builder.render_policies(
            cfgs.system_cfg, 
            input_vars,
            system_name="reflect"
        )
        
        print(f"✓ Policies rendered successfully: {len(policies)} policies")
        print(f"✓ Attach plan created: preflow_req={len(attach_plan.preflow_request)}, "
              f"perflow_req={len(attach_plan.perflow_request)}, cors={bool(attach_plan.cors_policy)}")
        
        # Verify policy parameters were applied correctly
        for policy in policies:
            print(f"  - Policy: {policy.name}")
            
            # Check if enhanced parameters were applied
            if "x-custom-key" in policy.xml:
                print("    ✓ Enhanced VerifyApiKey keyref parameter applied")
            if "https://app.example.com" in policy.xml:
                print("    ✓ Enhanced CORS allow_origins parameter applied")
            if "2000pm" in policy.xml:
                print("    ✓ Enhanced Quota allow parameter applied")
            if "75pm" in policy.xml:
                print("    ✓ Enhanced SpikeArrest rate parameter applied")
        
        print("\n✅ Enhanced Parameter Input System test PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Enhanced Parameter Input System test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_template_mapping():
    """Test the policy template mapping system"""
    print("\nTesting Policy Template Mapping...")
    
    try:
        builder = PolicyBuilder()
        
        # Check if template mapping was loaded
        if hasattr(builder, '_template_mapping'):
            mapping = builder._template_mapping
            print(f"✓ Template mapping loaded with {len(mapping)} policies")
            
            # Check some key mappings
            expected_mappings = {
                "VerifyApiKey": "VerifyApiKey.xml.j2",
                "SpikeArrest": "SpikeArrest.xml.j2",
                "Quota": "Quota.xml.j2",
                "CORS": "AssignMessage.CORS.xml.j2"
            }
            
            for policy, expected_template in expected_mappings.items():
                if policy in mapping and mapping[policy] == expected_template:
                    print(f"  ✓ {policy} -> {expected_template}")
                else:
                    print(f"  ❌ {policy} mapping incorrect or missing")
                    return False
            
            print("✅ Policy Template Mapping test PASSED!")
            return True
        else:
            print("❌ Template mapping not found in PolicyBuilder")
            return False
            
    except Exception as e:
        print(f"❌ Policy Template Mapping test FAILED: {str(e)}")
        return False


def test_api_input_structure():
    """Test the API input structure parsing"""
    print("\nTesting API Input Structure...")

    try:
        # Simulate API input JSON - simplified structure after removing flowConfig and envOverrides
        api_input = {
            "policyParams": json.dumps({
                "VerifyApiKey": {"keyref": "request.header.x-api-key"},
                "CORS": {"allow_origins": ["https://test.com"]},
                "Quota": {"allow": "500pm"},
                "SpikeArrest": {"rate": "100pm"}
            }),
            "gitCommitMessage": "Test commit message"
        }

        # Parse like the API endpoint would
        parsed_policy_params = json.loads(api_input["policyParams"])

        print("✓ API input JSON parsing successful")
        print(f"  - Policy params: {len(parsed_policy_params)} policies")
        print(f"  - Git commit message: {api_input['gitCommitMessage']}")

        # Verify quota and spikeArrest are now in policy params, not top-level
        if "Quota" in parsed_policy_params and "allow" in parsed_policy_params["Quota"]:
            print("  ✓ Quota parameters moved to policyParams structure")
        if "SpikeArrest" in parsed_policy_params and "rate" in parsed_policy_params["SpikeArrest"]:
            print("  ✓ SpikeArrest parameters moved to policyParams structure")
        if "regexRequired" in parsed_features:
            print("  ✓ regexRequired moved to features structure")

        print("✅ API Input Structure test PASSED!")
        return True

    except Exception as e:
        print(f"❌ API Input Structure test FAILED: {str(e)}")
        return False


def test_legacy_parameters_removed():
    """Test that legacy parameters are no longer accepted in the system"""
    print("\nTesting Legacy Parameters Removal...")

    try:
        from app.services.orchestrator import BootstrapRequest

        # Try to create a BootstrapRequest - should not have legacy fields
        request_fields = BootstrapRequest.__dataclass_fields__.keys()

        legacy_fields = ["limits", "regex_required"]
        found_legacy = [field for field in legacy_fields if field in request_fields]

        if found_legacy:
            print(f"❌ Found legacy fields in BootstrapRequest: {found_legacy}")
            return False

        print("✓ Legacy fields removed from BootstrapRequest")

        # Check that GitOptions only has commit_message
        from app.services.orchestrator import GitOptions
        git_fields = GitOptions.__dataclass_fields__.keys()

        if "repo_url" in git_fields or "branch" in git_fields:
            print("❌ Found legacy git fields (repo_url, branch) in GitOptions")
            return False

        if "commit_message" not in git_fields:
            print("❌ commit_message field missing from GitOptions")
            return False

        print("✓ GitOptions properly updated (only commit_message)")

        print("✅ Legacy Parameters Removal test PASSED!")
        return True

    except Exception as e:
        print(f"❌ Legacy Parameters Removal test FAILED: {str(e)}")
        return False


def main():
    """Run all Phase 1 tests"""
    print("=" * 60)
    print("PHASE 1 ENHANCEMENTS TEST SUITE")
    print("=" * 60)
    
    tests = [
        test_template_mapping,
        test_api_input_structure,
        test_legacy_parameters_removed,
        test_enhanced_parameter_input,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PHASE 1 TESTS PASSED!")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
