<AccessControl name="{{ name }}"{% if params.enabled is defined %} enabled="{{ 'true' if params.enabled else 'false' }}"{% endif %}>
  {% if params.source %}<Source>{{ params.source }}</Source>{% endif %}  <!-- e.g., client.ip or request.header.X-Forwarded-For -->
  {% if params.allow %}
  <Allow>
    {% for item in params.allow %}
    <Value>{{ item }}</Value>
    {% endfor %}
  </Allow>
  {% endif %}
  {% if params.deny %}
  <Deny>
    {% for item in params.deny %}
    <Value>{{ item }}</Value>
    {% endfor %}
  </Deny>
  {% endif %}
</AccessControl>