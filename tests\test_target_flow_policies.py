#!/usr/bin/env python3
"""
Test target flow policies rendering.
This test verifies that policies referenced in target endpoint flows
are properly extracted and rendered by the policy builder.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import yaml
from app.services.policy_builder import PolicyBuilder
from app.services.target_builder import TargetBuilder


def test_target_flow_policies():
    """Test that target flow policies are extracted and rendered correctly."""
    print("============================================================")
    print("TESTING TARGET FLOW POLICIES")
    print("============================================================")
    
    try:
        # Load the actual system configuration
        system_config_path = "app/config/systems/REFLECT_FDID.yaml"
        with open(system_config_path, 'r') as f:
            system_cfg = yaml.safe_load(f)
        
        print(f"✅ Loaded system configuration: {len(system_cfg.get('policies', {}))} policies")
        
        # Check target flow policies in configuration
        targets_cfg = system_cfg.get("targets", {})
        target_flow_policies = []
        
        for target_name, target_config in targets_cfg.items():
            target_flows = target_config.get("flows", {})
            print(f"   Target '{target_name}' flows:")
            
            preflow = target_flows.get("preflow", {})
            preflow_req = preflow.get("request", [])
            preflow_resp = preflow.get("response", [])
            
            postflow = target_flows.get("postflow", {})
            postflow_req = postflow.get("request", [])
            postflow_resp = postflow.get("response", [])
            
            print(f"      PreFlow Request: {preflow_req}")
            print(f"      PreFlow Response: {preflow_resp}")
            print(f"      PostFlow Request: {postflow_req}")
            print(f"      PostFlow Response: {postflow_resp}")
            
            target_flow_policies.extend(preflow_req + preflow_resp + postflow_req + postflow_resp)
        
        # Remove duplicates
        unique_target_policies = list(set(target_flow_policies))
        print(f"✅ Found {len(unique_target_policies)} unique target flow policies: {unique_target_policies}")
        
        # Test policy builder with target flow policies
        print(f"\n🏗️ Testing Policy Builder with Target Flow Policies...")
        policy_builder = PolicyBuilder()
        
        test_input_vars = {
            "methods": ["GET", "POST"],
            "headers": ["Authorization", "Content-Type"],
            "content_types": ["application/json"],
            "policy_params": {}
        }
        
        policies, attach_plan = policy_builder.render_policies(
            system_cfg=system_cfg,
            input_vars=test_input_vars,
            system_name="REFLECT_FDID"
        )
        
        print(f"   ✅ Policy Builder: Rendered {len(policies)} policies")
        
        # Check if target flow policies are included in rendered policies
        rendered_policy_names = [p.name for p in policies]
        
        target_policies_found = []
        target_policies_missing = []
        
        for target_policy in unique_target_policies:
            if target_policy in rendered_policy_names:
                target_policies_found.append(target_policy)
                print(f"      ✅ {target_policy}: Found in rendered policies")
            else:
                target_policies_missing.append(target_policy)
                print(f"      ❌ {target_policy}: Missing from rendered policies")
        
        if target_policies_missing:
            print(f"   ❌ Missing target flow policies: {target_policies_missing}")
            return False
        
        print(f"   ✅ All {len(target_policies_found)} target flow policies rendered successfully")
        
        # Test target builder with rendered policies
        print(f"\n🎯 Testing Target Builder with Rendered Policies...")
        target_builder = TargetBuilder()
        
        try:
            # Test default target
            default_xml = target_builder.build_target_endpoint_xml(
                system_cfg=system_cfg,
                endpoint_name="default"
            )
            
            print(f"   ✅ Default target XML built successfully ({len(default_xml)} chars)")
            
            # Check if target flow policies are referenced in the XML
            for target_policy in unique_target_policies:
                if target_policy in default_xml:
                    print(f"      ✅ {target_policy}: Referenced in target XML")
                else:
                    print(f"      ⚠️  {target_policy}: Not found in target XML (may be in different target)")
            
            # Test default-jo target
            jo_xml = target_builder.build_target_endpoint_xml(
                system_cfg=system_cfg,
                endpoint_name="default-jo"
            )
            
            print(f"   ✅ Default-jo target XML built successfully ({len(jo_xml)} chars)")
            
        except Exception as e:
            if "Invalid policy reference" in str(e):
                print(f"   ❌ Target builder failed with policy reference error: {e}")
                return False
            else:
                print(f"   ❌ Target builder failed with unexpected error: {e}")
                return False
        
        # Test specific target flow policies
        print(f"\n🔍 Testing Specific Target Flow Policies...")
        
        expected_target_policies = [
            "AM-SetTargetStartTime",
            "AM-SetTargetEndTime", 
            "FC-CommonTargetErrorHandler",
            "FC-ExtractCustomLogs"
        ]
        
        for expected_policy in expected_target_policies:
            policy_obj = next((p for p in policies if p.name == expected_policy), None)
            if policy_obj:
                if policy_obj.xml and len(policy_obj.xml) > 50:
                    print(f"      ✅ {expected_policy}: Template rendered successfully ({len(policy_obj.xml)} chars)")
                else:
                    print(f"      ❌ {expected_policy}: Template rendering failed")
                    return False
            else:
                print(f"      ⚠️  {expected_policy}: Not found in rendered policies")
        
        print("\n============================================================")
        print("✅ TARGET FLOW POLICIES TEST PASSED!")
        print("Target flow policies are properly extracted and rendered.")
        print("============================================================")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_target_flow_policies()
    sys.exit(0 if success else 1)
