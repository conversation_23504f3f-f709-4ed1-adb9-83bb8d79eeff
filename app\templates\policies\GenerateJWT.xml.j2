<GenerateJWT name="{{ name }}">
  {% if params.algorithm %}<Algorithm>{{ params.algorithm }}</Algorithm>{% endif %}   <!-- HS256 | RS256 | ES256 ... -->
  {% if params.secret_key %}
  <SecretKey>
    {% if params.secret_key.ref %}<Value ref="{{ params.secret_key.ref }}"/>{% elif params.secret_key.inline %}<Value>{{ params.secret_key.inline }}</Value>{% endif %}
  </SecretKey>
  {% endif %}
  {% if params.private_key %}
  <PrivateKey>
    {% if params.private_key.ref %}<Value ref="{{ params.private_key.ref }}"/>{% elif params.private_key.inline %}<Value>{{ params.private_key.inline }}</Value>{% endif %}
  </PrivateKey>
  {% endif %}
  {% if params.issuer %}<Issuer>{{ params.issuer }}</Issuer>{% endif %}
  {% if params.subject %}<Subject>{{ params.subject }}</Subject>{% endif %}
  {% if params.audience %}<Audience>{{ params.audience }}</Audience>{% endif %}
  {% if params.claims %}
  <AdditionalClaims>
    {% for c in params.claims %}
    <Claim name="{{ c.name }}">{{ c.value }}</Claim>
    {% endfor %}
  </AdditionalClaims>
  {% endif %}
  {% if params.expires_in is defined %}<ExpiresIn>{{ params.expires_in }}</ExpiresIn>{% endif %}
  {% if params.output is defined %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
</GenerateJWT>