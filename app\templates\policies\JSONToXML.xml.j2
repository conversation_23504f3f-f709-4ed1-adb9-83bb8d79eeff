<JSONToXML name="{{ name }}">
  {% if params.input %}<InputVariable>{{ params.input }}</InputVariable>{% endif %}
  {% if params.output %}<OutputVariable>{{ params.output }}</OutputVariable>{% endif %}
  {% if params.options %}
  <Options>
    {% if params.options.object_root %}<ObjectRootElementName>{{ params.options.object_root }}</ObjectRootElementName>{% endif %}
    {% if params.options.array_root %}<ArrayRootElementName>{{ params.options.array_root }}</ArrayRootElementName>{% endif %}
    {% if params.options.text_node_name %}<TextNodeName>{{ params.options.text_node_name }}</TextNodeName>{% endif %}
    {% if params.options.attribute_prefix %}<AttributePrefix>{{ params.options.attribute_prefix }}</AttributePrefix>{% endif %}
  </Options>
  {% endif %}
</JSONToXML>