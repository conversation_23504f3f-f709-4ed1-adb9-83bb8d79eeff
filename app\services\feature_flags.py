"""
Enhanced Feature Flag System
===========================
Comprehensive feature flag support with hierarchical flags, environment-specific flags,
and runtime evaluation for the Apigee proxy framework.
"""

import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import structlog
import yaml

from ..core.settings import settings

log = structlog.get_logger(__name__)


class FeatureFlagEvaluator:
    """Advanced feature flag evaluation with hierarchical support and conditions"""
    
    def __init__(self, env_name: str = "test", system_name: Optional[str] = None):
        self.env_name = env_name
        self.system_name = system_name
        self._flags_cache = {}
        self._load_feature_flags()
    
    def _load_feature_flags(self):
        """Load feature flags from multiple sources with precedence"""
        self._flags_cache = {}
        
        # 1. Load global feature flags
        global_flags = self._load_global_flags()
        
        # 2. Load environment-specific flags
        env_flags = self._load_environment_flags()
        
        # 3. Load system-specific flags
        system_flags = self._load_system_flags()
        
        # Merge with precedence: Global < Environment < System
        self._flags_cache = {**global_flags, **env_flags, **system_flags}
        
        log.info("feature_flags_loaded", 
                env=self.env_name, 
                system=self.system_name,
                total_flags=len(self._flags_cache))
    
    def _load_global_flags(self) -> Dict[str, Any]:
        """Load global feature flags"""
        try:
            # Use the app config directory
            config_dir = Path(__file__).parent.parent / "config"
            flags_path = config_dir / "feature_flags.yaml"
            if flags_path.exists():
                with open(flags_path, 'r') as f:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            log.warning("failed_to_load_global_flags", error=str(e))
        return {}
    
    def _load_environment_flags(self) -> Dict[str, Any]:
        """Load environment-specific feature flags"""
        try:
            config_dir = Path(__file__).parent.parent / "config"
            env_flags_path = config_dir / "env" / self.env_name / "feature_flags.yaml"
            if env_flags_path.exists():
                with open(env_flags_path, 'r') as f:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            log.warning("failed_to_load_env_flags", env=self.env_name, error=str(e))
        return {}
    
    def _load_system_flags(self) -> Dict[str, Any]:
        """Load system-specific feature flags"""
        if not self.system_name:
            return {}

        try:
            config_dir = Path(__file__).parent.parent / "config"
            system_flags_path = config_dir / "systems" / f"{self.system_name}_features.yaml"
            if system_flags_path.exists():
                with open(system_flags_path, 'r') as f:
                    return yaml.safe_load(f) or {}
        except Exception as e:
            log.warning("failed_to_load_system_flags", system=self.system_name, error=str(e))
        return {}
    
    def is_enabled(self, flag_name: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Check if a feature flag is enabled with advanced evaluation.
        
        Args:
            flag_name: Name of the feature flag
            context: Additional context for conditional evaluation
            
        Returns:
            True if the feature is enabled, False otherwise
        """
        context = context or {}
        
        # Check if flag exists
        if flag_name not in self._flags_cache:
            log.debug("feature_flag_not_found", flag=flag_name)
            return False
        
        flag_config = self._flags_cache[flag_name]
        
        # Simple boolean flag
        if isinstance(flag_config, bool):
            return flag_config
        
        # Complex flag with conditions
        if isinstance(flag_config, dict):
            return self._evaluate_complex_flag(flag_name, flag_config, context)
        
        # Default to False for unknown types
        return False
    
    def _evaluate_complex_flag(self, flag_name: str, flag_config: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Evaluate complex feature flag with conditions"""
        
        # Check if flag is globally disabled
        if flag_config.get('enabled', True) is False:
            return False
        
        # Environment-specific evaluation
        env_config = flag_config.get('environments', {})
        if env_config:
            env_enabled = env_config.get(self.env_name)
            if env_enabled is not None:
                if not env_enabled:
                    return False
        
        # Percentage rollout
        percentage = flag_config.get('percentage')
        if percentage is not None:
            # Simple hash-based percentage (in real implementation, use proper user ID)
            hash_value = hash(f"{flag_name}:{self.env_name}:{self.system_name}") % 100
            if hash_value >= percentage:
                return False
        
        # Conditional evaluation
        conditions = flag_config.get('conditions', [])
        if conditions:
            return self._evaluate_conditions(conditions, context)
        
        # Default to enabled if no conditions block it
        return flag_config.get('default', True)
    
    def _evaluate_conditions(self, conditions: List[Dict[str, Any]], context: Dict[str, Any]) -> bool:
        """Evaluate a list of conditions (AND logic)"""
        for condition in conditions:
            if not self._evaluate_single_condition(condition, context):
                return False
        return True
    
    def _evaluate_single_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """Evaluate a single condition"""
        condition_type = condition.get('type')
        
        if condition_type == 'environment':
            required_envs = condition.get('values', [])
            return self.env_name in required_envs
        
        elif condition_type == 'system':
            required_systems = condition.get('values', [])
            return self.system_name in required_systems
        
        elif condition_type == 'context':
            key = condition.get('key')
            expected_value = condition.get('value')
            operator = condition.get('operator', 'equals')
            
            if key not in context:
                return False
            
            actual_value = context[key]
            
            if operator == 'equals':
                return actual_value == expected_value
            elif operator == 'in':
                return actual_value in expected_value
            elif operator == 'greater_than':
                return actual_value > expected_value
            elif operator == 'less_than':
                return actual_value < expected_value
        
        elif condition_type == 'api_characteristic':
            # Check API characteristics like method count, security type, etc.
            characteristic = condition.get('characteristic')
            operator = condition.get('operator', 'equals')
            expected_value = condition.get('value')
            
            if characteristic == 'method_count':
                method_count = len(context.get('methods', []))
                if operator == 'greater_than':
                    return method_count > expected_value
                elif operator == 'less_than':
                    return method_count < expected_value
                else:
                    return method_count == expected_value
            
            elif characteristic == 'has_security':
                return bool(context.get('security_policies'))
        
        return False
    
    def get_flag_value(self, flag_name: str, default: Any = None, context: Optional[Dict[str, Any]] = None) -> Any:
        """Get the value of a feature flag (not just boolean)"""
        context = context or {}
        
        if flag_name not in self._flags_cache:
            return default
        
        flag_config = self._flags_cache[flag_name]
        
        # Simple value
        if not isinstance(flag_config, dict):
            return flag_config
        
        # Complex flag - check if enabled first
        if not self._evaluate_complex_flag(flag_name, flag_config, context):
            return default
        
        # Return the configured value
        return flag_config.get('value', flag_config.get('default', default))
    
    def get_enabled_flags(self, context: Optional[Dict[str, Any]] = None) -> List[str]:
        """Get list of all enabled feature flags"""
        context = context or {}
        enabled_flags = []
        
        for flag_name in self._flags_cache:
            if self.is_enabled(flag_name, context):
                enabled_flags.append(flag_name)
        
        return enabled_flags
    
    def reload_flags(self):
        """Reload feature flags from configuration files"""
        self._load_feature_flags()


# Global feature flag evaluator instance
_global_evaluator: Optional[FeatureFlagEvaluator] = None


def get_feature_evaluator(env_name: str = "test", system_name: Optional[str] = None) -> FeatureFlagEvaluator:
    """Get or create a feature flag evaluator"""
    global _global_evaluator
    
    # Create new evaluator if needed or if context changed
    if (_global_evaluator is None or 
        _global_evaluator.env_name != env_name or 
        _global_evaluator.system_name != system_name):
        _global_evaluator = FeatureFlagEvaluator(env_name, system_name)
    
    return _global_evaluator


def is_feature_enabled(flag_name: str, env_name: str = "test", 
                      system_name: Optional[str] = None, 
                      context: Optional[Dict[str, Any]] = None) -> bool:
    """Convenience function to check if a feature is enabled"""
    evaluator = get_feature_evaluator(env_name, system_name)
    return evaluator.is_enabled(flag_name, context)
